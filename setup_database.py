#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد قاعدة البيانات
Database Setup Script

ينشئ قاعدة البيانات المحلية مع البيانات الأولية للاختبار
"""

import os
import sys
import sqlite3
import bcrypt
from pathlib import Path

def create_database():
    """إنشاء قاعدة البيانات المحلية"""
    
    # إنشاء مجلد البيانات
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    db_path = data_dir / "iots_local.db"
    
    # حذف قاعدة البيانات الموجودة إن وجدت
    if db_path.exists():
        db_path.unlink()
        print("تم حذف قاعدة البيانات القديمة")
    
    # إنشاء قاعدة البيانات الجديدة
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    print("إنشاء قاعدة البيانات المحلية...")
    
    # قراءة وتنفيذ سكريبت SQLite
    schema_file = Path("src/database/sqlite_schema.sql")
    if schema_file.exists():
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # تنفيذ السكريبت
        cursor.executescript(schema_sql)
        print("تم إنشاء جداول قاعدة البيانات")
    else:
        print("خطأ: ملف السكريبت غير موجود")
        return False
    
    # إضافة بيانات اختبار إضافية
    add_test_data(cursor)
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"تم إنشاء قاعدة البيانات بنجاح: {db_path}")
    return True

def add_test_data(cursor):
    """إضافة بيانات اختبار"""
    
    print("إضافة بيانات الاختبار...")
    
    # تشفير كلمة مرور المدير
    admin_password = "admin"
    hashed_password = bcrypt.hashpw(admin_password.encode('utf-8'), bcrypt.gensalt())
    
    # تحديث كلمة مرور المدير
    cursor.execute("""
        UPDATE users 
        SET user_pass = ? 
        WHERE user_name = 'admin'
    """, (hashed_password.decode('utf-8'),))
    
    # إضافة مستخدم اختبار
    test_password = bcrypt.hashpw("test123".encode('utf-8'), bcrypt.gensalt())
    cursor.execute("""
        INSERT OR IGNORE INTO users (user_name, user_pass, full_name, email, permission, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
    """, ("testuser", test_password.decode('utf-8'), "مستخدم تجريبي", "<EMAIL>", "user", 1))
    
    # إضافة بعض المعاملات التجريبية
    sample_transactions = [
        ("2024/001", "2024-01-15", "طلب تأشيرة سياحة", "تم استلام الطلب وقيد المراجعة", 1, 1, None, 1, 1, 1, 1, "medium", "2024-01-25", None, 1, None),
        ("2024/002", "2024-01-16", "طلب تأشيرة عمل", "طلب معلومات إضافية من المتقدم", 1, 1, None, 2, 2, 2, 2, "high", "2024-01-30", None, 1, None),
        ("2024/003", "2024-01-17", "طلب تأشيرة دراسة", "تم إنجاز المعاملة وإرسال الرد", 1, 1, None, 3, 3, 6, 4, "low", "2024-02-01", "2024-01-20", 1, None),
    ]
    
    for transaction in sample_transactions:
        cursor.execute("""
            INSERT OR IGNORE INTO transactions (
                head_incoming_no, head_incoming_date, subject, researcher_notes,
                user_id, researcher_1_id, researcher_2_id, visa_type_id, received_from_id,
                action_taken_id, request_status_id, priority, due_date, completion_date,
                created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, transaction)
    
    print("تم إضافة بيانات الاختبار")

def test_database():
    """اختبار قاعدة البيانات"""
    
    print("\nاختبار قاعدة البيانات...")
    
    db_path = Path("data/iots_local.db")
    if not db_path.exists():
        print("خطأ: قاعدة البيانات غير موجودة")
        return False
    
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # اختبار جدول المستخدمين
    cursor.execute("SELECT COUNT(*) FROM users")
    users_count = cursor.fetchone()[0]
    print(f"عدد المستخدمين: {users_count}")
    
    # اختبار جدول المعاملات
    cursor.execute("SELECT COUNT(*) FROM transactions")
    transactions_count = cursor.fetchone()[0]
    print(f"عدد المعاملات: {transactions_count}")
    
    # اختبار الجداول المساعدة
    tables = ['visa_types', 'received_from_sources', 'actions_taken', 'request_statuses']
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"عدد سجلات {table}: {count}")
    
    # اختبار المستخدم الافتراضي
    cursor.execute("SELECT user_name, full_name, permission FROM users WHERE user_name = 'admin'")
    admin_user = cursor.fetchone()
    if admin_user:
        print(f"المستخدم الافتراضي: {admin_user[1]} ({admin_user[0]}) - {admin_user[2]}")
    else:
        print("خطأ: المستخدم الافتراضي غير موجود")
    
    conn.close()
    print("اختبار قاعدة البيانات مكتمل")
    return True

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("سكريبت إعداد قاعدة البيانات")
    print("=" * 50)
    
    try:
        # إنشاء قاعدة البيانات
        if create_database():
            # اختبار قاعدة البيانات
            test_database()
            
            print("\n" + "=" * 50)
            print("تم إعداد قاعدة البيانات بنجاح!")
            print("يمكنك الآن تشغيل التطبيق باستخدام:")
            print("python main.py")
            print("\nبيانات تسجيل الدخول:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin")
            print("=" * 50)
            
        else:
            print("فشل في إعداد قاعدة البيانات")
            return 1
            
    except Exception as e:
        print(f"خطأ: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
