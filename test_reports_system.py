#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التقارير والإحصائيات المتقدمة
Advanced Reports System Test

يختبر جميع وظائف التقارير والتصدير
"""

import sys
import os
from datetime import datetime, date, timedelta
from pathlib import Path

# إضافة مسار src
sys.path.insert(0, 'src')

def test_reports_service():
    """اختبار خدمة التقارير"""
    
    print("اختبار خدمة التقارير...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.reports_service import ReportsService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        reports_service = ReportsService(conn_manager)
        
        # اختبار أنواع التقارير المتاحة
        report_types = reports_service.get_available_report_types()
        print(f"✅ أنواع التقارير المتاحة: {len(report_types)} نوع")
        
        for report_type in report_types:
            print(f"  - {report_type['icon']} {report_type['name']}: {report_type['description']}")
        
        # اختبار تقرير المعاملات
        print("\n--- اختبار تقرير المعاملات ---")
        transactions_report = reports_service.generate_transactions_report()
        print(f"✅ تقرير المعاملات: {transactions_report['total_count']} معاملة")
        print(f"  - الإحصائيات: {len(transactions_report['statistics'])} إحصائية")
        print(f"  - التجميعات: {len(transactions_report['groupings'])} تجميع")
        
        # اختبار تقرير الأداء العام
        print("\n--- اختبار تقرير الأداء ---")
        performance_report = reports_service.generate_performance_report(period='month')
        print(f"✅ تقرير الأداء: {len(performance_report['users_performance'])} مستخدم")
        if performance_report['users_performance']:
            top_user = performance_report['users_performance'][0]
            print(f"  - أفضل أداء: {top_user['user']['full_name']} ({top_user['completion_rate']}%)")
        
        # اختبار تقرير الحالات
        print("\n--- اختبار تقرير الحالات ---")
        status_report = reports_service.generate_status_report()
        print(f"✅ تقرير الحالات: {len(status_report['status_statistics'])} حالة")
        print(f"  - إجمالي المعاملات: {status_report['total_transactions']}")
        print(f"  - معدل الإنجاز: {status_report['summary']['completion_rate']}%")
        
        # اختبار تقرير أنواع التأشيرات
        print("\n--- اختبار تقرير أنواع التأشيرات ---")
        visa_report = reports_service.generate_visa_types_report()
        print(f"✅ تقرير أنواع التأشيرات: {len(visa_report['visa_statistics'])} نوع")
        if visa_report['visa_statistics']:
            most_requested = visa_report['visa_statistics'][0]
            print(f"  - الأكثر طلباً: {most_requested['visa_type']} ({most_requested['count']} معاملة)")
        
        # اختبار تقرير الملخص الشهري
        print("\n--- اختبار تقرير الملخص الشهري ---")
        monthly_report = reports_service.generate_monthly_summary_report()
        print(f"✅ تقرير الملخص الشهري: {len(monthly_report['monthly_data'])} شهر")
        yearly_summary = monthly_report['yearly_summary']
        print(f"  - إجمالي المعاملات السنوية: {yearly_summary['total_transactions']}")
        print(f"  - معدل الإنجاز السنوي: {yearly_summary['overall_completion_rate']}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة التقارير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_export_service():
    """اختبار خدمة التصدير"""
    
    print("\nاختبار خدمة التصدير...")
    
    try:
        from services.export_service import ExportService
        
        # إنشاء خدمة التصدير
        export_service = ExportService()
        
        # اختبار صيغ التصدير المتاحة
        export_formats = export_service.get_export_formats()
        print(f"✅ صيغ التصدير المتاحة: {len(export_formats)} صيغة")
        
        for fmt in export_formats:
            print(f"  - {fmt['icon']} {fmt['name']}: {fmt['description']}")
        
        # إنشاء بيانات تقرير تجريبي
        test_report_data = {
            'title': 'تقرير اختبار',
            'generated_at': datetime.now().isoformat(),
            'statistics': {
                'total': 100,
                'completed': 75,
                'pending': 20,
                'overdue': 5,
                'completion_rate': 75.0,
                'avg_processing_days': 3.5
            },
            'summary': {
                'most_common_status': 'مكتمل',
                'total_transactions': 100,
                'overall_completion_rate': 75.0
            }
        }
        
        # اختبار تصدير JSON (متاح دائماً)
        print("\n--- اختبار تصدير JSON ---")
        json_file = export_service.export_to_json(test_report_data, "test_report")
        if os.path.exists(json_file):
            print(f"✅ تصدير JSON: {json_file}")
            file_size = os.path.getsize(json_file)
            print(f"  - حجم الملف: {file_size} بايت")
        else:
            print("❌ فشل في تصدير JSON")
        
        # اختبار تصدير Excel (إذا كان متاحاً)
        try:
            print("\n--- اختبار تصدير Excel ---")
            excel_file = export_service.export_to_excel(test_report_data, "test_report")
            if os.path.exists(excel_file):
                print(f"✅ تصدير Excel: {excel_file}")
                file_size = os.path.getsize(excel_file)
                print(f"  - حجم الملف: {file_size} بايت")
            else:
                print("❌ فشل في تصدير Excel")
        except ImportError:
            print("⚠️ مكتبة openpyxl غير متوفرة - تخطي اختبار Excel")
        except Exception as e:
            print(f"❌ خطأ في تصدير Excel: {str(e)}")
        
        # اختبار تصدير PDF (إذا كان متاحاً)
        try:
            print("\n--- اختبار تصدير PDF ---")
            pdf_file = export_service.export_to_pdf(test_report_data, "test_report")
            if os.path.exists(pdf_file):
                print(f"✅ تصدير PDF: {pdf_file}")
                file_size = os.path.getsize(pdf_file)
                print(f"  - حجم الملف: {file_size} بايت")
            else:
                print("❌ فشل في تصدير PDF")
        except ImportError:
            print("⚠️ مكتبة reportlab غير متوفرة - تخطي اختبار PDF")
        except Exception as e:
            print(f"❌ خطأ في تصدير PDF: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_reports_widget():
    """اختبار واجهة التقارير"""
    
    print("\nاختبار واجهة التقارير...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        from ui.reports_widget import ReportsWidget, ReportParametersWidget, ReportPreviewWidget
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار ويدجت التقارير الرئيسي
        reports_widget = ReportsWidget(conn_manager, auth_service)
        print("✅ إنشاء ويدجت التقارير الرئيسي: نجح")
        
        # اختبار ويدجت معايير التقرير
        for report_type in ['transactions', 'performance', 'status', 'visa_types', 'monthly_summary']:
            try:
                params_widget = ReportParametersWidget(report_type, conn_manager)
                print(f"✅ إنشاء ويدجت معايير {report_type}: نجح")
            except Exception as e:
                print(f"❌ خطأ في إنشاء ويدجت معايير {report_type}: {str(e)}")
        
        # اختبار ويدجت معاينة التقرير
        preview_widget = ReportPreviewWidget()
        print("✅ إنشاء ويدجت معاينة التقرير: نجح")
        
        # اختبار عرض تقرير تجريبي
        test_report_data = {
            'title': 'تقرير تجريبي',
            'generated_at': datetime.now().isoformat(),
            'statistics': {
                'total': 50,
                'completed': 40,
                'pending': 8,
                'overdue': 2,
                'completion_rate': 80.0
            },
            'summary': {
                'most_common_status': 'مكتمل',
                'total_transactions': 50
            }
        }
        
        preview_widget.display_report(test_report_data)
        print("✅ عرض تقرير تجريبي في المعاينة: نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التقارير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_generation_performance():
    """اختبار أداء إنشاء التقارير"""
    
    print("\nاختبار أداء إنشاء التقارير...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.reports_service import ReportsService
        import time
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        reports_service = ReportsService(conn_manager)
        
        # اختبار أداء التقارير المختلفة
        performance_tests = [
            ('تقرير المعاملات', lambda: reports_service.generate_transactions_report()),
            ('تقرير الأداء', lambda: reports_service.generate_performance_report()),
            ('تقرير الحالات', lambda: reports_service.generate_status_report()),
            ('تقرير أنواع التأشيرات', lambda: reports_service.generate_visa_types_report()),
            ('تقرير الملخص الشهري', lambda: reports_service.generate_monthly_summary_report())
        ]
        
        total_time = 0
        successful_tests = 0
        
        for test_name, test_func in performance_tests:
            try:
                start_time = time.time()
                report_data = test_func()
                end_time = time.time()
                
                generation_time = end_time - start_time
                total_time += generation_time
                successful_tests += 1
                
                # حساب حجم البيانات
                data_size = len(str(report_data))
                
                print(f"✅ {test_name}: {generation_time:.3f} ثانية ({data_size} حرف)")
                
            except Exception as e:
                print(f"❌ فشل {test_name}: {str(e)}")
        
        if successful_tests > 0:
            avg_time = total_time / successful_tests
            print(f"\n✅ متوسط وقت إنشاء التقرير: {avg_time:.3f} ثانية")
            
            # تقييم الأداء
            if avg_time < 0.5:
                print("🚀 أداء ممتاز (< 0.5 ثانية)")
            elif avg_time < 1.0:
                print("✅ أداء جيد (< 1.0 ثانية)")
            elif avg_time < 2.0:
                print("⚠️ أداء مقبول (< 2.0 ثانية)")
            else:
                print("🐌 أداء بطيء (> 2.0 ثانية)")
        
        return successful_tests == len(performance_tests)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {str(e)}")
        return False

def test_export_formats_compatibility():
    """اختبار توافق صيغ التصدير"""
    
    print("\nاختبار توافق صيغ التصدير...")
    
    try:
        # اختبار توفر المكتبات
        libraries_status = {}
        
        # اختبار openpyxl للـ Excel
        try:
            import openpyxl
            libraries_status['openpyxl'] = True
            print("✅ مكتبة openpyxl متوفرة - تصدير Excel مدعوم")
        except ImportError:
            libraries_status['openpyxl'] = False
            print("❌ مكتبة openpyxl غير متوفرة - تصدير Excel غير مدعوم")
        
        # اختبار reportlab للـ PDF
        try:
            import reportlab
            libraries_status['reportlab'] = True
            print("✅ مكتبة reportlab متوفرة - تصدير PDF مدعوم")
        except ImportError:
            libraries_status['reportlab'] = False
            print("❌ مكتبة reportlab غير متوفرة - تصدير PDF غير مدعوم")
        
        # JSON متاح دائماً
        libraries_status['json'] = True
        print("✅ تصدير JSON مدعوم (مدمج)")
        
        # إحصائيات التوافق
        supported_formats = sum(libraries_status.values())
        total_formats = len(libraries_status)
        compatibility_rate = (supported_formats / total_formats) * 100
        
        print(f"\n📊 معدل التوافق: {compatibility_rate:.1f}% ({supported_formats}/{total_formats})")
        
        if compatibility_rate == 100:
            print("🎉 جميع صيغ التصدير مدعومة!")
        elif compatibility_rate >= 66:
            print("✅ معظم صيغ التصدير مدعومة")
        else:
            print("⚠️ بعض صيغ التصدير غير مدعومة")
            print("\nلتثبيت المكتبات المفقودة:")
            if not libraries_status['openpyxl']:
                print("  pip install openpyxl")
            if not libraries_status['reportlab']:
                print("  pip install reportlab")
        
        return compatibility_rate >= 33  # على الأقل JSON يجب أن يعمل
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوافق: {str(e)}")
        return False

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    try:
        exports_dir = Path("exports")
        if exports_dir.exists():
            test_files = list(exports_dir.glob("test_report*"))
            for file_path in test_files:
                try:
                    file_path.unlink()
                    print(f"🗑️ تم حذف ملف الاختبار: {file_path.name}")
                except:
                    pass
    except:
        pass

def run_reports_system_tests():
    """تشغيل جميع اختبارات نظام التقارير"""
    
    print("=" * 60)
    print("اختبار نظام التقارير والإحصائيات المتقدمة")
    print("=" * 60)
    
    tests = [
        ("خدمة التقارير", test_reports_service),
        ("خدمة التصدير", test_export_service),
        ("واجهة التقارير", test_reports_widget),
        ("أداء إنشاء التقارير", test_report_generation_performance),
        ("توافق صيغ التصدير", test_export_formats_compatibility),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج اختبار نظام التقارير: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات نظام التقارير نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ 5 أنواع تقارير متقدمة")
        print("- ✅ تصدير متعدد الصيغ (JSON, Excel, PDF)")
        print("- ✅ واجهات تفاعلية متطورة")
        print("- ✅ معاينة التقارير المباشرة")
        print("- ✅ أداء سريع ومحسن")
        print("- ✅ إحصائيات شاملة ومفصلة")
        return True
    else:
        print("⚠️ بعض اختبارات نظام التقارير فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_reports_system_tests()
        
        # تنظيف ملفات الاختبار
        cleanup_test_files()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 نظام التقارير والإحصائيات جاهز للاستخدام!")
            print("\nلتجربة النظام:")
            print("1. شغل التطبيق: python main.py")
            print("2. سجل الدخول: admin / admin")
            print("3. انتقل إلى 'التقارير والإحصائيات'")
            print("4. اختر نوع التقرير المطلوب")
            print("5. حدد المعايير واضغط 'إنشاء التقرير'")
            print("6. اعرض المعاينة وصدر التقرير بالصيغة المطلوبة")
            print(f"{'=' * 60}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
