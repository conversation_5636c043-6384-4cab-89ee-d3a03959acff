# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window

واجهة تسجيل الدخول للتطبيق
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QCheckBox, 
                            QMessageBox, QFrame, QProgressBar, QSpacerItem,
                            QSizePolicy)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

from services.auth_service import AuthService
from utils.logger import Logger

class LoginThread(QThread):
    """خيط تسجيل الدخول لتجنب تجميد الواجهة"""
    
    login_result = pyqtSignal(dict)
    
    def __init__(self, auth_service, username, password):
        super().__init__()
        self.auth_service = auth_service
        self.username = username
        self.password = password
    
    def run(self):
        """تنفيذ عملية تسجيل الدخول"""
        result = self.auth_service.login(self.username, self.password)
        self.login_result.emit(result)

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    def __init__(self, config_manager, connection_manager, parent=None):
        super().__init__(parent)
        
        self.config = config_manager
        self.connection_manager = connection_manager
        self.auth_service = AuthService(connection_manager, config_manager)
        self.logger = Logger(__name__)
        
        self.login_thread = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_styles()
        
        # تحديد حالة الاتصال
        self.update_connection_status()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - " + self.config.app_name)
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        # تعيين اتجاه النافذة
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # شعار التطبيق
        self.setup_logo(main_layout)
        
        # عنوان التطبيق
        self.setup_title(main_layout)
        
        # حالة الاتصال
        self.setup_connection_status(main_layout)
        
        # نموذج تسجيل الدخول
        self.setup_login_form(main_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # أزرار التحكم
        self.setup_buttons(main_layout)
        
        # مساحة فارغة
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, 
                                       QSizePolicy.Policy.Expanding))
    
    def setup_logo(self, layout):
        """إعداد شعار التطبيق"""
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # محاولة تحميل الشعار
        try:
            pixmap = QPixmap("assets/images/logo.png")
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio,
                                            Qt.TransformationMode.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                # شعار نصي بديل
                logo_label.setText("📋")
                logo_label.setStyleSheet("font-size: 48px;")
        except:
            # شعار نصي بديل
            logo_label.setText("📋")
            logo_label.setStyleSheet("font-size: 48px;")
        
        layout.addWidget(logo_label)
    
    def setup_title(self, layout):
        """إعداد عنوان التطبيق"""
        title_label = QLabel(self.config.app_name)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px 0;
            }
        """)
        layout.addWidget(title_label)
        
        subtitle_label = QLabel("نظام متكامل لمتابعة المراسلات والمعاملات")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(subtitle_label)
    
    def setup_connection_status(self, layout):
        """إعداد عرض حالة الاتصال"""
        self.connection_frame = QFrame()
        self.connection_frame.setFrameStyle(QFrame.Shape.Box)
        self.connection_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                margin: 5px 0;
            }
        """)
        
        connection_layout = QHBoxLayout(self.connection_frame)
        
        self.connection_icon = QLabel("🔗")
        self.connection_label = QLabel("جاري التحقق من الاتصال...")
        
        connection_layout.addWidget(self.connection_icon)
        connection_layout.addWidget(self.connection_label)
        connection_layout.addStretch()
        
        layout.addWidget(self.connection_frame)
    
    def setup_login_form(self, layout):
        """إعداد نموذج تسجيل الدخول"""
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.Shape.Box)
        form_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
                background-color: #f8f9fa;
            }
        """)

        # استخدام VBox بدلاً من FormLayout لضمان الظهور دائماً
        container = QVBoxLayout(form_frame)
        container.setSpacing(12)

        # اسم المستخدم
        user_label = QLabel("اسم المستخدم")
        user_label.setStyleSheet("font-size: 13px; color: #2c3e50;")
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setMinimumHeight(36)
        self.username_edit.setClearButtonEnabled(True)
        self.username_edit.setStyleSheet(self.get_input_style())
        container.addWidget(user_label)
        container.addWidget(self.username_edit)

        # كلمة المرور
        pass_label = QLabel("كلمة المرور")
        pass_label.setStyleSheet("font-size: 13px; color: #2c3e50;")
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setMinimumHeight(36)
        self.password_edit.setClearButtonEnabled(True)
        self.password_edit.setStyleSheet(self.get_input_style())
        container.addWidget(pass_label)
        container.addWidget(self.password_edit)

        # تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
        """)
        container.addWidget(self.remember_checkbox)

        layout.addWidget(form_frame)
    
    def setup_buttons(self, layout):
        """إعداد أزرار التحكم"""
        button_layout = QHBoxLayout()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet(self.get_button_style("#3498db"))
        self.login_button.setMinimumHeight(40)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style("#95a5a6"))
        self.cancel_button.setMinimumHeight(40)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        
        # تسجيل الدخول بالضغط على Enter
        self.username_edit.returnPressed.connect(self.handle_login)
        self.password_edit.returnPressed.connect(self.handle_login)
        
        # تحديث حالة الاتصال كل 30 ثانية
        self.connection_timer = QTimer()
        self.connection_timer.timeout.connect(self.update_connection_status)
        self.connection_timer.start(30000)  # 30 ثانية
    
    def setup_styles(self):
        """إعداد الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
    
    def get_input_style(self):
        """نمط حقول الإدخال"""
        return """
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """
    
    def get_button_style(self, color):
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للون
        if color == "#3498db":
            return "#2980b9" if factor == 0.9 else "#1f618d"
        elif color == "#95a5a6":
            return "#7f8c8d" if factor == 0.9 else "#5d6d7e"
        return color
    
    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        try:
            # نحاول اختبار الاتصال الحالي (سواء MySQL أو SQLite)
            connection_ok = False
            try:
                if hasattr(self.connection_manager, 'test_connection'):
                    connection_ok = bool(self.connection_manager.test_connection())
            except Exception:
                connection_ok = False

            if getattr(self.connection_manager, 'is_online', False):
                # متصل بـ MySQL
                self.connection_icon.setText("🟢")
                self.connection_label.setText("متصل بالخادم الرئيسي")
                self.connection_frame.setStyleSheet("""
                    QFrame {
                        border: 1px solid #27ae60;
                        border-radius: 5px;
                        padding: 5px;
                        margin: 5px 0;
                        background-color: #d5f4e6;
                    }
                """)
            elif connection_ok:
                # متصل محلياً بـ SQLite (وضع آمن ومتاح)
                self.connection_icon.setText("🟢")
                self.connection_label.setText("النمط المحلي - متصل (SQLite)")
                self.connection_frame.setStyleSheet("""
                    QFrame {
                        border: 1px solid #3498db;
                        border-radius: 5px;
                        padding: 5px;
                        margin: 5px 0;
                        background-color: #eaf2fb;
                    }
                """)
            else:
                # غير متصل بأي قاعدة
                self.connection_icon.setText("🔴")
                self.connection_label.setText("غير متصل بأي قاعدة بيانات")
                self.connection_frame.setStyleSheet("""
                    QFrame {
                        border: 1px solid #e74c3c;
                        border-radius: 5px;
                        padding: 5px;
                        margin: 5px 0;
                        background-color: #fadbd8;
                    }
                """)
        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة الاتصال: {str(e)}")
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # التحقق من صحة البيانات
        if not username or not password:
            self.show_message("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور", QMessageBox.Icon.Warning)
            return
        
        # تعطيل الواجهة أثناء تسجيل الدخول
        self.set_ui_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        
        # بدء خيط تسجيل الدخول
        self.login_thread = LoginThread(self.auth_service, username, password)
        self.login_thread.login_result.connect(self.handle_login_result)
        self.login_thread.start()
    
    def handle_login_result(self, result):
        """معالجة نتيجة تسجيل الدخول"""
        # إعادة تفعيل الواجهة
        self.set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        
        if result['success']:
            self.logger.info("تم تسجيل الدخول بنجاح")
            self.accept()  # إغلاق النافذة بنجاح
        else:
            self.show_message("خطأ في تسجيل الدخول", result['message'], QMessageBox.Icon.Critical)
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    def set_ui_enabled(self, enabled):
        """تفعيل/تعطيل عناصر الواجهة"""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
    
    def show_message(self, title, message, icon=QMessageBox.Icon.Information):
        """عرض رسالة للمستخدم"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        msg_box.exec()
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if self.login_thread and self.login_thread.isRunning():
            self.login_thread.terminate()
            self.login_thread.wait()
        
        if hasattr(self, 'connection_timer'):
            self.connection_timer.stop()
        
        event.accept()
