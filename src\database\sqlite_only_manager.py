# -*- coding: utf-8 -*-
"""
مدير الاتصال بـ SQLite فقط
SQLite Only Connection Manager

مدير اتصال مبسط يعمل مع SQLite فقط للتشغيل السريع
"""

import os
import sqlite3
import logging
from typing import Optional, Dict, List, Any
from contextlib import contextmanager

class SQLiteOnlyManager:
    """مدير الاتصال بـ SQLite فقط"""
    
    def __init__(self, config_manager):
        """
        تهيئة مدير الاتصال
        
        Args:
            config_manager: مدير التكوين
        """
        self.config = config_manager
        self.current_mode = 'sqlite'
        
        # التأكد من وجود قاعدة البيانات
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات"""
        try:
            sqlite_path = self.config.sqlite_database
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(sqlite_path), exist_ok=True)
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not os.path.exists(sqlite_path):
                logging.info("إنشاء قاعدة البيانات المحلية...")
                self._create_basic_database()
            
        except Exception as e:
            logging.error(f"خطأ في التأكد من وجود قاعدة البيانات: {str(e)}")
    
    def _create_basic_database(self):
        """إنشاء قاعدة بيانات أساسية"""
        try:
            conn = sqlite3.connect(self.config.sqlite_database)
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT NOT NULL UNIQUE,
                    user_pass TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    permission TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT (datetime('now', 'localtime'))
                )
            """)
            
            # إنشاء جدول المعاملات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transactions (
                    transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    head_incoming_no TEXT,
                    head_incoming_date TEXT,
                    subject TEXT NOT NULL,
                    status TEXT DEFAULT 'new',
                    created_at TEXT DEFAULT (datetime('now', 'localtime'))
                )
            """)
            
            conn.commit()
            conn.close()
            
            logging.info("تم إنشاء قاعدة البيانات الأساسية")
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء قاعدة البيانات الأساسية: {str(e)}")
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.config.sqlite_database)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logging.error(f"خطأ في اختبار الاتصال: {str(e)}")
            return False
    
    @contextmanager
    def get_connection(self):
        """
        الحصول على اتصال قاعدة البيانات
        
        Yields:
            اتصال قاعدة البيانات
        """
        connection = None
        try:
            connection = sqlite3.connect(self.config.sqlite_database)
            connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            yield connection
            
        except Exception as e:
            logging.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
            raise
            
        finally:
            if connection:
                try:
                    connection.close()
                except:
                    pass
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """
        تنفيذ استعلام وإرجاع النتائج
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            قائمة النتائج
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                results = [dict(row) for row in cursor.fetchall()]
                cursor.close()
                
                return results
                
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return []
    
    def execute_non_query(self, query: str, params: tuple = None) -> bool:
        """
        تنفيذ استعلام بدون إرجاع نتائج
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            True إذا نجح التنفيذ
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                conn.commit()
                cursor.close()
                
                return True
                
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return False
    
    def get_last_insert_id(self) -> Optional[int]:
        """الحصول على آخر معرف تم إدراجه"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT last_insert_rowid()")
                result = cursor.fetchone()
                cursor.close()
                return result[0] if result else None
                
        except Exception as e:
            logging.error(f"خطأ في الحصول على آخر معرف: {str(e)}")
            return None
    
    def switch_to_mysql(self) -> bool:
        """التبديل إلى نمط MySQL - غير مدعوم في هذا المدير"""
        return False
    
    def switch_to_sqlite(self) -> bool:
        """التبديل إلى نمط SQLite - مدعوم دائماً"""
        return True
    
    @property
    def is_online(self) -> bool:
        """هل التطبيق في النمط المتصل؟"""
        return False  # دائماً محلي
    
    @property
    def is_offline(self) -> bool:
        """هل التطبيق في النمط المحلي؟"""
        return True  # دائماً محلي
