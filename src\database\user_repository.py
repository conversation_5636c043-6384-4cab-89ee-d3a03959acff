# -*- coding: utf-8 -*-
"""
مستودع المستخدمين
User Repository

يدير عمليات قاعدة البيانات الخاصة بالمستخدمين
"""

from typing import List, Dict, Optional
import hashlib
from datetime import datetime, timedelta

from database.base_repository import BaseRepository

class UserRepository(BaseRepository):
    """مستودع المستخدمين"""
    
    def get_table_name(self) -> str:
        """إرجاع اسم جدول المستخدمين"""
        return 'users'
    
    def get_primary_key(self) -> str:
        """إرجاع اسم المفتاح الأساسي"""
        return 'user_id'
    
    def find_by_username(self, username: str) -> Optional[Dict]:
        """
        البحث عن مستخدم بالاسم
        
        Args:
            username: اسم المستخدم
            
        Returns:
            بيانات المستخدم أو None
        """
        try:
            results = self.find_all("user_name = ?", (username,))
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المستخدم {username}: {str(e)}")
            raise
    
    def find_by_email(self, email: str) -> Optional[Dict]:
        """
        البحث عن مستخدم بالبريد الإلكتروني
        
        Args:
            email: البريد الإلكتروني
            
        Returns:
            بيانات المستخدم أو None
        """
        try:
            results = self.find_all("email = ?", (email,))
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المستخدم بالبريد {email}: {str(e)}")
            raise
    
    def create_user(self, username: str, password: str, full_name: str, 
                   email: str = None, phone: str = None, 
                   permission: str = 'user') -> Optional[int]:
        """
        إنشاء مستخدم جديد
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            full_name: الاسم الكامل
            email: البريد الإلكتروني
            phone: رقم الهاتف
            permission: الصلاحية
            
        Returns:
            معرف المستخدم الجديد
        """
        try:
            # التحقق من عدم وجود المستخدم
            if self.find_by_username(username):
                raise ValueError(f"اسم المستخدم '{username}' موجود بالفعل")
            
            if email and self.find_by_email(email):
                raise ValueError(f"البريد الإلكتروني '{email}' موجود بالفعل")
            
            # تشفير كلمة المرور
            hashed_password = self.hash_password(password)
            
            # إعداد البيانات
            user_data = {
                'user_name': username,
                'user_pass': hashed_password,
                'full_name': full_name,
                'email': email,
                'phone': phone,
                'permission': permission,
                'is_active': 1
            }
            
            return self.insert(user_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المستخدم {username}: {str(e)}")
            raise
    
    def verify_password(self, username: str, password: str) -> bool:
        """
        التحقق من كلمة المرور
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            True إذا كانت كلمة المرور صحيحة
        """
        try:
            user = self.find_by_username(username)
            if not user:
                return False
            
            # التحقق من أن المستخدم نشط
            if not user.get('is_active', 0):
                return False
            
            # التحقق من القفل
            if self.is_user_locked(user):
                return False
            
            # التحقق من كلمة المرور
            stored_password = user.get('password_hash', user.get('user_pass', ''))

            # تشفير كلمة المرور المدخلة
            password_hash = hashlib.sha256(password.encode()).hexdigest()

            is_valid = (password_hash == stored_password)
            
            # تحديث محاولات تسجيل الدخول
            if is_valid:
                self.reset_login_attempts(user['user_id'])
                self.update_last_login(user['user_id'])
            else:
                self.increment_login_attempts(user['user_id'])
            
            return is_valid
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من كلمة المرور للمستخدم {username}: {str(e)}")
            return False
    
    def hash_password(self, password: str) -> str:
        """
        تشفير كلمة المرور

        Args:
            password: كلمة المرور

        Returns:
            كلمة المرور المشفرة
        """
        return hashlib.sha256(password.encode()).hexdigest()
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        تغيير كلمة المرور
        
        Args:
            user_id: معرف المستخدم
            old_password: كلمة المرور القديمة
            new_password: كلمة المرور الجديدة
            
        Returns:
            True إذا تم التغيير بنجاح
        """
        try:
            user = self.find_by_id(user_id)
            if not user:
                return False
            
            # التحقق من كلمة المرور القديمة
            if not self.verify_password(user['user_name'], old_password):
                return False
            
            # تشفير كلمة المرور الجديدة
            hashed_password = self.hash_password(new_password)
            
            # تحديث كلمة المرور
            return self.update(user_id, {'user_pass': hashed_password})
            
        except Exception as e:
            self.logger.error(f"خطأ في تغيير كلمة المرور للمستخدم {user_id}: {str(e)}")
            return False
    
    def reset_password(self, user_id: int, new_password: str) -> bool:
        """
        إعادة تعيين كلمة المرور (للمدير)
        
        Args:
            user_id: معرف المستخدم
            new_password: كلمة المرور الجديدة
            
        Returns:
            True إذا تم التغيير بنجاح
        """
        try:
            hashed_password = self.hash_password(new_password)
            return self.update(user_id, {
                'user_pass': hashed_password,
                'login_attempts': 0,
                'locked_until': None
            })
            
        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين كلمة المرور للمستخدم {user_id}: {str(e)}")
            return False
    
    def increment_login_attempts(self, user_id: int):
        """زيادة عدد محاولات تسجيل الدخول الفاشلة"""
        try:
            user = self.find_by_id(user_id)
            if user:
                attempts = user.get('login_attempts', 0) + 1
                update_data = {'login_attempts': attempts}
                
                # قفل المستخدم بعد 3 محاولات فاشلة
                if attempts >= 3:
                    if self.connection_manager.is_online:
                        # MySQL
                        lock_until = datetime.now() + timedelta(minutes=30)
                        update_data['locked_until'] = lock_until.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        # SQLite
                        lock_until = datetime.now() + timedelta(minutes=30)
                        update_data['locked_until'] = lock_until.strftime('%Y-%m-%d %H:%M:%S')
                
                self.update(user_id, update_data)
                
        except Exception as e:
            self.logger.error(f"خطأ في زيادة محاولات تسجيل الدخول للمستخدم {user_id}: {str(e)}")
    
    def reset_login_attempts(self, user_id: int):
        """إعادة تعيين محاولات تسجيل الدخول"""
        try:
            self.update(user_id, {
                'login_attempts': 0,
                'locked_until': None
            })
        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين محاولات تسجيل الدخول للمستخدم {user_id}: {str(e)}")
    
    def update_last_login(self, user_id: int):
        """تحديث آخر تسجيل دخول"""
        try:
            if self.connection_manager.is_online:
                # MySQL
                self.connection_manager.execute_non_query(
                    "UPDATE users SET last_login = NOW() WHERE user_id = ?",
                    (user_id,)
                )
            else:
                # SQLite
                self.update(user_id, {'last_login': datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث آخر تسجيل دخول للمستخدم {user_id}: {str(e)}")
    
    def is_user_locked(self, user: Dict) -> bool:
        """التحقق من قفل المستخدم"""
        try:
            locked_until = user.get('locked_until')
            if not locked_until:
                return False
            
            if isinstance(locked_until, str):
                locked_until = datetime.strptime(locked_until, '%Y-%m-%d %H:%M:%S')
            
            return datetime.now() < locked_until
            
        except Exception:
            return False
    
    def get_researchers(self) -> List[Dict]:
        """الحصول على قائمة الباحثين النشطين"""
        return self.find_all("is_active = ? AND permission IN ('admin', 'user')", (1,), "full_name")

    def get_all_users(self) -> List[Dict]:
        """الحصول على جميع المستخدمين النشطين"""
        return self.find_all("is_active = ?", (1,), "full_name")

    def get_admins(self) -> List[Dict]:
        """الحصول على قائمة المديرين"""
        return self.find_all("is_active = ? AND permission = ?", (1, 'admin'), "full_name")

    def find_by_email(self, email: str) -> Optional[Dict]:
        """البحث عن مستخدم بالبريد الإلكتروني"""
        results = self.find_all("email = ?", (email,))
        return results[0] if results else None
    
    def search_users(self, search_term: str) -> List[Dict]:
        """البحث في المستخدمين"""
        return self.search(search_term, ['user_name', 'full_name', 'email'])
