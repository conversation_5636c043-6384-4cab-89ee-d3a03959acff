#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وإصلاح كلمة المرور
"""

import sqlite3
import hashlib

def check_and_fix_password():
    """فحص وإصلاح كلمة المرور"""
    
    try:
        conn = sqlite3.connect('data/iots_local.db')
        cursor = conn.cursor()

        cursor.execute('SELECT user_name, user_pass, full_name FROM users WHERE user_name = ?', ('admin',))
        user = cursor.fetchone()

        if user:
            print(f'المستخدم: {user[0]}')
            print(f'كلمة المرور المحفوظة: {user[1]}')
            print(f'الاسم الكامل: {user[2]}')
            
            # اختبار كلمة المرور
            test_password = 'admin'
            hashed = hashlib.sha256(test_password.encode()).hexdigest()
            print(f'كلمة المرور المشفرة للاختبار: {hashed}')
            
            if user[1] == hashed:
                print('✅ كلمة المرور صحيحة')
            else:
                print('❌ كلمة المرور خاطئة - سيتم إصلاحها')
                
                # تحديث كلمة المرور
                cursor.execute('UPDATE users SET user_pass = ? WHERE user_name = ?', (hashed, 'admin'))
                conn.commit()
                print('✅ تم تحديث كلمة المرور')
        else:
            print('❌ المستخدم غير موجود')

        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f'❌ خطأ: {e}')
        return False

if __name__ == "__main__":
    check_and_fix_password()
