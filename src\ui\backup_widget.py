# -*- coding: utf-8 -*-
"""
ويدجت إدارة النسخ الاحتياطي والاستعادة
Backup Management Widget

واجهة شاملة لإدارة النسخ الاحتياطي والاستعادة
"""

from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QLabel, QFrame, QPushButton, QTableWidget, QTableWidgetItem,
                            QGroupBox, QFormLayout, QCheckBox, QSpinBox, QTimeEdit,
                            QTextEdit, QProgressBar, QMessageBox, QFileDialog,
                            QComboBox, QLineEdit, QSplitter, QHeaderView,
                            QAbstractItemView, QMenu, QDialog, QDialogButtonBox)
from PyQt6.QtCore import Qt, QTime, QThread, pyqtSignal, QTimer, QDateTime
from PyQt6.QtGui import QFont, QColor, QPixmap, QAction, QIcon

from ui.base_widget import BaseWidget
from services.backup_service import BackupService
from utils.logger import Logger
from datetime import datetime
import os

class BackupThread(QThread):
    """خيط النسخ الاحتياطي"""
    
    backup_completed = pyqtSignal(bool, str, str)  # success, message, path
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    
    def __init__(self, backup_service, backup_name, include_attachments):
        super().__init__()
        self.backup_service = backup_service
        self.backup_name = backup_name
        self.include_attachments = include_attachments
    
    def run(self):
        """تشغيل النسخ الاحتياطي"""
        try:
            self.status_updated.emit("بدء إنشاء النسخة الاحتياطية...")
            self.progress_updated.emit(10)
            
            success, message, backup_path = self.backup_service.create_backup(
                self.backup_name, self.include_attachments
            )
            
            self.progress_updated.emit(100)
            self.backup_completed.emit(success, message, backup_path)
            
        except Exception as e:
            self.backup_completed.emit(False, str(e), "")

class RestoreThread(QThread):
    """خيط الاستعادة"""
    
    restore_completed = pyqtSignal(bool, str)  # success, message
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    
    def __init__(self, backup_service, backup_path, restore_options):
        super().__init__()
        self.backup_service = backup_service
        self.backup_path = backup_path
        self.restore_options = restore_options
    
    def run(self):
        """تشغيل الاستعادة"""
        try:
            self.status_updated.emit("بدء استعادة النسخة الاحتياطية...")
            self.progress_updated.emit(10)
            
            success, message = self.backup_service.restore_backup(
                self.backup_path,
                self.restore_options.get('restore_database', True),
                self.restore_options.get('restore_config', True),
                self.restore_options.get('restore_attachments', True)
            )
            
            self.progress_updated.emit(100)
            self.restore_completed.emit(success, message)
            
        except Exception as e:
            self.restore_completed.emit(False, str(e))

class BackupSettingsDialog(QDialog):
    """نافذة إعدادات النسخ الاحتياطي"""
    
    def __init__(self, backup_service, parent=None):
        super().__init__(parent)
        self.backup_service = backup_service
        self.setWindowTitle("إعدادات النسخ الاحتياطي")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # إعدادات النسخ الاحتياطي التلقائي
        auto_group = QGroupBox("النسخ الاحتياطي التلقائي")
        auto_layout = QFormLayout(auto_group)
        
        self.auto_backup_check = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        auto_layout.addRow("", self.auto_backup_check)
        
        self.backup_time_edit = QTimeEdit()
        self.backup_time_edit.setTime(QTime(2, 0))  # 2:00 AM
        auto_layout.addRow("وقت النسخ الاحتياطي:", self.backup_time_edit)
        
        layout.addWidget(auto_group)
        
        # إعدادات التخزين
        storage_group = QGroupBox("إعدادات التخزين")
        storage_layout = QFormLayout(storage_group)
        
        self.max_backups_spin = QSpinBox()
        self.max_backups_spin.setRange(1, 100)
        self.max_backups_spin.setValue(30)
        storage_layout.addRow("الحد الأقصى للنسخ الاحتياطية:", self.max_backups_spin)
        
        self.compress_check = QCheckBox("ضغط النسخ الاحتياطية")
        self.compress_check.setChecked(True)
        storage_layout.addRow("", self.compress_check)
        
        self.include_attachments_check = QCheckBox("تضمين المرفقات")
        self.include_attachments_check.setChecked(True)
        storage_layout.addRow("", self.include_attachments_check)
        
        layout.addWidget(storage_group)
        
        # إعدادات إضافية
        additional_group = QGroupBox("إعدادات إضافية")
        additional_layout = QFormLayout(additional_group)
        
        self.backup_on_startup_check = QCheckBox("نسخ احتياطي عند بدء التشغيل")
        additional_layout.addRow("", self.backup_on_startup_check)
        
        self.backup_on_shutdown_check = QCheckBox("نسخ احتياطي عند إغلاق التطبيق")
        self.backup_on_shutdown_check.setChecked(True)
        additional_layout.addRow("", self.backup_on_shutdown_check)
        
        layout.addWidget(additional_group)
        
        # الأزرار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        settings = self.backup_service.get_backup_settings()
        
        self.auto_backup_check.setChecked(settings.get('auto_backup_enabled', True))
        
        backup_time = settings.get('backup_time', '02:00')
        try:
            hour, minute = map(int, backup_time.split(':'))
            self.backup_time_edit.setTime(QTime(hour, minute))
        except:
            self.backup_time_edit.setTime(QTime(2, 0))
        
        self.max_backups_spin.setValue(settings.get('max_backup_files', 30))
        self.compress_check.setChecked(settings.get('compress_backups', True))
        self.include_attachments_check.setChecked(settings.get('include_attachments', True))
        self.backup_on_startup_check.setChecked(settings.get('backup_on_startup', False))
        self.backup_on_shutdown_check.setChecked(settings.get('backup_on_shutdown', True))
    
    def get_settings(self) -> Dict:
        """الحصول على الإعدادات المحدثة"""
        backup_time = self.backup_time_edit.time()
        
        return {
            'auto_backup_enabled': self.auto_backup_check.isChecked(),
            'backup_time': f"{backup_time.hour():02d}:{backup_time.minute():02d}",
            'max_backup_files': self.max_backups_spin.value(),
            'compress_backups': self.compress_check.isChecked(),
            'include_attachments': self.include_attachments_check.isChecked(),
            'backup_on_startup': self.backup_on_startup_check.isChecked(),
            'backup_on_shutdown': self.backup_on_shutdown_check.isChecked()
        }

class RestoreOptionsDialog(QDialog):
    """نافذة خيارات الاستعادة"""
    
    def __init__(self, backup_info, parent=None):
        super().__init__(parent)
        self.backup_info = backup_info
        self.setWindowTitle("خيارات الاستعادة")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # معلومات النسخة الاحتياطية
        info_group = QGroupBox("معلومات النسخة الاحتياطية")
        info_layout = QFormLayout(info_group)
        
        info_layout.addRow("الاسم:", QLabel(self.backup_info.get('backup_name', 'غير محدد')))
        
        created_at = self.backup_info.get('created_at', '')
        if created_at:
            try:
                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                info_layout.addRow("تاريخ الإنشاء:", QLabel(formatted_date))
            except:
                info_layout.addRow("تاريخ الإنشاء:", QLabel(created_at))
        
        file_size_mb = self.backup_info.get('file_size_mb', 0)
        info_layout.addRow("الحجم:", QLabel(f"{file_size_mb} ميجابايت"))
        
        database_type = self.backup_info.get('database_type', 'غير محدد')
        info_layout.addRow("نوع قاعدة البيانات:", QLabel(database_type))
        
        layout.addWidget(info_group)
        
        # خيارات الاستعادة
        options_group = QGroupBox("خيارات الاستعادة")
        options_layout = QVBoxLayout(options_group)
        
        self.restore_database_check = QCheckBox("استعادة قاعدة البيانات")
        self.restore_database_check.setChecked(True)
        options_layout.addWidget(self.restore_database_check)
        
        self.restore_config_check = QCheckBox("استعادة ملفات التكوين")
        self.restore_config_check.setChecked(True)
        options_layout.addWidget(self.restore_config_check)
        
        self.restore_attachments_check = QCheckBox("استعادة المرفقات")
        self.restore_attachments_check.setChecked(True)
        options_layout.addWidget(self.restore_attachments_check)
        
        layout.addWidget(options_group)
        
        # تحذير
        warning_label = QLabel("⚠️ تحذير: ستتم استبدال البيانات الحالية بالبيانات المستعادة")
        warning_label.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 10px;")
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)
        
        # الأزرار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_restore_options(self) -> Dict:
        """الحصول على خيارات الاستعادة"""
        return {
            'restore_database': self.restore_database_check.isChecked(),
            'restore_config': self.restore_config_check.isChecked(),
            'restore_attachments': self.restore_attachments_check.isChecked()
        }

class BackupWidget(BaseWidget):
    """ويدجت إدارة النسخ الاحتياطي الرئيسي"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        
        # إنشاء خدمة النسخ الاحتياطي
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        self.backup_service = BackupService(connection_manager, config_manager)
        
        # خيوط العمل
        self.backup_thread = None
        self.restore_thread = None
        
        super().__init__("إدارة النسخ الاحتياطي والاستعادة", parent)
        
        # بدء جدولة النسخ الاحتياطي التلقائي
        self.backup_service.start_scheduler()
        
        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backup_list()
    
    def setup_content(self):
        """إعداد المحتوى"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.setup_toolbar(layout)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب النسخ الاحتياطي
        self.setup_backup_tab()
        self.tabs.addTab(self.backup_tab, "🔄 النسخ الاحتياطي")
        
        # تبويب الاستعادة
        self.setup_restore_tab()
        self.tabs.addTab(self.restore_tab, "📥 الاستعادة")
        
        # تبويب الإعدادات
        self.setup_settings_tab()
        self.tabs.addTab(self.settings_tab, "⚙️ الإعدادات")
        
        # تبويب الإحصائيات
        self.setup_statistics_tab()
        self.tabs.addTab(self.statistics_tab, "📊 الإحصائيات")
        
        layout.addWidget(self.tabs)
        
        # شريط الحالة
        self.setup_status_bar(layout)
        
        self.content_layout.addLayout(layout)
    
    def setup_toolbar(self, layout):
        """إعداد شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # العنوان
        title_label = QLabel("💾 إدارة النسخ الاحتياطي والاستعادة")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار الإجراءات السريعة
        self.quick_backup_btn = QPushButton("⚡ نسخ احتياطي سريع")
        self.quick_backup_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        self.quick_backup_btn.clicked.connect(self.quick_backup)
        toolbar_layout.addWidget(self.quick_backup_btn)
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        self.refresh_btn.clicked.connect(self.refresh_backup_list)
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_backup_tab(self):
        """إعداد تبويب النسخ الاحتياطي"""
        self.backup_tab = QWidget()
        layout = QVBoxLayout(self.backup_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # إنشاء نسخة احتياطية جديدة
        create_group = QGroupBox("إنشاء نسخة احتياطية جديدة")
        create_layout = QFormLayout(create_group)
        
        self.backup_name_edit = QLineEdit()
        self.backup_name_edit.setPlaceholderText("اتركه فارغاً للاسم التلقائي")
        create_layout.addRow("اسم النسخة الاحتياطية:", self.backup_name_edit)
        
        self.include_attachments_backup_check = QCheckBox("تضمين المرفقات")
        self.include_attachments_backup_check.setChecked(True)
        create_layout.addRow("", self.include_attachments_backup_check)
        
        # زر إنشاء النسخة الاحتياطية
        self.create_backup_btn = QPushButton("🔄 إنشاء نسخة احتياطية")
        self.create_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.create_backup_btn.clicked.connect(self.create_backup)
        create_layout.addRow("", self.create_backup_btn)
        
        layout.addWidget(create_group)
        
        # قائمة النسخ الاحتياطية
        list_group = QGroupBox("النسخ الاحتياطية الموجودة")
        list_layout = QVBoxLayout(list_group)
        
        # جدول النسخ الاحتياطية
        self.backup_table = QTableWidget()
        self.backup_table.setColumnCount(6)
        self.backup_table.setHorizontalHeaderLabels([
            "الاسم", "تاريخ الإنشاء", "الحجم", "نوع قاعدة البيانات", "مضغوط", "الإجراءات"
        ])
        
        # تنسيق الجدول
        header = self.backup_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        self.backup_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.backup_table.setAlternatingRowColors(True)
        self.backup_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.backup_table.customContextMenuRequested.connect(self.show_backup_context_menu)
        
        list_layout.addWidget(self.backup_table)
        
        layout.addWidget(list_group)
    
    def setup_restore_tab(self):
        """إعداد تبويب الاستعادة"""
        self.restore_tab = QWidget()
        layout = QVBoxLayout(self.restore_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # استعادة من ملف
        file_group = QGroupBox("استعادة من ملف خارجي")
        file_layout = QHBoxLayout(file_group)
        
        self.restore_file_edit = QLineEdit()
        self.restore_file_edit.setPlaceholderText("اختر ملف النسخة الاحتياطية...")
        file_layout.addWidget(self.restore_file_edit)
        
        self.browse_file_btn = QPushButton("📁 تصفح")
        self.browse_file_btn.clicked.connect(self.browse_restore_file)
        file_layout.addWidget(self.browse_file_btn)
        
        self.restore_from_file_btn = QPushButton("📥 استعادة من الملف")
        self.restore_from_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.restore_from_file_btn.clicked.connect(self.restore_from_file)
        file_layout.addWidget(self.restore_from_file_btn)
        
        layout.addWidget(file_group)
        
        # تعليمات الاستعادة
        instructions_group = QGroupBox("تعليمات الاستعادة")
        instructions_layout = QVBoxLayout(instructions_group)
        
        instructions_text = QTextEdit()
        instructions_text.setReadOnly(True)
        instructions_text.setMaximumHeight(150)
        instructions_text.setHtml("""
        <h4>كيفية استعادة النسخة الاحتياطية:</h4>
        <ol>
            <li><strong>من القائمة:</strong> انقر بزر الماوس الأيمن على النسخة الاحتياطية واختر "استعادة"</li>
            <li><strong>من ملف خارجي:</strong> اختر الملف واضغط "استعادة من الملف"</li>
            <li><strong>اختر الخيارات:</strong> حدد ما تريد استعادته (قاعدة البيانات، التكوين، المرفقات)</li>
            <li><strong>تأكيد:</strong> اضغط موافق لبدء عملية الاستعادة</li>
        </ol>
        <p><strong>⚠️ تحذير:</strong> ستتم استبدال البيانات الحالية بالبيانات المستعادة</p>
        """)
        instructions_layout.addWidget(instructions_text)
        
        layout.addWidget(instructions_group)
        
        layout.addStretch()
    
    def setup_settings_tab(self):
        """إعداد تبويب الإعدادات"""
        self.settings_tab = QWidget()
        layout = QVBoxLayout(self.settings_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # الإعدادات الحالية
        current_group = QGroupBox("الإعدادات الحالية")
        current_layout = QFormLayout(current_group)
        
        self.current_settings_labels = {}
        settings = self.backup_service.get_backup_settings()
        
        for key, value in settings.items():
            label = QLabel(str(value))
            self.current_settings_labels[key] = label
            current_layout.addRow(self.translate_setting_key(key) + ":", label)
        
        layout.addWidget(current_group)
        
        # أزرار الإعدادات
        settings_buttons_layout = QHBoxLayout()
        
        self.edit_settings_btn = QPushButton("⚙️ تعديل الإعدادات")
        self.edit_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.edit_settings_btn.clicked.connect(self.edit_settings)
        settings_buttons_layout.addWidget(self.edit_settings_btn)
        
        self.reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        self.reset_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.reset_settings_btn.clicked.connect(self.reset_settings)
        settings_buttons_layout.addWidget(self.reset_settings_btn)
        
        settings_buttons_layout.addStretch()
        layout.addLayout(settings_buttons_layout)
        
        layout.addStretch()
    
    def setup_statistics_tab(self):
        """إعداد تبويب الإحصائيات"""
        self.statistics_tab = QWidget()
        layout = QVBoxLayout(self.statistics_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # إحصائيات عامة
        stats_group = QGroupBox("إحصائيات النسخ الاحتياطي")
        stats_layout = QFormLayout(stats_group)
        
        self.stats_labels = {}
        stats_keys = [
            'total_backups', 'total_size_mb', 'average_size_mb',
            'backup_frequency', 'auto_backup_enabled'
        ]
        
        for key in stats_keys:
            label = QLabel("جاري التحميل...")
            self.stats_labels[key] = label
            stats_layout.addRow(self.translate_stat_key(key) + ":", label)
        
        layout.addWidget(stats_group)
        
        # أحدث وأقدم نسخة احتياطية
        recent_group = QGroupBox("معلومات النسخ الاحتياطية")
        recent_layout = QVBoxLayout(recent_group)
        
        self.latest_backup_label = QLabel("جاري التحميل...")
        recent_layout.addWidget(QLabel("أحدث نسخة احتياطية:"))
        recent_layout.addWidget(self.latest_backup_label)
        
        self.oldest_backup_label = QLabel("جاري التحميل...")
        recent_layout.addWidget(QLabel("أقدم نسخة احتياطية:"))
        recent_layout.addWidget(self.oldest_backup_label)
        
        layout.addWidget(recent_group)
        
        # زر تحديث الإحصائيات
        self.refresh_stats_btn = QPushButton("🔄 تحديث الإحصائيات")
        self.refresh_stats_btn.clicked.connect(self.refresh_statistics)
        layout.addWidget(self.refresh_stats_btn)
        
        layout.addStretch()
        
        # تحديث الإحصائيات
        self.refresh_statistics()
    
    def setup_status_bar(self, layout):
        """إعداد شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_frame)

    # ==================== وظائف النسخ الاحتياطي ====================

    def quick_backup(self):
        """نسخ احتياطي سريع"""
        backup_name = f"quick_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.start_backup(backup_name, True)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_name = self.backup_name_edit.text().strip()
        if not backup_name:
            backup_name = None  # سيتم إنشاء اسم تلقائي

        include_attachments = self.include_attachments_backup_check.isChecked()
        self.start_backup(backup_name, include_attachments)

    def start_backup(self, backup_name, include_attachments):
        """بدء عملية النسخ الاحتياطي"""
        if self.backup_thread and self.backup_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية نسخ احتياطي جارية بالفعل")
            return

        # تعطيل الأزرار
        self.set_backup_buttons_enabled(False)

        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label.setText("جاري إنشاء النسخة الاحتياطية...")

        # بدء خيط النسخ الاحتياطي
        self.backup_thread = BackupThread(self.backup_service, backup_name, include_attachments)
        self.backup_thread.backup_completed.connect(self.on_backup_completed)
        self.backup_thread.progress_updated.connect(self.progress_bar.setValue)
        self.backup_thread.status_updated.connect(self.status_label.setText)
        self.backup_thread.start()

    def on_backup_completed(self, success, message, backup_path):
        """معالجة اكتمال النسخ الاحتياطي"""
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)

        # تفعيل الأزرار
        self.set_backup_buttons_enabled(True)

        if success:
            self.status_label.setText(f"تم إنشاء النسخة الاحتياطية: {os.path.basename(backup_path)}")

            # مسح حقل الاسم
            self.backup_name_edit.clear()

            # تحديث قائمة النسخ الاحتياطية
            self.refresh_backup_list()

            # تحديث الإحصائيات
            self.refresh_statistics()

            # عرض رسالة النجاح
            QMessageBox.information(
                self,
                "نجح النسخ الاحتياطي",
                f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}"
            )
        else:
            self.status_label.setText("فشل في إنشاء النسخة الاحتياطية")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{message}")

        # تنظيف الخيط
        if self.backup_thread:
            self.backup_thread.deleteLater()
            self.backup_thread = None

    def set_backup_buttons_enabled(self, enabled):
        """تفعيل/تعطيل أزرار النسخ الاحتياطي"""
        self.quick_backup_btn.setEnabled(enabled)
        self.create_backup_btn.setEnabled(enabled)
        self.refresh_btn.setEnabled(enabled)

    # ==================== وظائف الاستعادة ====================

    def browse_restore_file(self):
        """تصفح ملف الاستعادة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            "",
            "ملفات النسخ الاحتياطي (*.zip);;جميع الملفات (*)"
        )

        if file_path:
            self.restore_file_edit.setText(file_path)

    def restore_from_file(self):
        """استعادة من ملف خارجي"""
        file_path = self.restore_file_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف النسخة الاحتياطية")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "تحذير", "الملف المحدد غير موجود")
            return

        # التحقق من سلامة النسخة الاحتياطية
        is_valid, message, verification_results = self.backup_service.verify_backup_integrity(file_path)

        if not is_valid:
            reply = QMessageBox.question(
                self,
                "تحذير",
                f"النسخة الاحتياطية قد تكون تالفة:\n{message}\n\nهل تريد المتابعة؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

        # إنشاء معلومات أساسية للنسخة الاحتياطية
        backup_info = {
            'backup_name': os.path.basename(file_path),
            'file_path': file_path,
            'file_size_mb': round(os.path.getsize(file_path) / (1024 * 1024), 2),
            'database_type': 'غير محدد'
        }

        self.start_restore(backup_info)

    def restore_backup_from_list(self, backup_info):
        """استعادة نسخة احتياطية من القائمة"""
        self.start_restore(backup_info)

    def start_restore(self, backup_info):
        """بدء عملية الاستعادة"""
        if self.restore_thread and self.restore_thread.isRunning():
            QMessageBox.warning(self, "تحذير", "عملية استعادة جارية بالفعل")
            return

        # عرض نافذة خيارات الاستعادة
        options_dialog = RestoreOptionsDialog(backup_info, self)
        if options_dialog.exec() != QDialog.DialogCode.Accepted:
            return

        restore_options = options_dialog.get_restore_options()

        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self,
            "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n"
            "⚠️ تحذير: ستتم استبدال البيانات الحالية بالبيانات المستعادة",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # تعطيل الأزرار
        self.set_restore_buttons_enabled(False)

        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label.setText("جاري استعادة النسخة الاحتياطية...")

        # بدء خيط الاستعادة
        self.restore_thread = RestoreThread(
            self.backup_service, backup_info['file_path'], restore_options
        )
        self.restore_thread.restore_completed.connect(self.on_restore_completed)
        self.restore_thread.progress_updated.connect(self.progress_bar.setValue)
        self.restore_thread.status_updated.connect(self.status_label.setText)
        self.restore_thread.start()

    def on_restore_completed(self, success, message):
        """معالجة اكتمال الاستعادة"""
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)

        # تفعيل الأزرار
        self.set_restore_buttons_enabled(True)

        if success:
            self.status_label.setText("تم استعادة النسخة الاحتياطية بنجاح")

            # مسح حقل الملف
            self.restore_file_edit.clear()

            # عرض رسالة النجاح
            QMessageBox.information(
                self,
                "نجحت الاستعادة",
                "تم استعادة النسخة الاحتياطية بنجاح!\n\n"
                "يُنصح بإعادة تشغيل التطبيق لضمان تحميل البيانات المستعادة بشكل صحيح."
            )
        else:
            self.status_label.setText("فشل في استعادة النسخة الاحتياطية")
            QMessageBox.critical(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{message}")

        # تنظيف الخيط
        if self.restore_thread:
            self.restore_thread.deleteLater()
            self.restore_thread = None

    def set_restore_buttons_enabled(self, enabled):
        """تفعيل/تعطيل أزرار الاستعادة"""
        self.restore_from_file_btn.setEnabled(enabled)
        self.browse_file_btn.setEnabled(enabled)

    # ==================== إدارة قائمة النسخ الاحتياطية ====================

    def refresh_backup_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            backups = self.backup_service.get_backup_list()

            self.backup_table.setRowCount(len(backups))

            for row, backup in enumerate(backups):
                # الاسم
                name_item = QTableWidgetItem(backup.get('backup_name', 'غير محدد'))
                self.backup_table.setItem(row, 0, name_item)

                # تاريخ الإنشاء
                created_at = backup.get('created_at', '')
                if created_at:
                    try:
                        dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                        date_item = QTableWidgetItem(formatted_date)
                    except:
                        date_item = QTableWidgetItem(created_at)
                else:
                    date_item = QTableWidgetItem('غير محدد')
                self.backup_table.setItem(row, 1, date_item)

                # الحجم
                file_size_mb = backup.get('file_size_mb', 0)
                size_item = QTableWidgetItem(f"{file_size_mb} MB")
                self.backup_table.setItem(row, 2, size_item)

                # نوع قاعدة البيانات
                db_type = backup.get('database_type', 'غير محدد')
                db_item = QTableWidgetItem(db_type)
                self.backup_table.setItem(row, 3, db_item)

                # مضغوط
                compressed = backup.get('compressed', False)
                compressed_item = QTableWidgetItem("نعم" if compressed else "لا")
                self.backup_table.setItem(row, 4, compressed_item)

                # الإجراءات (سيتم إضافتها في القائمة المنبثقة)
                actions_item = QTableWidgetItem("انقر بالزر الأيمن")
                actions_item.setData(Qt.ItemDataRole.UserRole, backup)
                self.backup_table.setItem(row, 5, actions_item)

            self.status_label.setText(f"تم تحميل {len(backups)} نسخة احتياطية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل قائمة النسخ الاحتياطية:\n{str(e)}")

    def show_backup_context_menu(self, position):
        """عرض القائمة المنبثقة للنسخ الاحتياطية"""
        item = self.backup_table.itemAt(position)
        if not item:
            return

        row = item.row()
        backup_info = self.backup_table.item(row, 5).data(Qt.ItemDataRole.UserRole)

        if not backup_info:
            return

        menu = QMenu(self)

        # استعادة
        restore_action = QAction("📥 استعادة", self)
        restore_action.triggered.connect(lambda: self.restore_backup_from_list(backup_info))
        menu.addAction(restore_action)

        # التحقق من السلامة
        verify_action = QAction("🔍 التحقق من السلامة", self)
        verify_action.triggered.connect(lambda: self.verify_backup(backup_info))
        menu.addAction(verify_action)

        menu.addSeparator()

        # فتح مجلد الملف
        open_folder_action = QAction("📁 فتح مجلد الملف", self)
        open_folder_action.triggered.connect(lambda: self.open_backup_folder(backup_info))
        menu.addAction(open_folder_action)

        # نسخ المسار
        copy_path_action = QAction("📋 نسخ المسار", self)
        copy_path_action.triggered.connect(lambda: self.copy_backup_path(backup_info))
        menu.addAction(copy_path_action)

        menu.addSeparator()

        # حذف
        delete_action = QAction("🗑️ حذف", self)
        delete_action.triggered.connect(lambda: self.delete_backup(backup_info))
        menu.addAction(delete_action)

        menu.exec(self.backup_table.mapToGlobal(position))

    def verify_backup(self, backup_info):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            backup_path = backup_info['file_path']
            is_valid, message, verification_results = self.backup_service.verify_backup_integrity(backup_path)

            # إنشاء تقرير التحقق
            report = f"نتيجة التحقق من النسخة الاحتياطية: {backup_info['backup_name']}\n\n"
            report += f"الحالة العامة: {'سليمة' if is_valid else 'تالفة'}\n"
            report += f"الرسالة: {message}\n\n"
            report += "تفاصيل التحقق:\n"

            for check, result in verification_results.items():
                status = "✅ نجح" if result else "❌ فشل"
                report += f"- {self.translate_verification_check(check)}: {status}\n"

            # عرض التقرير
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تقرير التحقق من السلامة")
            msg_box.setText(report)
            msg_box.setIcon(QMessageBox.Icon.Information if is_valid else QMessageBox.Icon.Warning)
            msg_box.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التحقق من سلامة النسخة الاحتياطية:\n{str(e)}")

    def open_backup_folder(self, backup_info):
        """فتح مجلد النسخة الاحتياطية"""
        try:
            import subprocess
            import platform

            backup_path = backup_info['file_path']
            folder_path = os.path.dirname(backup_path)

            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في فتح مجلد الملف:\n{str(e)}")

    def copy_backup_path(self, backup_info):
        """نسخ مسار النسخة الاحتياطية"""
        try:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(backup_info['file_path'])
            self.status_label.setText("تم نسخ المسار إلى الحافظة")
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في نسخ المسار:\n{str(e)}")

    def delete_backup(self, backup_info):
        """حذف نسخة احتياطية"""
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{backup_info['backup_name']}؟\n\n"
            "لا يمكن التراجع عن هذا الإجراء.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success, message = self.backup_service.delete_backup(backup_info['file_path'])

                if success:
                    self.status_label.setText(f"تم حذف النسخة الاحتياطية: {backup_info['backup_name']}")
                    self.refresh_backup_list()
                    self.refresh_statistics()
                    QMessageBox.information(self, "نجح الحذف", "تم حذف النسخة الاحتياطية بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", f"فشل في حذف النسخة الاحتياطية:\n{message}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في حذف النسخة الاحتياطية:\n{str(e)}")

    # ==================== إدارة الإعدادات ====================

    def edit_settings(self):
        """تعديل إعدادات النسخ الاحتياطي"""
        dialog = BackupSettingsDialog(self.backup_service, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_settings = dialog.get_settings()

            success = self.backup_service.update_backup_settings(new_settings)

            if success:
                self.refresh_settings_display()
                self.status_label.setText("تم حفظ الإعدادات بنجاح")
                QMessageBox.information(self, "نجح الحفظ", "تم حفظ إعدادات النسخ الاحتياطي بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الإعدادات")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "تأكيد إعادة التعيين",
            "هل أنت متأكد من إعادة تعيين جميع إعدادات النسخ الاحتياطي إلى القيم الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = self.backup_service.repair_backup_settings()

            if success:
                self.refresh_settings_display()
                self.status_label.setText("تم إعادة تعيين الإعدادات")
                QMessageBox.information(self, "نجحت إعادة التعيين", "تم إعادة تعيين الإعدادات بنجاح")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إعادة تعيين الإعدادات")

    def refresh_settings_display(self):
        """تحديث عرض الإعدادات"""
        settings = self.backup_service.get_backup_settings()

        for key, value in settings.items():
            if key in self.current_settings_labels:
                self.current_settings_labels[key].setText(str(value))

    # ==================== الإحصائيات ====================

    def refresh_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = self.backup_service.get_backup_statistics()

            # تحديث التسميات
            for key, value in stats.items():
                if key in self.stats_labels:
                    if key == 'auto_backup_enabled':
                        display_value = "مفعل" if value else "معطل"
                    elif key in ['total_size_mb', 'average_size_mb']:
                        display_value = f"{value} ميجابايت"
                    else:
                        display_value = str(value)

                    self.stats_labels[key].setText(display_value)

            # تحديث معلومات أحدث وأقدم نسخة احتياطية
            latest_backup = stats.get('latest_backup')
            if latest_backup:
                latest_text = f"{latest_backup['backup_name']} ({latest_backup.get('file_size_mb', 0)} MB)"
                self.latest_backup_label.setText(latest_text)
            else:
                self.latest_backup_label.setText("لا توجد نسخ احتياطية")

            oldest_backup = stats.get('oldest_backup')
            if oldest_backup:
                oldest_text = f"{oldest_backup['backup_name']} ({oldest_backup.get('file_size_mb', 0)} MB)"
                self.oldest_backup_label.setText(oldest_text)
            else:
                self.oldest_backup_label.setText("لا توجد نسخ احتياطية")

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في تحديث الإحصائيات:\n{str(e)}")

    # ==================== وظائف مساعدة ====================

    def translate_setting_key(self, key: str) -> str:
        """ترجمة مفاتيح الإعدادات"""
        translations = {
            'auto_backup_enabled': 'النسخ الاحتياطي التلقائي',
            'backup_interval_hours': 'فترة النسخ الاحتياطي (ساعات)',
            'max_backup_files': 'الحد الأقصى للنسخ الاحتياطية',
            'compress_backups': 'ضغط النسخ الاحتياطية',
            'backup_location': 'مكان النسخ الاحتياطي',
            'include_attachments': 'تضمين المرفقات',
            'backup_time': 'وقت النسخ الاحتياطي',
            'backup_on_startup': 'نسخ احتياطي عند بدء التشغيل',
            'backup_on_shutdown': 'نسخ احتياطي عند الإغلاق'
        }
        return translations.get(key, key)

    def translate_stat_key(self, key: str) -> str:
        """ترجمة مفاتيح الإحصائيات"""
        translations = {
            'total_backups': 'إجمالي النسخ الاحتياطية',
            'total_size_mb': 'الحجم الإجمالي',
            'average_size_mb': 'متوسط الحجم',
            'backup_frequency': 'تكرار النسخ الاحتياطي',
            'auto_backup_enabled': 'النسخ الاحتياطي التلقائي'
        }
        return translations.get(key, key)

    def translate_verification_check(self, check: str) -> str:
        """ترجمة فحوصات التحقق"""
        translations = {
            'file_exists': 'وجود الملف',
            'file_readable': 'قابلية القراءة',
            'backup_info_valid': 'صحة معلومات النسخة الاحتياطية',
            'database_files_exist': 'وجود ملفات قاعدة البيانات',
            'checksum_valid': 'صحة checksum',
            'structure_valid': 'صحة الهيكل'
        }
        return translations.get(check, check)

    def get_toolbar_button_style(self, color: str) -> str:
        """نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:disabled {{
                background-color: #95a5a6;
                color: #ecf0f1;
            }}
        """

    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)

    def refresh(self):
        """تحديث الويدجت"""
        self.refresh_backup_list()
        self.refresh_statistics()
        self.refresh_settings_display()

    def closeEvent(self, event):
        """معالجة إغلاق الويدجت"""
        # إيقاف الجدولة
        self.backup_service.stop_scheduler()

        # تنظيف الملفات المؤقتة
        self.backup_service.cleanup_temp_files()

        super().closeEvent(event)
