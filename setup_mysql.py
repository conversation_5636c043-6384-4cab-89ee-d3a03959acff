#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة بيانات MySQL
MySQL Database Setup

ينشئ قاعدة بيانات MySQL مع البيانات الأساسية
"""

import sys
import os
import hashlib

# إضافة مسار src
sys.path.insert(0, 'src')

def test_mysql_availability():
    """اختبار توفر مكتبة MySQL"""
    try:
        import mysql.connector
        print("✅ مكتبة mysql.connector متوفرة")
        return True
    except ImportError:
        print("❌ مكتبة mysql.connector غير متوفرة")
        print("لتثبيتها، استخدم: pip install mysql-connector-python")
        return False

def create_mysql_database():
    """إنشاء قاعدة بيانات MySQL"""
    
    if not test_mysql_availability():
        return False
    
    try:
        import mysql.connector
        from utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        print(f"محاولة الاتصال بخادم MySQL: {config.mysql_host}:{config.mysql_port}")
        
        # الاتصال بخادم MySQL (بدون تحديد قاعدة بيانات)
        connection = mysql.connector.connect(
            host=config.mysql_host,
            port=config.mysql_port,
            user=config.mysql_user,
            password=config.mysql_password,
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {config.mysql_database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print(f"✅ تم إنشاء قاعدة البيانات: {config.mysql_database}")
        
        # الاتصال بقاعدة البيانات المحددة
        cursor.execute(f"USE {config.mysql_database}")
        
        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                user_id INT AUTO_INCREMENT PRIMARY KEY,
                user_name VARCHAR(50) NOT NULL UNIQUE,
                user_pass VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                permission ENUM('admin', 'user') DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول المعاملات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                transaction_id INT AUTO_INCREMENT PRIMARY KEY,
                head_incoming_no VARCHAR(50),
                head_incoming_date DATE,
                subject TEXT NOT NULL,
                status VARCHAR(50) DEFAULT 'new',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إنشاء جدول أنواع التأشيرات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS visa_types (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type_name VARCHAR(100) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # إدراج المستخدم الافتراضي
        admin_password = hashlib.sha256("admin".encode()).hexdigest()
        cursor.execute("""
            INSERT IGNORE INTO users (user_name, user_pass, full_name, email, permission) 
            VALUES (%s, %s, %s, %s, %s)
        """, ('admin', admin_password, 'مدير النظام', '<EMAIL>', 'admin'))
        
        # إدراج أنواع التأشيرات
        visa_types_data = [
            ('تأشيرة عمل', 'تأشيرة للعمل في المملكة'),
            ('تأشيرة زيارة', 'تأشيرة زيارة عائلية أو سياحية'),
            ('تأشيرة دراسة', 'تأشيرة للدراسة في المملكة'),
            ('تأشيرة علاج', 'تأشيرة للعلاج الطبي'),
            ('تأشيرة حج', 'تأشيرة لأداء فريضة الحج'),
            ('تأشيرة عمرة', 'تأشيرة لأداء العمرة'),
            ('تأشيرة ترانزيت', 'تأشيرة عبور')
        ]
        
        for visa_type, description in visa_types_data:
            cursor.execute("""
                INSERT IGNORE INTO visa_types (type_name, description) 
                VALUES (%s, %s)
            """, (visa_type, description))
        
        # إدراج بعض المعاملات التجريبية
        sample_transactions = [
            ('2024/001', '2024-01-15', 'طلب تأشيرة عمل - شركة التقنية المتقدمة', 'new'),
            ('2024/002', '2024-01-16', 'طلب تأشيرة زيارة عائلية', 'in_progress'),
            ('2024/003', '2024-01-17', 'طلب تأشيرة دراسة - جامعة الملك سعود', 'completed')
        ]
        
        for trans_no, trans_date, subject, status in sample_transactions:
            cursor.execute("""
                INSERT IGNORE INTO transactions (head_incoming_no, head_incoming_date, subject, status) 
                VALUES (%s, %s, %s, %s)
            """, (trans_no, trans_date, subject, status))
        
        # حفظ التغييرات
        connection.commit()
        
        # عرض الإحصائيات
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM transactions")
        transactions_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM visa_types")
        visa_types_count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        print(f"✅ تم إعداد قاعدة بيانات MySQL بنجاح!")
        print(f"📊 الإحصائيات:")
        print(f"   - المستخدمين: {users_count}")
        print(f"   - المعاملات: {transactions_count}")
        print(f"   - أنواع التأشيرات: {visa_types_count}")
        print(f"\n🔑 بيانات تسجيل الدخول:")
        print(f"   اسم المستخدم: admin")
        print(f"   كلمة المرور: admin")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة بيانات MySQL: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("إعداد قاعدة بيانات MySQL")
    print("=" * 50)
    
    if create_mysql_database():
        print("\n🎉 تم إعداد قاعدة بيانات MySQL بنجاح!")
        print("\nيمكنك الآن تشغيل التطبيق وسيتصل تلقائياً بـ MySQL:")
        print("python main.py")
        return 0
    else:
        print("\n❌ فشل في إعداد قاعدة بيانات MySQL")
        print("سيعمل التطبيق في النمط المحلي (SQLite)")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
