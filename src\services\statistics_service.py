# -*- coding: utf-8 -*-
"""
خدمة الإحصائيات
Statistics Service

تدير حساب وتجميع الإحصائيات المختلفة للنظام
"""

from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta, date
import calendar

from utils.logger import Logger

class StatisticsService:
    """خدمة الإحصائيات"""
    
    def __init__(self, connection_manager):
        """
        تهيئة خدمة الإحصائيات
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        self.logger = Logger(__name__)
    
    def get_overview_statistics(self) -> Dict:
        """
        الحصول على الإحصائيات العامة
        
        Returns:
            قاموس بالإحصائيات العامة
        """
        try:
            # استعلام الإحصائيات الأساسية
            if self.connection_manager.is_online:
                # MySQL
                query = """
                    SELECT 
                        COUNT(*) as total_transactions,
                        COUNT(CASE WHEN rs.request_status = 'جديد' THEN 1 END) as new_transactions,
                        COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) as processing_transactions,
                        COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed_transactions,
                        COUNT(CASE WHEN rs.request_status = 'مؤجل' THEN 1 END) as delayed_transactions,
                        COUNT(CASE WHEN rs.request_status = 'ملغي' THEN 1 END) as cancelled_transactions,
                        COUNT(CASE WHEN t.due_date < CURDATE() AND rs.request_status NOT IN ('مكتمل', 'ملغي') THEN 1 END) as overdue_transactions,
                        AVG(CASE WHEN t.completion_date IS NOT NULL THEN DATEDIFF(t.completion_date, t.head_incoming_date) END) as avg_completion_days
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                """
            else:
                # SQLite
                query = """
                    SELECT 
                        COUNT(*) as total_transactions,
                        COUNT(CASE WHEN rs.request_status = 'جديد' THEN 1 END) as new_transactions,
                        COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) as processing_transactions,
                        COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed_transactions,
                        COUNT(CASE WHEN rs.request_status = 'مؤجل' THEN 1 END) as delayed_transactions,
                        COUNT(CASE WHEN rs.request_status = 'ملغي' THEN 1 END) as cancelled_transactions,
                        COUNT(CASE WHEN julianday(t.due_date) < julianday('now') AND rs.request_status NOT IN ('مكتمل', 'ملغي') THEN 1 END) as overdue_transactions,
                        AVG(CASE WHEN t.completion_date IS NOT NULL THEN julianday(t.completion_date) - julianday(t.head_incoming_date) END) as avg_completion_days
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                """
            
            results = self.connection_manager.execute_query(query)
            
            if results:
                stats = results[0]
                
                # حساب معدل الإنجاز
                total = stats.get('total_transactions', 0)
                completed = stats.get('completed_transactions', 0)
                completion_rate = (completed / total * 100) if total > 0 else 0
                
                # حساب معدل التأخير
                overdue = stats.get('overdue_transactions', 0)
                overdue_rate = (overdue / total * 100) if total > 0 else 0
                
                return {
                    'total_transactions': stats.get('total_transactions', 0),
                    'new_transactions': stats.get('new_transactions', 0),
                    'processing_transactions': stats.get('processing_transactions', 0),
                    'completed_transactions': stats.get('completed_transactions', 0),
                    'delayed_transactions': stats.get('delayed_transactions', 0),
                    'cancelled_transactions': stats.get('cancelled_transactions', 0),
                    'overdue_transactions': stats.get('overdue_transactions', 0),
                    'completion_rate': round(completion_rate, 1),
                    'overdue_rate': round(overdue_rate, 1),
                    'avg_completion_days': round(stats.get('avg_completion_days', 0) or 0, 1)
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات العامة: {str(e)}")
            return {}
    
    def get_monthly_statistics(self, year: int = None) -> List[Dict]:
        """
        الحصول على الإحصائيات الشهرية
        
        Args:
            year: السنة (افتراضياً السنة الحالية)
            
        Returns:
            قائمة بالإحصائيات الشهرية
        """
        try:
            if year is None:
                year = datetime.now().year
            
            if self.connection_manager.is_online:
                # MySQL
                query = """
                    SELECT 
                        MONTH(t.head_incoming_date) as month,
                        COUNT(*) as total,
                        COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed,
                        COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) as processing
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                    WHERE YEAR(t.head_incoming_date) = ?
                    GROUP BY MONTH(t.head_incoming_date)
                    ORDER BY MONTH(t.head_incoming_date)
                """
            else:
                # SQLite
                query = """
                    SELECT 
                        CAST(strftime('%m', t.head_incoming_date) AS INTEGER) as month,
                        COUNT(*) as total,
                        COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed,
                        COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) as processing
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                    WHERE strftime('%Y', t.head_incoming_date) = ?
                    GROUP BY strftime('%m', t.head_incoming_date)
                    ORDER BY strftime('%m', t.head_incoming_date)
                """
            
            results = self.connection_manager.execute_query(query, (str(year),))
            
            # إنشاء قائمة بجميع الشهور (12 شهر)
            monthly_stats = []
            month_names = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ]
            
            # إنشاء قاموس للنتائج
            results_dict = {row['month']: row for row in results}
            
            for month_num in range(1, 13):
                if month_num in results_dict:
                    row = results_dict[month_num]
                    monthly_stats.append({
                        'month': month_num,
                        'month_name': month_names[month_num - 1],
                        'total': row['total'],
                        'completed': row['completed'],
                        'processing': row['processing']
                    })
                else:
                    monthly_stats.append({
                        'month': month_num,
                        'month_name': month_names[month_num - 1],
                        'total': 0,
                        'completed': 0,
                        'processing': 0
                    })
            
            return monthly_stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الإحصائيات الشهرية: {str(e)}")
            return []
    
    def get_researcher_statistics(self) -> List[Dict]:
        """
        الحصول على إحصائيات الباحثين
        
        Returns:
            قائمة بإحصائيات الباحثين
        """
        try:
            query = """
                SELECT 
                    u.user_id,
                    u.full_name,
                    COUNT(t.id) as total_assigned,
                    COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed,
                    COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) as processing,
                    COUNT(CASE WHEN rs.request_status = 'جديد' THEN 1 END) as new_tasks
                FROM users u
                LEFT JOIN transactions t ON u.user_id = t.researcher_1_id
                LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                WHERE u.is_active = 1
                GROUP BY u.user_id, u.full_name
                HAVING COUNT(t.id) > 0
                ORDER BY total_assigned DESC
            """
            
            results = self.connection_manager.execute_query(query)
            
            researcher_stats = []
            for row in results:
                total = row['total_assigned']
                completed = row['completed']
                completion_rate = (completed / total * 100) if total > 0 else 0
                
                researcher_stats.append({
                    'user_id': row['user_id'],
                    'name': row['full_name'],
                    'total_assigned': total,
                    'completed': completed,
                    'processing': row['processing'],
                    'new_tasks': row['new_tasks'],
                    'completion_rate': round(completion_rate, 1)
                })
            
            return researcher_stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الباحثين: {str(e)}")
            return []
    
    def get_visa_type_statistics(self) -> List[Dict]:
        """
        الحصول على إحصائيات أنواع التأشيرات
        
        Returns:
            قائمة بإحصائيات أنواع التأشيرات
        """
        try:
            query = """
                SELECT 
                    vt.visa_type,
                    COUNT(t.id) as total_count,
                    COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) as completed_count
                FROM visa_types vt
                LEFT JOIN transactions t ON vt.id = t.visa_type_id
                LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                WHERE vt.is_active = 1
                GROUP BY vt.id, vt.visa_type
                HAVING COUNT(t.id) > 0
                ORDER BY total_count DESC
            """
            
            results = self.connection_manager.execute_query(query)
            
            visa_stats = []
            total_all = sum(row['total_count'] for row in results)
            
            for row in results:
                total = row['total_count']
                completed = row['completed_count']
                percentage = (total / total_all * 100) if total_all > 0 else 0
                completion_rate = (completed / total * 100) if total > 0 else 0
                
                visa_stats.append({
                    'visa_type': row['visa_type'],
                    'total_count': total,
                    'completed_count': completed,
                    'percentage': round(percentage, 1),
                    'completion_rate': round(completion_rate, 1)
                })
            
            return visa_stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات أنواع التأشيرات: {str(e)}")
            return []
    
    def get_recent_activity(self, limit: int = 10) -> List[Dict]:
        """
        الحصول على النشاطات الحديثة
        
        Args:
            limit: عدد النشاطات المطلوبة
            
        Returns:
            قائمة بالنشاطات الحديثة
        """
        try:
            if self.connection_manager.is_online:
                # MySQL
                query = """
                    SELECT 
                        t.head_incoming_no,
                        t.subject,
                        t.head_incoming_date,
                        rs.request_status,
                        u1.full_name as data_entry_user,
                        u2.full_name as researcher_name,
                        t.created_at,
                        t.updated_at
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                    LEFT JOIN users u1 ON t.user_id = u1.user_id
                    LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
                    ORDER BY t.updated_at DESC
                    LIMIT ?
                """
            else:
                # SQLite
                query = """
                    SELECT 
                        t.head_incoming_no,
                        t.subject,
                        t.head_incoming_date,
                        rs.request_status,
                        u1.full_name as data_entry_user,
                        u2.full_name as researcher_name,
                        t.created_at,
                        t.updated_at
                    FROM transactions t
                    LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                    LEFT JOIN users u1 ON t.user_id = u1.user_id
                    LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
                    ORDER BY t.updated_at DESC
                    LIMIT ?
                """
            
            results = self.connection_manager.execute_query(query, (limit,))
            
            activities = []
            for row in results:
                # تقصير الموضوع إذا كان طويلاً
                subject = row['subject']
                if len(subject) > 50:
                    subject = subject[:47] + "..."
                
                activities.append({
                    'incoming_no': row['head_incoming_no'],
                    'subject': subject,
                    'incoming_date': row['head_incoming_date'],
                    'status': row['request_status'],
                    'data_entry_user': row['data_entry_user'],
                    'researcher_name': row['researcher_name'],
                    'created_at': row['created_at'],
                    'updated_at': row['updated_at']
                })
            
            return activities
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على النشاطات الحديثة: {str(e)}")
            return []
    
    def get_priority_statistics(self) -> Dict:
        """
        الحصول على إحصائيات الأولوية
        
        Returns:
            قاموس بإحصائيات الأولوية
        """
        try:
            query = """
                SELECT 
                    t.priority,
                    COUNT(*) as count
                FROM transactions t
                GROUP BY t.priority
                ORDER BY 
                    CASE t.priority 
                        WHEN 'urgent' THEN 1
                        WHEN 'high' THEN 2
                        WHEN 'medium' THEN 3
                        WHEN 'low' THEN 4
                    END
            """
            
            results = self.connection_manager.execute_query(query)
            
            priority_names = {
                'urgent': 'عاجل',
                'high': 'عالي',
                'medium': 'متوسط',
                'low': 'منخفض'
            }
            
            priority_stats = {}
            total = sum(row['count'] for row in results)
            
            for row in results:
                priority = row['priority']
                count = row['count']
                percentage = (count / total * 100) if total > 0 else 0
                
                priority_stats[priority] = {
                    'name': priority_names.get(priority, priority),
                    'count': count,
                    'percentage': round(percentage, 1)
                }
            
            return priority_stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات الأولوية: {str(e)}")
            return {}
