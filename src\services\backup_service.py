# -*- coding: utf-8 -*-
"""
خدمة النسخ الاحتياطي والاستعادة
Backup and Restore Service

تدير النسخ الاحتياطي التلقائي والاستعادة مع الضغط والجدولة
"""

import os
import shutil
import sqlite3
import zipfile
import json
import hashlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import threading
import time

try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    SCHEDULE_AVAILABLE = False

try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from utils.logger import Logger
from utils.config_manager import ConfigManager

class BackupService:
    """خدمة النسخ الاحتياطي والاستعادة"""
    
    def __init__(self, connection_manager, config_manager: ConfigManager):
        """
        تهيئة خدمة النسخ الاحتياطي
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
            config_manager: مدير التكوين
        """
        self.connection_manager = connection_manager
        self.config_manager = config_manager
        self.logger = Logger(__name__)
        
        # إعداد مجلدات النسخ الاحتياطي
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        
        self.temp_dir = Path("temp_backup")
        self.temp_dir.mkdir(exist_ok=True)
        
        # إعدادات النسخ الاحتياطي
        self.backup_settings = self._load_backup_settings()
        
        # خيط الجدولة
        self.scheduler_thread = None
        self.scheduler_running = False
        
        # قفل للعمليات المتزامنة
        self.backup_lock = threading.Lock()
    
    def _load_backup_settings(self) -> Dict:
        """تحميل إعدادات النسخ الاحتياطي"""
        default_settings = {
            'auto_backup_enabled': True,
            'backup_interval_hours': 24,
            'max_backup_files': 30,
            'compress_backups': True,
            'backup_location': str(self.backup_dir),
            'include_attachments': True,
            'backup_time': '02:00',  # 2:00 AM
            'backup_on_startup': False,
            'backup_on_shutdown': True
        }
        
        try:
            settings_file = Path("backup_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
        except Exception as e:
            self.logger.warning(f"فشل في تحميل إعدادات النسخ الاحتياطي: {str(e)}")
        
        return default_settings
    
    def save_backup_settings(self, settings: Dict) -> bool:
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            settings_file = Path("backup_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            self.backup_settings.update(settings)
            self.logger.info("تم حفظ إعدادات النسخ الاحتياطي")
            return True
            
        except Exception as e:
            self.logger.error(f"فشل في حفظ إعدادات النسخ الاحتياطي: {str(e)}")
            return False
    
    # ==================== النسخ الاحتياطي ====================
    
    def create_backup(self, backup_name: str = None, include_attachments: bool = True) -> Tuple[bool, str, str]:
        """
        إنشاء نسخة احتياطية
        
        Args:
            backup_name: اسم النسخة الاحتياطية (اختياري)
            include_attachments: تضمين المرفقات
            
        Returns:
            (نجح, رسالة, مسار الملف)
        """
        with self.backup_lock:
            try:
                # إنشاء اسم النسخة الاحتياطية
                if not backup_name:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"backup_{timestamp}"
                
                self.logger.info(f"بدء إنشاء النسخة الاحتياطية: {backup_name}")
                
                # إنشاء مجلد مؤقت للنسخة الاحتياطية
                temp_backup_path = self.temp_dir / backup_name
                temp_backup_path.mkdir(exist_ok=True)
                
                # نسخ قاعدة البيانات
                db_success, db_message = self._backup_database(temp_backup_path)
                if not db_success:
                    return False, f"فشل في نسخ قاعدة البيانات: {db_message}", ""
                
                # نسخ ملفات التكوين
                config_success, config_message = self._backup_config_files(temp_backup_path)
                if not config_success:
                    self.logger.warning(f"تحذير في نسخ ملفات التكوين: {config_message}")
                
                # نسخ المرفقات (إذا كانت موجودة)
                if include_attachments:
                    attachments_success, attachments_message = self._backup_attachments(temp_backup_path)
                    if not attachments_success:
                        self.logger.warning(f"تحذير في نسخ المرفقات: {attachments_message}")
                
                # إنشاء معلومات النسخة الاحتياطية
                backup_info = self._create_backup_info(backup_name, include_attachments)
                info_file = temp_backup_path / "backup_info.json"
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_info, f, ensure_ascii=False, indent=2, default=str)
                
                # ضغط النسخة الاحتياطية
                if self.backup_settings.get('compress_backups', True):
                    backup_file_path = self._compress_backup(temp_backup_path, backup_name)
                    # حذف المجلد المؤقت
                    shutil.rmtree(temp_backup_path)
                else:
                    # نقل المجلد إلى مجلد النسخ الاحتياطي
                    backup_file_path = self.backup_dir / backup_name
                    shutil.move(str(temp_backup_path), str(backup_file_path))
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups()
                
                self.logger.info(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_file_path}")
                return True, "تم إنشاء النسخة الاحتياطية بنجاح", str(backup_file_path)
                
            except Exception as e:
                self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
                
                # تنظيف الملفات المؤقتة
                try:
                    if temp_backup_path.exists():
                        shutil.rmtree(temp_backup_path)
                except:
                    pass
                
                return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", ""
    
    def _backup_database(self, backup_path: Path) -> Tuple[bool, str]:
        """نسخ قاعدة البيانات"""
        try:
            db_backup_path = backup_path / "database"
            db_backup_path.mkdir(exist_ok=True)
            
            if self.connection_manager.is_online and MYSQL_AVAILABLE:
                # نسخ قاعدة بيانات MySQL
                return self._backup_mysql_database(db_backup_path)
            else:
                # نسخ قاعدة بيانات SQLite
                return self._backup_sqlite_database(db_backup_path)
                
        except Exception as e:
            return False, str(e)
    
    def _backup_mysql_database(self, db_backup_path: Path) -> Tuple[bool, str]:
        """نسخ قاعدة بيانات MySQL"""
        try:
            # الحصول على إعدادات MySQL
            mysql_config = self.config_manager.get_mysql_config()
            
            # إنشاء اتصال MySQL
            connection = pymysql.connect(
                host=mysql_config['host'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                database=mysql_config['database'],
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            # نسخ كل جدول
            for table in tables:
                self._backup_mysql_table(cursor, table, db_backup_path)
            
            # نسخ هيكل قاعدة البيانات
            self._backup_mysql_structure(cursor, db_backup_path)
            
            cursor.close()
            connection.close()
            
            return True, "تم نسخ قاعدة بيانات MySQL بنجاح"
            
        except Exception as e:
            return False, f"خطأ في نسخ قاعدة بيانات MySQL: {str(e)}"
    
    def _backup_mysql_table(self, cursor, table_name: str, db_backup_path: Path):
        """نسخ جدول MySQL"""
        # نسخ البيانات
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [col[0] for col in cursor.fetchall()]
        
        # حفظ البيانات في ملف JSON
        table_data = {
            'table_name': table_name,
            'columns': columns,
            'rows': [dict(zip(columns, row)) for row in rows]
        }
        
        table_file = db_backup_path / f"{table_name}.json"
        with open(table_file, 'w', encoding='utf-8') as f:
            json.dump(table_data, f, ensure_ascii=False, indent=2, default=str)
    
    def _backup_mysql_structure(self, cursor, db_backup_path: Path):
        """نسخ هيكل قاعدة بيانات MySQL"""
        try:
            # نسخ هيكل الجداول
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            structure_data = {'tables': {}}
            
            for table in tables:
                cursor.execute(f"SHOW CREATE TABLE {table}")
                create_statement = cursor.fetchone()[1]
                structure_data['tables'][table] = create_statement
            
            # حفظ هيكل قاعدة البيانات
            structure_file = db_backup_path / "database_structure.json"
            with open(structure_file, 'w', encoding='utf-8') as f:
                json.dump(structure_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.warning(f"تحذير في نسخ هيكل قاعدة البيانات: {str(e)}")
    
    def _backup_sqlite_database(self, db_backup_path: Path) -> Tuple[bool, str]:
        """نسخ قاعدة بيانات SQLite"""
        try:
            sqlite_file = Path("data/transactions.db")
            if sqlite_file.exists():
                backup_sqlite_file = db_backup_path / "transactions.db"
                shutil.copy2(sqlite_file, backup_sqlite_file)
                
                # إنشاء نسخة نصية أيضاً
                self._export_sqlite_to_json(sqlite_file, db_backup_path)
                
                return True, "تم نسخ قاعدة بيانات SQLite بنجاح"
            else:
                return False, "ملف قاعدة بيانات SQLite غير موجود"
                
        except Exception as e:
            return False, f"خطأ في نسخ قاعدة بيانات SQLite: {str(e)}"
    
    def _export_sqlite_to_json(self, sqlite_file: Path, db_backup_path: Path):
        """تصدير SQLite إلى JSON"""
        try:
            conn = sqlite3.connect(sqlite_file)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            
            # تصدير كل جدول
            for table in tables:
                cursor.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                
                table_data = {
                    'table_name': table,
                    'rows': [dict(row) for row in rows]
                }
                
                table_file = db_backup_path / f"{table}.json"
                with open(table_file, 'w', encoding='utf-8') as f:
                    json.dump(table_data, f, ensure_ascii=False, indent=2, default=str)
            
            conn.close()
            
        except Exception as e:
            self.logger.warning(f"تحذير في تصدير SQLite إلى JSON: {str(e)}")
    
    def _backup_config_files(self, backup_path: Path) -> Tuple[bool, str]:
        """نسخ ملفات التكوين"""
        try:
            config_backup_path = backup_path / "config"
            config_backup_path.mkdir(exist_ok=True)
            
            # ملفات التكوين المطلوب نسخها
            config_files = [
                "config.ini",
                "backup_settings.json",
                "user_preferences.json"
            ]
            
            copied_files = 0
            for config_file in config_files:
                source_file = Path(config_file)
                if source_file.exists():
                    dest_file = config_backup_path / config_file
                    shutil.copy2(source_file, dest_file)
                    copied_files += 1
            
            return True, f"تم نسخ {copied_files} ملف تكوين"
            
        except Exception as e:
            return False, f"خطأ في نسخ ملفات التكوين: {str(e)}"
    
    def _backup_attachments(self, backup_path: Path) -> Tuple[bool, str]:
        """نسخ المرفقات"""
        try:
            attachments_dir = Path("attachments")
            if not attachments_dir.exists():
                return True, "لا توجد مرفقات للنسخ"
            
            attachments_backup_path = backup_path / "attachments"
            shutil.copytree(attachments_dir, attachments_backup_path)
            
            # حساب عدد الملفات المنسوخة
            file_count = sum(1 for _ in attachments_backup_path.rglob('*') if _.is_file())
            
            return True, f"تم نسخ {file_count} مرفق"
            
        except Exception as e:
            return False, f"خطأ في نسخ المرفقات: {str(e)}"
    
    def _create_backup_info(self, backup_name: str, include_attachments: bool) -> Dict:
        """إنشاء معلومات النسخة الاحتياطية"""
        return {
            'backup_name': backup_name,
            'created_at': datetime.now().isoformat(),
            'database_type': 'MySQL' if self.connection_manager.is_online else 'SQLite',
            'include_attachments': include_attachments,
            'application_version': '1.0.0',  # يمكن الحصول عليها من ملف التكوين
            'backup_size': 0,  # سيتم حسابها لاحقاً
            'checksum': '',    # سيتم حسابها لاحقاً
            'compressed': self.backup_settings.get('compress_backups', True)
        }
    
    def _compress_backup(self, backup_path: Path, backup_name: str) -> Path:
        """ضغط النسخة الاحتياطية"""
        zip_file_path = self.backup_dir / f"{backup_name}.zip"
        
        with zipfile.ZipFile(zip_file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in backup_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(backup_path)
                    zipf.write(file_path, arcname)
        
        # حساب checksum
        checksum = self._calculate_file_checksum(zip_file_path)
        
        # تحديث معلومات النسخة الاحتياطية
        info_file = backup_path / "backup_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            backup_info['backup_size'] = zip_file_path.stat().st_size
            backup_info['checksum'] = checksum
            
            # إعادة إضافة معلومات النسخة الاحتياطية إلى الملف المضغوط
            with zipfile.ZipFile(zip_file_path, 'a') as zipf:
                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2, default=str))
        
        return zip_file_path
    
    def _calculate_file_checksum(self, file_path: Path) -> str:
        """حساب checksum للملف"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            max_backups = self.backup_settings.get('max_backup_files', 30)
            
            # الحصول على قائمة النسخ الاحتياطية
            backup_files = []
            for file_path in self.backup_dir.iterdir():
                if file_path.is_file() and (file_path.suffix == '.zip' or file_path.name.startswith('backup_')):
                    backup_files.append((file_path, file_path.stat().st_mtime))
                elif file_path.is_dir() and file_path.name.startswith('backup_'):
                    backup_files.append((file_path, file_path.stat().st_mtime))
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # حذف النسخ الزائدة
            if len(backup_files) > max_backups:
                for file_path, _ in backup_files[max_backups:]:
                    try:
                        if file_path.is_file():
                            file_path.unlink()
                        else:
                            shutil.rmtree(file_path)
                        self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {file_path.name}")
                    except Exception as e:
                        self.logger.warning(f"فشل في حذف النسخة الاحتياطية القديمة {file_path.name}: {str(e)}")
                        
        except Exception as e:
            self.logger.warning(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")
    
    # ==================== الاستعادة ====================
    
    def restore_backup(self, backup_path: str, restore_database: bool = True, 
                      restore_config: bool = True, restore_attachments: bool = True) -> Tuple[bool, str]:
        """
        استعادة نسخة احتياطية
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            restore_database: استعادة قاعدة البيانات
            restore_config: استعادة ملفات التكوين
            restore_attachments: استعادة المرفقات
            
        Returns:
            (نجح, رسالة)
        """
        with self.backup_lock:
            try:
                backup_file = Path(backup_path)
                if not backup_file.exists():
                    return False, "ملف النسخة الاحتياطية غير موجود"
                
                self.logger.info(f"بدء استعادة النسخة الاحتياطية: {backup_file.name}")
                
                # إنشاء مجلد مؤقت للاستعادة
                temp_restore_path = self.temp_dir / f"restore_{int(time.time())}"
                temp_restore_path.mkdir(exist_ok=True)
                
                try:
                    # استخراج النسخة الاحتياطية
                    if backup_file.suffix == '.zip':
                        with zipfile.ZipFile(backup_file, 'r') as zipf:
                            zipf.extractall(temp_restore_path)
                    else:
                        # نسخ مجلد غير مضغوط
                        shutil.copytree(backup_file, temp_restore_path / backup_file.name)
                        temp_restore_path = temp_restore_path / backup_file.name
                    
                    # التحقق من صحة النسخة الاحتياطية
                    backup_info = self._validate_backup(temp_restore_path)
                    if not backup_info:
                        return False, "النسخة الاحتياطية تالفة أو غير صحيحة"
                    
                    # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
                    current_backup_success, current_backup_message, current_backup_path = self.create_backup(
                        f"before_restore_{int(time.time())}", True
                    )
                    if current_backup_success:
                        self.logger.info(f"تم إنشاء نسخة احتياطية من الحالة الحالية: {current_backup_path}")
                    
                    # استعادة قاعدة البيانات
                    if restore_database:
                        db_success, db_message = self._restore_database(temp_restore_path, backup_info)
                        if not db_success:
                            return False, f"فشل في استعادة قاعدة البيانات: {db_message}"
                    
                    # استعادة ملفات التكوين
                    if restore_config:
                        config_success, config_message = self._restore_config_files(temp_restore_path)
                        if not config_success:
                            self.logger.warning(f"تحذير في استعادة ملفات التكوين: {config_message}")
                    
                    # استعادة المرفقات
                    if restore_attachments:
                        attachments_success, attachments_message = self._restore_attachments(temp_restore_path)
                        if not attachments_success:
                            self.logger.warning(f"تحذير في استعادة المرفقات: {attachments_message}")
                    
                    self.logger.info("تم استعادة النسخة الاحتياطية بنجاح")
                    return True, "تم استعادة النسخة الاحتياطية بنجاح"
                    
                finally:
                    # تنظيف الملفات المؤقتة
                    try:
                        if temp_restore_path.exists():
                            shutil.rmtree(temp_restore_path)
                    except:
                        pass
                        
            except Exception as e:
                self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
                return False, f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
    
    def _validate_backup(self, backup_path: Path) -> Optional[Dict]:
        """التحقق من صحة النسخة الاحتياطية"""
        try:
            info_file = backup_path / "backup_info.json"
            if not info_file.exists():
                return None
            
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            # التحقق من وجود المجلدات المطلوبة
            required_dirs = ['database']
            for req_dir in required_dirs:
                if not (backup_path / req_dir).exists():
                    self.logger.warning(f"مجلد مطلوب مفقود: {req_dir}")
            
            return backup_info
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من صحة النسخة الاحتياطية: {str(e)}")
            return None
    
    def _restore_database(self, backup_path: Path, backup_info: Dict) -> Tuple[bool, str]:
        """استعادة قاعدة البيانات"""
        try:
            db_backup_path = backup_path / "database"
            if not db_backup_path.exists():
                return False, "مجلد قاعدة البيانات غير موجود في النسخة الاحتياطية"
            
            database_type = backup_info.get('database_type', 'SQLite')
            
            if database_type == 'MySQL' and MYSQL_AVAILABLE:
                return self._restore_mysql_database(db_backup_path)
            else:
                return self._restore_sqlite_database(db_backup_path)
                
        except Exception as e:
            return False, str(e)
    
    def _restore_mysql_database(self, db_backup_path: Path) -> Tuple[bool, str]:
        """استعادة قاعدة بيانات MySQL"""
        try:
            # الحصول على إعدادات MySQL
            mysql_config = self.config_manager.get_mysql_config()
            
            # إنشاء اتصال MySQL
            connection = pymysql.connect(
                host=mysql_config['host'],
                user=mysql_config['user'],
                password=mysql_config['password'],
                database=mysql_config['database'],
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            
            # استعادة هيكل قاعدة البيانات أولاً
            structure_file = db_backup_path / "database_structure.json"
            if structure_file.exists():
                with open(structure_file, 'r', encoding='utf-8') as f:
                    structure_data = json.load(f)
                
                # حذف الجداول الموجودة
                cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
                for table_name in structure_data['tables'].keys():
                    cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
                
                # إنشاء الجداول
                for table_name, create_statement in structure_data['tables'].items():
                    cursor.execute(create_statement)
                
                cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            
            # استعادة بيانات الجداول
            for json_file in db_backup_path.glob("*.json"):
                if json_file.name != "database_structure.json":
                    self._restore_mysql_table_data(cursor, json_file)
            
            connection.commit()
            cursor.close()
            connection.close()
            
            return True, "تم استعادة قاعدة بيانات MySQL بنجاح"
            
        except Exception as e:
            return False, f"خطأ في استعادة قاعدة بيانات MySQL: {str(e)}"
    
    def _restore_mysql_table_data(self, cursor, json_file: Path):
        """استعادة بيانات جدول MySQL"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                table_data = json.load(f)
            
            table_name = table_data['table_name']
            rows = table_data['rows']
            
            if rows:
                # حذف البيانات الموجودة
                cursor.execute(f"DELETE FROM {table_name}")
                
                # إدراج البيانات الجديدة
                columns = list(rows[0].keys())
                placeholders = ', '.join(['%s'] * len(columns))
                insert_query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                for row in rows:
                    values = [row[col] for col in columns]
                    cursor.execute(insert_query, values)
                    
        except Exception as e:
            self.logger.warning(f"خطأ في استعادة بيانات الجدول {json_file.name}: {str(e)}")
    
    def _restore_sqlite_database(self, db_backup_path: Path) -> Tuple[bool, str]:
        """استعادة قاعدة بيانات SQLite"""
        try:
            # البحث عن ملف SQLite
            sqlite_backup_file = db_backup_path / "transactions.db"
            
            if sqlite_backup_file.exists():
                # نسخ ملف SQLite
                sqlite_target = Path("data/transactions.db")
                sqlite_target.parent.mkdir(exist_ok=True)
                
                # إنشاء نسخة احتياطية من الملف الحالي
                if sqlite_target.exists():
                    backup_current = sqlite_target.with_suffix('.db.backup')
                    shutil.copy2(sqlite_target, backup_current)
                
                # نسخ الملف المستعاد
                shutil.copy2(sqlite_backup_file, sqlite_target)
                
                return True, "تم استعادة قاعدة بيانات SQLite بنجاح"
            else:
                # محاولة استعادة من ملفات JSON
                return self._restore_sqlite_from_json(db_backup_path)
                
        except Exception as e:
            return False, f"خطأ في استعادة قاعدة بيانات SQLite: {str(e)}"
    
    def _restore_sqlite_from_json(self, db_backup_path: Path) -> Tuple[bool, str]:
        """استعادة SQLite من ملفات JSON"""
        try:
            sqlite_target = Path("data/transactions.db")
            sqlite_target.parent.mkdir(exist_ok=True)
            
            # إنشاء قاعدة بيانات جديدة
            conn = sqlite3.connect(sqlite_target)
            cursor = conn.cursor()
            
            # قراءة ملفات JSON واستعادة البيانات
            for json_file in db_backup_path.glob("*.json"):
                if json_file.name != "database_structure.json":
                    with open(json_file, 'r', encoding='utf-8') as f:
                        table_data = json.load(f)
                    
                    table_name = table_data['table_name']
                    rows = table_data['rows']
                    
                    if rows:
                        # إنشاء الجدول (بشكل بسيط)
                        columns = list(rows[0].keys())
                        create_query = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join([f'{col} TEXT' for col in columns])})"
                        cursor.execute(create_query)
                        
                        # إدراج البيانات
                        placeholders = ', '.join(['?' for _ in columns])
                        insert_query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                        
                        for row in rows:
                            values = [row.get(col) for col in columns]
                            cursor.execute(insert_query, values)
            
            conn.commit()
            conn.close()
            
            return True, "تم استعادة قاعدة بيانات SQLite من JSON بنجاح"
            
        except Exception as e:
            return False, f"خطأ في استعادة SQLite من JSON: {str(e)}"
    
    def _restore_config_files(self, backup_path: Path) -> Tuple[bool, str]:
        """استعادة ملفات التكوين"""
        try:
            config_backup_path = backup_path / "config"
            if not config_backup_path.exists():
                return True, "لا توجد ملفات تكوين للاستعادة"
            
            restored_files = 0
            for config_file in config_backup_path.iterdir():
                if config_file.is_file():
                    target_file = Path(config_file.name)
                    
                    # إنشاء نسخة احتياطية من الملف الحالي
                    if target_file.exists():
                        backup_current = target_file.with_suffix(target_file.suffix + '.backup')
                        shutil.copy2(target_file, backup_current)
                    
                    # نسخ الملف المستعاد
                    shutil.copy2(config_file, target_file)
                    restored_files += 1
            
            return True, f"تم استعادة {restored_files} ملف تكوين"
            
        except Exception as e:
            return False, f"خطأ في استعادة ملفات التكوين: {str(e)}"
    
    def _restore_attachments(self, backup_path: Path) -> Tuple[bool, str]:
        """استعادة المرفقات"""
        try:
            attachments_backup_path = backup_path / "attachments"
            if not attachments_backup_path.exists():
                return True, "لا توجد مرفقات للاستعادة"
            
            attachments_target = Path("attachments")
            
            # إنشاء نسخة احتياطية من المرفقات الحالية
            if attachments_target.exists():
                backup_current = Path("attachments_backup")
                if backup_current.exists():
                    shutil.rmtree(backup_current)
                shutil.copytree(attachments_target, backup_current)
                shutil.rmtree(attachments_target)
            
            # نسخ المرفقات المستعادة
            shutil.copytree(attachments_backup_path, attachments_target)
            
            # حساب عدد الملفات المستعادة
            file_count = sum(1 for _ in attachments_target.rglob('*') if _.is_file())
            
            return True, f"تم استعادة {file_count} مرفق"
            
        except Exception as e:
            return False, f"خطأ في استعادة المرفقات: {str(e)}"
    
    # ==================== إدارة النسخ الاحتياطية ====================
    
    def get_backup_list(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backups = []
            
            for item in self.backup_dir.iterdir():
                if item.is_file() and item.suffix == '.zip':
                    backup_info = self._get_backup_info_from_zip(item)
                elif item.is_dir() and item.name.startswith('backup_'):
                    backup_info = self._get_backup_info_from_dir(item)
                else:
                    continue
                
                if backup_info:
                    backups.append(backup_info)
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            return backups
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {str(e)}")
            return []
    
    def _get_backup_info_from_zip(self, zip_file: Path) -> Optional[Dict]:
        """الحصول على معلومات النسخة الاحتياطية من ملف مضغوط"""
        try:
            with zipfile.ZipFile(zip_file, 'r') as zipf:
                if 'backup_info.json' in zipf.namelist():
                    with zipf.open('backup_info.json') as f:
                        backup_info = json.load(f)
                else:
                    # إنشاء معلومات أساسية
                    backup_info = {
                        'backup_name': zip_file.stem,
                        'created_at': datetime.fromtimestamp(zip_file.stat().st_mtime).isoformat(),
                        'database_type': 'Unknown',
                        'compressed': True
                    }
            
            # إضافة معلومات الملف
            backup_info['file_path'] = str(zip_file)
            backup_info['file_size'] = zip_file.stat().st_size
            backup_info['file_size_mb'] = round(backup_info['file_size'] / (1024 * 1024), 2)
            
            return backup_info
            
        except Exception as e:
            self.logger.warning(f"خطأ في قراءة معلومات النسخة الاحتياطية {zip_file.name}: {str(e)}")
            return None
    
    def _get_backup_info_from_dir(self, backup_dir: Path) -> Optional[Dict]:
        """الحصول على معلومات النسخة الاحتياطية من مجلد"""
        try:
            info_file = backup_dir / "backup_info.json"
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
            else:
                # إنشاء معلومات أساسية
                backup_info = {
                    'backup_name': backup_dir.name,
                    'created_at': datetime.fromtimestamp(backup_dir.stat().st_mtime).isoformat(),
                    'database_type': 'Unknown',
                    'compressed': False
                }
            
            # إضافة معلومات المجلد
            backup_info['file_path'] = str(backup_dir)
            
            # حساب حجم المجلد
            total_size = sum(f.stat().st_size for f in backup_dir.rglob('*') if f.is_file())
            backup_info['file_size'] = total_size
            backup_info['file_size_mb'] = round(total_size / (1024 * 1024), 2)
            
            return backup_info
            
        except Exception as e:
            self.logger.warning(f"خطأ في قراءة معلومات النسخة الاحتياطية {backup_dir.name}: {str(e)}")
            return None
    
    def delete_backup(self, backup_path: str) -> Tuple[bool, str]:
        """حذف نسخة احتياطية"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False, "النسخة الاحتياطية غير موجودة"
            
            if backup_file.is_file():
                backup_file.unlink()
            else:
                shutil.rmtree(backup_file)
            
            self.logger.info(f"تم حذف النسخة الاحتياطية: {backup_file.name}")
            return True, "تم حذف النسخة الاحتياطية بنجاح"
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
            return False, f"خطأ في حذف النسخة الاحتياطية: {str(e)}"

    # ==================== الجدولة التلقائية ====================

    def start_scheduler(self):
        """بدء جدولة النسخ الاحتياطي التلقائي"""
        if not SCHEDULE_AVAILABLE:
            self.logger.warning("مكتبة schedule غير متوفرة - الجدولة التلقائية معطلة")
            return

        if self.scheduler_running:
            return

        try:
            # إعداد الجدولة
            schedule.clear()

            if self.backup_settings.get('auto_backup_enabled', True):
                backup_time = self.backup_settings.get('backup_time', '02:00')
                schedule.every().day.at(backup_time).do(self._scheduled_backup)

                self.logger.info(f"تم تفعيل النسخ الاحتياطي التلقائي في {backup_time}")

            # بدء خيط الجدولة
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()

        except Exception as e:
            self.logger.error(f"خطأ في بدء جدولة النسخ الاحتياطي: {str(e)}")

    def stop_scheduler(self):
        """إيقاف جدولة النسخ الاحتياطي التلقائي"""
        self.scheduler_running = False

        if SCHEDULE_AVAILABLE:
            schedule.clear()

        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)

        self.logger.info("تم إيقاف جدولة النسخ الاحتياطي التلقائي")

    def _run_scheduler(self):
        """تشغيل جدولة النسخ الاحتياطي"""
        if not SCHEDULE_AVAILABLE:
            return

        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                self.logger.error(f"خطأ في جدولة النسخ الاحتياطي: {str(e)}")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ

    def _scheduled_backup(self):
        """تنفيذ النسخ الاحتياطي المجدول"""
        try:
            self.logger.info("بدء النسخ الاحتياطي المجدول")

            backup_name = f"scheduled_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            include_attachments = self.backup_settings.get('include_attachments', True)

            success, message, backup_path = self.create_backup(backup_name, include_attachments)

            if success:
                self.logger.info(f"تم النسخ الاحتياطي المجدول بنجاح: {backup_path}")
            else:
                self.logger.error(f"فشل النسخ الاحتياطي المجدول: {message}")

        except Exception as e:
            self.logger.error(f"خطأ في النسخ الاحتياطي المجدول: {str(e)}")

    def backup_on_startup(self):
        """نسخ احتياطي عند بدء التشغيل"""
        if self.backup_settings.get('backup_on_startup', False):
            try:
                self.logger.info("بدء النسخ الاحتياطي عند بدء التشغيل")

                backup_name = f"startup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                success, message, backup_path = self.create_backup(backup_name, True)

                if success:
                    self.logger.info(f"تم النسخ الاحتياطي عند بدء التشغيل: {backup_path}")
                else:
                    self.logger.warning(f"فشل النسخ الاحتياطي عند بدء التشغيل: {message}")

            except Exception as e:
                self.logger.error(f"خطأ في النسخ الاحتياطي عند بدء التشغيل: {str(e)}")

    def backup_on_shutdown(self):
        """نسخ احتياطي عند إغلاق التطبيق"""
        if self.backup_settings.get('backup_on_shutdown', True):
            try:
                self.logger.info("بدء النسخ الاحتياطي عند إغلاق التطبيق")

                backup_name = f"shutdown_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                success, message, backup_path = self.create_backup(backup_name, True)

                if success:
                    self.logger.info(f"تم النسخ الاحتياطي عند إغلاق التطبيق: {backup_path}")
                else:
                    self.logger.warning(f"فشل النسخ الاحتياطي عند إغلاق التطبيق: {message}")

            except Exception as e:
                self.logger.error(f"خطأ في النسخ الاحتياطي عند إغلاق التطبيق: {str(e)}")

    # ==================== إحصائيات النسخ الاحتياطي ====================

    def get_backup_statistics(self) -> Dict:
        """الحصول على إحصائيات النسخ الاحتياطي"""
        try:
            backups = self.get_backup_list()

            if not backups:
                return {
                    'total_backups': 0,
                    'total_size_mb': 0,
                    'latest_backup': None,
                    'oldest_backup': None,
                    'average_size_mb': 0,
                    'backup_frequency': 'غير محدد'
                }

            # حساب الإحصائيات
            total_size = sum(backup.get('file_size', 0) for backup in backups)
            total_size_mb = round(total_size / (1024 * 1024), 2)
            average_size_mb = round(total_size_mb / len(backups), 2)

            # أحدث وأقدم نسخة احتياطية
            latest_backup = backups[0] if backups else None
            oldest_backup = backups[-1] if backups else None

            # حساب تكرار النسخ الاحتياطي
            backup_frequency = self._calculate_backup_frequency(backups)

            return {
                'total_backups': len(backups),
                'total_size_mb': total_size_mb,
                'latest_backup': latest_backup,
                'oldest_backup': oldest_backup,
                'average_size_mb': average_size_mb,
                'backup_frequency': backup_frequency,
                'auto_backup_enabled': self.backup_settings.get('auto_backup_enabled', True),
                'backup_time': self.backup_settings.get('backup_time', '02:00'),
                'max_backup_files': self.backup_settings.get('max_backup_files', 30)
            }

        except Exception as e:
            self.logger.error(f"خطأ في حساب إحصائيات النسخ الاحتياطي: {str(e)}")
            return {}

    def _calculate_backup_frequency(self, backups: List[Dict]) -> str:
        """حساب تكرار النسخ الاحتياطي"""
        try:
            if len(backups) < 2:
                return "غير محدد"

            # حساب الفترات بين النسخ الاحتياطية
            intervals = []
            for i in range(len(backups) - 1):
                try:
                    date1 = datetime.fromisoformat(backups[i]['created_at'].replace('Z', '+00:00'))
                    date2 = datetime.fromisoformat(backups[i + 1]['created_at'].replace('Z', '+00:00'))
                    interval = abs((date1 - date2).total_seconds() / 3600)  # بالساعات
                    intervals.append(interval)
                except:
                    continue

            if not intervals:
                return "غير محدد"

            # حساب متوسط الفترة
            avg_interval = sum(intervals) / len(intervals)

            if avg_interval < 2:
                return "كل ساعة"
            elif avg_interval < 12:
                return f"كل {int(avg_interval)} ساعات"
            elif avg_interval < 36:
                return "يومياً"
            elif avg_interval < 168:
                return f"كل {int(avg_interval / 24)} أيام"
            else:
                return f"كل {int(avg_interval / 168)} أسابيع"

        except Exception as e:
            self.logger.warning(f"خطأ في حساب تكرار النسخ الاحتياطي: {str(e)}")
            return "غير محدد"

    # ==================== التحقق من سلامة النسخ الاحتياطية ====================

    def verify_backup_integrity(self, backup_path: str) -> Tuple[bool, str, Dict]:
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False, "النسخة الاحتياطية غير موجودة", {}

            verification_results = {
                'file_exists': True,
                'file_readable': False,
                'backup_info_valid': False,
                'database_files_exist': False,
                'checksum_valid': False,
                'structure_valid': False
            }

            # التحقق من قابلية القراءة
            try:
                if backup_file.suffix == '.zip':
                    with zipfile.ZipFile(backup_file, 'r') as zipf:
                        zipf.testzip()  # اختبار سلامة الملف المضغوط
                        verification_results['file_readable'] = True

                        # التحقق من وجود ملف معلومات النسخة الاحتياطية
                        if 'backup_info.json' in zipf.namelist():
                            verification_results['backup_info_valid'] = True

                            # قراءة معلومات النسخة الاحتياطية
                            with zipf.open('backup_info.json') as f:
                                backup_info = json.load(f)

                                # التحقق من checksum إذا كان متوفراً
                                if 'checksum' in backup_info:
                                    current_checksum = self._calculate_file_checksum(backup_file)
                                    verification_results['checksum_valid'] = (
                                        current_checksum == backup_info['checksum']
                                    )

                        # التحقق من وجود ملفات قاعدة البيانات
                        db_files = [f for f in zipf.namelist() if f.startswith('database/')]
                        verification_results['database_files_exist'] = len(db_files) > 0

                        # التحقق من هيكل النسخة الاحتياطية
                        required_dirs = ['database/']
                        structure_valid = all(
                            any(f.startswith(req_dir) for f in zipf.namelist())
                            for req_dir in required_dirs
                        )
                        verification_results['structure_valid'] = structure_valid

                else:
                    # مجلد غير مضغوط
                    verification_results['file_readable'] = True

                    # التحقق من ملف معلومات النسخة الاحتياطية
                    info_file = backup_file / "backup_info.json"
                    verification_results['backup_info_valid'] = info_file.exists()

                    # التحقق من مجلد قاعدة البيانات
                    db_dir = backup_file / "database"
                    verification_results['database_files_exist'] = db_dir.exists()

                    # التحقق من الهيكل
                    verification_results['structure_valid'] = db_dir.exists()

            except Exception as e:
                return False, f"خطأ في قراءة النسخة الاحتياطية: {str(e)}", verification_results

            # تقييم النتائج
            all_checks_passed = all(verification_results.values())
            critical_checks = ['file_readable', 'database_files_exist', 'structure_valid']
            critical_checks_passed = all(verification_results[check] for check in critical_checks)

            if all_checks_passed:
                return True, "النسخة الاحتياطية سليمة تماماً", verification_results
            elif critical_checks_passed:
                return True, "النسخة الاحتياطية سليمة مع تحذيرات بسيطة", verification_results
            else:
                return False, "النسخة الاحتياطية تالفة أو غير مكتملة", verification_results

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من سلامة النسخة الاحتياطية: {str(e)}")
            return False, f"خطأ في التحقق من سلامة النسخة الاحتياطية: {str(e)}", {}

    def repair_backup_settings(self) -> bool:
        """إصلاح إعدادات النسخ الاحتياطي"""
        try:
            # إعادة تحميل الإعدادات الافتراضية
            self.backup_settings = self._load_backup_settings()

            # إنشاء مجلدات النسخ الاحتياطي إذا لم تكن موجودة
            self.backup_dir.mkdir(exist_ok=True)
            self.temp_dir.mkdir(exist_ok=True)

            # حفظ الإعدادات
            self.save_backup_settings(self.backup_settings)

            self.logger.info("تم إصلاح إعدادات النسخ الاحتياطي")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في إصلاح إعدادات النسخ الاحتياطي: {str(e)}")
            return False

    # ==================== تنظيف وصيانة ====================

    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            if self.temp_dir.exists():
                for item in self.temp_dir.iterdir():
                    try:
                        if item.is_file():
                            item.unlink()
                        else:
                            shutil.rmtree(item)
                    except Exception as e:
                        self.logger.warning(f"فشل في حذف الملف المؤقت {item.name}: {str(e)}")

                self.logger.info("تم تنظيف الملفات المؤقتة")

        except Exception as e:
            self.logger.warning(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")

    def get_backup_settings(self) -> Dict:
        """الحصول على إعدادات النسخ الاحتياطي"""
        return self.backup_settings.copy()

    def update_backup_settings(self, new_settings: Dict) -> bool:
        """تحديث إعدادات النسخ الاحتياطي"""
        try:
            # التحقق من صحة الإعدادات
            if 'backup_interval_hours' in new_settings:
                if not isinstance(new_settings['backup_interval_hours'], (int, float)) or new_settings['backup_interval_hours'] <= 0:
                    return False

            if 'max_backup_files' in new_settings:
                if not isinstance(new_settings['max_backup_files'], int) or new_settings['max_backup_files'] <= 0:
                    return False

            # تحديث الإعدادات
            self.backup_settings.update(new_settings)

            # حفظ الإعدادات
            success = self.save_backup_settings(self.backup_settings)

            if success:
                # إعادة تشغيل الجدولة إذا تغيرت إعدادات التوقيت
                if any(key in new_settings for key in ['auto_backup_enabled', 'backup_time']):
                    self.stop_scheduler()
                    self.start_scheduler()

            return success

        except Exception as e:
            self.logger.error(f"خطأ في تحديث إعدادات النسخ الاحتياطي: {str(e)}")
            return False
