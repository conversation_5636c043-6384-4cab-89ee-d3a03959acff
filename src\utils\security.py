# -*- coding: utf-8 -*-
"""
أدوات الأمان
Security Utilities

أدوات مساعدة للأمان والتشفير
"""

import hashlib
import secrets
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta

class SecurityUtils:
    """أدوات الأمان"""
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, any]:
        """
        التحقق من قوة كلمة المرور
        
        Args:
            password: كلمة المرور
            
        Returns:
            نتيجة التحقق مع التفاصيل
        """
        result = {
            'is_valid': False,
            'score': 0,
            'issues': [],
            'suggestions': []
        }
        
        if not password:
            result['issues'].append('كلمة المرور فارغة')
            return result
        
        # طول كلمة المرور
        if len(password) < 6:
            result['issues'].append('كلمة المرور قصيرة جداً (أقل من 6 أحرف)')
        el<PERSON>(password) >= 8:
            result['score'] += 2
        else:
            result['score'] += 1
        
        # وجود أحرف كبيرة
        if re.search(r'[A-Z]', password):
            result['score'] += 1
        else:
            result['suggestions'].append('أضف أحرف كبيرة')
        
        # وجود أحرف صغيرة
        if re.search(r'[a-z]', password):
            result['score'] += 1
        else:
            result['suggestions'].append('أضف أحرف صغيرة')
        
        # وجود أرقام
        if re.search(r'\d', password):
            result['score'] += 1
        else:
            result['suggestions'].append('أضف أرقام')
        
        # وجود رموز خاصة
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result['score'] += 1
        else:
            result['suggestions'].append('أضف رموز خاصة (!@#$%^&*)')
        
        # تجنب التكرار
        if not re.search(r'(.)\1{2,}', password):
            result['score'] += 1
        else:
            result['issues'].append('تجنب تكرار الأحرف أكثر من مرتين')
        
        # كلمات مرور شائعة
        common_passwords = [
            '123456', 'password', '123456789', '12345678', '12345',
            '1234567', '1234567890', 'qwerty', 'abc123', 'admin'
        ]
        
        if password.lower() in common_passwords:
            result['issues'].append('كلمة المرور شائعة جداً')
            result['score'] = max(0, result['score'] - 2)
        
        # تحديد مستوى القوة
        if result['score'] >= 5 and len(result['issues']) == 0:
            result['is_valid'] = True
        
        return result
    
    @staticmethod
    def generate_secure_password(length: int = 12) -> str:
        """
        إنشاء كلمة مرور آمنة
        
        Args:
            length: طول كلمة المرور
            
        Returns:
            كلمة المرور المُنشأة
        """
        # مجموعات الأحرف
        lowercase = 'abcdefghijklmnopqrstuvwxyz'
        uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
        digits = '0123456789'
        special = '!@#$%^&*()_+-=[]{}|;:,.<>?'
        
        # ضمان وجود حرف واحد على الأقل من كل مجموعة
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]
        
        # إكمال باقي الطول
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # خلط الأحرف
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    @staticmethod
    def hash_data(data: str, salt: str = None) -> Dict[str, str]:
        """
        تشفير البيانات باستخدام SHA-256
        
        Args:
            data: البيانات المراد تشفيرها
            salt: الملح (اختياري)
            
        Returns:
            البيانات المشفرة مع الملح
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # دمج البيانات مع الملح
        salted_data = data + salt
        
        # تشفير البيانات
        hash_object = hashlib.sha256(salted_data.encode('utf-8'))
        hashed_data = hash_object.hexdigest()
        
        return {
            'hash': hashed_data,
            'salt': salt
        }
    
    @staticmethod
    def verify_hash(data: str, stored_hash: str, salt: str) -> bool:
        """
        التحقق من صحة البيانات المشفرة
        
        Args:
            data: البيانات الأصلية
            stored_hash: البيانات المشفرة المحفوظة
            salt: الملح
            
        Returns:
            True إذا كانت البيانات صحيحة
        """
        result = SecurityUtils.hash_data(data, salt)
        return result['hash'] == stored_hash
    
    @staticmethod
    def sanitize_input(input_text: str) -> str:
        """
        تنظيف النص المدخل من الأحرف الضارة
        
        Args:
            input_text: النص المدخل
            
        Returns:
            النص المنظف
        """
        if not input_text:
            return ""
        
        # إزالة الأحرف الضارة
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\n', '\r', '\t']
        cleaned_text = input_text
        
        for char in dangerous_chars:
            cleaned_text = cleaned_text.replace(char, '')
        
        # تحديد الطول الأقصى
        max_length = 1000
        if len(cleaned_text) > max_length:
            cleaned_text = cleaned_text[:max_length]
        
        return cleaned_text.strip()
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """
        التحقق من صحة البريد الإلكتروني
        
        Args:
            email: البريد الإلكتروني
            
        Returns:
            True إذا كان البريد صحيحاً
        """
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        التحقق من صحة رقم الهاتف
        
        Args:
            phone: رقم الهاتف
            
        Returns:
            True إذا كان الرقم صحيحاً
        """
        if not phone:
            return False
        
        # إزالة المسافات والرموز
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # التحقق من الطول والتنسيق
        patterns = [
            r'^\+?[1-9]\d{1,14}$',  # تنسيق دولي
            r'^0\d{9,10}$',         # تنسيق محلي
            r'^\d{10,11}$'          # تنسيق بسيط
        ]
        
        return any(re.match(pattern, cleaned_phone) for pattern in patterns)

class SessionManager:
    """مدير الجلسات"""
    
    def __init__(self):
        self.active_sessions = {}
    
    def create_session(self, user_id: int, session_data: Dict) -> str:
        """
        إنشاء جلسة جديدة
        
        Args:
            user_id: معرف المستخدم
            session_data: بيانات الجلسة
            
        Returns:
            رمز الجلسة
        """
        session_token = secrets.token_urlsafe(32)
        
        self.active_sessions[session_token] = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'data': session_data
        }
        
        return session_token
    
    def get_session(self, session_token: str) -> Optional[Dict]:
        """
        الحصول على بيانات الجلسة
        
        Args:
            session_token: رمز الجلسة
            
        Returns:
            بيانات الجلسة أو None
        """
        return self.active_sessions.get(session_token)
    
    def update_session_activity(self, session_token: str):
        """
        تحديث نشاط الجلسة
        
        Args:
            session_token: رمز الجلسة
        """
        if session_token in self.active_sessions:
            self.active_sessions[session_token]['last_activity'] = datetime.now()
    
    def destroy_session(self, session_token: str):
        """
        إنهاء الجلسة
        
        Args:
            session_token: رمز الجلسة
        """
        if session_token in self.active_sessions:
            del self.active_sessions[session_token]
    
    def cleanup_expired_sessions(self, timeout_minutes: int = 60):
        """
        تنظيف الجلسات المنتهية الصلاحية
        
        Args:
            timeout_minutes: مهلة انتهاء الجلسة بالدقائق
        """
        current_time = datetime.now()
        expired_sessions = []
        
        for token, session in self.active_sessions.items():
            last_activity = session['last_activity']
            if (current_time - last_activity).total_seconds() > (timeout_minutes * 60):
                expired_sessions.append(token)
        
        for token in expired_sessions:
            del self.active_sessions[token]
    
    def get_active_sessions_count(self) -> int:
        """الحصول على عدد الجلسات النشطة"""
        return len(self.active_sessions)
    
    def get_user_sessions(self, user_id: int) -> List[str]:
        """
        الحصول على جلسات مستخدم معين
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            قائمة برموز الجلسات
        """
        user_sessions = []
        for token, session in self.active_sessions.items():
            if session['user_id'] == user_id:
                user_sessions.append(token)
        
        return user_sessions
