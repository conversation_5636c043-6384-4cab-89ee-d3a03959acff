# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window

النافذة الرئيسية للتطبيق مع نظام التنقل
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QStackedWidget, QMenuBar, QMenu, QStatusBar,
                            QToolBar, QLabel, QPushButton, QFrame, QSplitter,
                            QTreeWidget, QTreeWidgetItem, QMessageBox, QDialog)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

from services.auth_service import AuthService
from services.permission_service import PermissionService
from ui.dashboard_widget import DashboardWidget
from ui.transactions_list_widget import TransactionsListWidget
from ui.advanced_search_main_widget import AdvancedSearchMainWidget
from ui.admin_dashboard_widget import AdminDashboardWidget
from ui.reports_widget import ReportsWidget
from ui.backup_widget import BackupWidget
from utils.logger import Logger

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارات مخصصة
    user_logged_out = pyqtSignal()
    
    def __init__(self, config_manager, connection_manager, auth_service, parent=None):
        super().__init__(parent)
        
        self.config = config_manager
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.permission_service = PermissionService()
        self.logger = Logger(__name__)
        
        # متغيرات النافذة
        self.current_user = None
        self.current_widget = None
        
        # إعداد النافذة
        self.setup_window()
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_connections()
        
        # تحديث واجهة المستخدم
        self.update_user_interface()
        
        # بدء مؤقت تحديث الحالة
        self.start_status_timer()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.setWindowTitle(self.config.app_name)
        self.setMinimumSize(1000, 700)
        self.resize(self.config.window_width, self.config.window_height)
        
        # تعيين اتجاه النافذة
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تعيين أيقونة النافذة
        try:
            icon = QIcon("assets/images/app_icon.png")
            if not icon.isNull():
                self.setWindowIcon(icon)
        except:
            pass
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # إنشاء المقسم الرئيسي
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # إعداد الشريط الجانبي
        self.setup_sidebar()
        
        # إعداد المنطقة الرئيسية
        self.setup_main_area()
        
        # تعيين أحجام المقسم
        self.main_splitter.setSizes([250, 750])
        self.main_splitter.setCollapsible(0, False)  # منع إخفاء الشريط الجانبي
    
    def setup_sidebar(self):
        """إعداد الشريط الجانبي"""
        # إطار الشريط الجانبي
        self.sidebar_frame = QFrame()
        self.sidebar_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        self.sidebar_frame.setMaximumWidth(300)
        self.sidebar_frame.setMinimumWidth(200)
        
        sidebar_layout = QVBoxLayout(self.sidebar_frame)
        sidebar_layout.setContentsMargins(10, 10, 10, 10)
        sidebar_layout.setSpacing(10)
        
        # معلومات المستخدم
        self.setup_user_info(sidebar_layout)
        
        # قائمة التنقل
        self.setup_navigation_tree(sidebar_layout)
        
        # معلومات الاتصال
        self.setup_connection_info(sidebar_layout)
        
        self.main_splitter.addWidget(self.sidebar_frame)
    
    def setup_user_info(self, layout):
        """إعداد معلومات المستخدم"""
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.Shape.Box)
        user_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        user_layout = QVBoxLayout(user_frame)
        user_layout.setSpacing(5)
        
        # صورة المستخدم (رمز افتراضي)
        self.user_avatar = QLabel("👤")
        self.user_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.user_avatar.setStyleSheet("font-size: 32px; margin: 5px;")
        user_layout.addWidget(self.user_avatar)
        
        # اسم المستخدم
        self.user_name_label = QLabel("غير محدد")
        self.user_name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.user_name_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        user_layout.addWidget(self.user_name_label)
        
        # دور المستخدم
        self.user_role_label = QLabel("غير محدد")
        self.user_role_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.user_role_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        user_layout.addWidget(self.user_role_label)
        
        # زر تسجيل الخروج
        self.logout_button = QPushButton("تسجيل الخروج")
        self.logout_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        user_layout.addWidget(self.logout_button)
        
        layout.addWidget(user_frame)
    
    def setup_navigation_tree(self, layout):
        """إعداد شجرة التنقل"""
        nav_label = QLabel("التنقل")
        nav_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px 0 5px 0;")
        layout.addWidget(nav_label)
        
        self.navigation_tree = QTreeWidget()
        self.navigation_tree.setHeaderHidden(True)
        self.navigation_tree.setRootIsDecorated(True)
        self.navigation_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #007bff;
                selection-color: white;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f8f9fa;
            }
            QTreeWidget::item:hover {
                background-color: #f8f9fa;
            }
            QTreeWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        layout.addWidget(self.navigation_tree)
    
    def setup_connection_info(self, layout):
        """إعداد معلومات الاتصال"""
        connection_frame = QFrame()
        connection_frame.setFrameStyle(QFrame.Shape.Box)
        connection_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                margin-top: 10px;
            }
        """)
        
        connection_layout = QVBoxLayout(connection_frame)
        connection_layout.setSpacing(3)
        
        # حالة الاتصال
        self.connection_status_label = QLabel("🔗 جاري التحقق...")
        self.connection_status_label.setStyleSheet("font-size: 12px;")
        connection_layout.addWidget(self.connection_status_label)
        
        # نمط التشغيل
        self.mode_label = QLabel("النمط: غير محدد")
        self.mode_label.setStyleSheet("font-size: 11px; color: #6c757d;")
        connection_layout.addWidget(self.mode_label)
        
        layout.addWidget(connection_frame)
    
    def setup_main_area(self):
        """إعداد المنطقة الرئيسية"""
        # إطار المحتوى الرئيسي
        self.main_content_frame = QFrame()
        self.main_content_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        
        main_content_layout = QVBoxLayout(self.main_content_frame)
        main_content_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط العنوان
        self.setup_content_header(main_content_layout)
        
        # منطقة المحتوى المتغير
        self.content_stack = QStackedWidget()
        main_content_layout.addWidget(self.content_stack)
        
        # إضافة لوحة التحكم الافتراضية
        self.dashboard_widget = DashboardWidget(
            self.config, self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.dashboard_widget)

        # إضافة ويدجت إدارة المعاملات
        self.transactions_widget = TransactionsListWidget(
            self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.transactions_widget)

        # إضافة ويدجت البحث المتقدم
        self.advanced_search_widget = AdvancedSearchMainWidget(
            self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.advanced_search_widget)

        # إضافة لوحة تحكم المدير
        self.admin_dashboard_widget = AdminDashboardWidget(
            self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.admin_dashboard_widget)

        # إضافة ويدجت التقارير
        self.reports_widget = ReportsWidget(
            self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.reports_widget)

        # إضافة ويدجت النسخ الاحتياطي
        self.backup_widget = BackupWidget(
            self.connection_manager, self.auth_service
        )
        self.content_stack.addWidget(self.backup_widget)
        
        self.main_splitter.addWidget(self.main_content_frame)
    
    def setup_content_header(self, layout):
        """إعداد شريط عنوان المحتوى"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.Box)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # عنوان الصفحة
        self.page_title_label = QLabel("لوحة التحكم")
        self.page_title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(self.page_title_label)
        
        header_layout.addStretch()
        
        # معلومات إضافية
        self.page_info_label = QLabel("")
        self.page_info_label.setStyleSheet("font-size: 12px; color: #6c757d;")
        header_layout.addWidget(self.page_info_label)
        
        layout.addWidget(header_frame)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # تسجيل الخروج
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.setShortcut("Ctrl+Q")
        logout_action.triggered.connect(self.handle_logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Alt+F4")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_current_view)
        view_menu.addAction(refresh_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        # حول
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setIcon(QIcon("assets/images/refresh.png"))
        refresh_action.triggered.connect(self.refresh_current_view)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # مزامنة
        sync_action = QAction("مزامنة", self)
        sync_action.setIcon(QIcon("assets/images/sync.png"))
        sync_action.triggered.connect(self.sync_data)
        toolbar.addAction(sync_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # رسالة الحالة
        self.status_message = QLabel("جاهز")
        self.status_bar.addWidget(self.status_message)
        
        self.status_bar.addPermanentWidget(QLabel("|"))
        
        # حالة الاتصال
        self.status_connection = QLabel("غير متصل")
        self.status_bar.addPermanentWidget(self.status_connection)
        
        self.status_bar.addPermanentWidget(QLabel("|"))
        
        # الوقت
        self.status_time = QLabel("")
        self.status_bar.addPermanentWidget(self.status_time)
    
    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        # أحداث التنقل
        self.navigation_tree.itemClicked.connect(self.handle_navigation_click)
        
        # تسجيل الخروج
        self.logout_button.clicked.connect(self.handle_logout)
    
    def update_user_interface(self):
        """تحديث واجهة المستخدم حسب المستخدم الحالي"""
        current_user = self.auth_service.get_current_user()
        
        if current_user:
            self.current_user = current_user
            
            # تحديث معلومات المستخدم
            self.user_name_label.setText(current_user.get('full_name', 'غير محدد'))
            
            role_text = "مدير" if current_user.get('permission') == 'admin' else "مستخدم"
            self.user_role_label.setText(role_text)
            
            # تحديث قائمة التنقل
            self.update_navigation_menu()
            
            # تحديث عنوان النافذة
            self.setWindowTitle(f"{self.config.app_name} - {current_user.get('full_name')}")
        
        # تحديث حالة الاتصال
        self.update_connection_status()
    
    def update_navigation_menu(self):
        """تحديث قائمة التنقل حسب صلاحيات المستخدم"""
        self.navigation_tree.clear()
        
        if not self.current_user:
            return
        
        user_role = self.current_user.get('permission', 'user')
        menu_items = self.permission_service.get_accessible_menu_items(user_role)
        
        for item in menu_items:
            tree_item = QTreeWidgetItem([item['title']])
            tree_item.setData(0, Qt.ItemDataRole.UserRole, item)
            
            # إضافة أيقونة إذا كانت متوفرة
            if 'icon' in item:
                # يمكن إضافة الأيقونات هنا
                pass
            
            # إضافة القوائم الفرعية
            if 'submenu' in item:
                for subitem in item['submenu']:
                    sub_tree_item = QTreeWidgetItem([subitem['title']])
                    sub_tree_item.setData(0, Qt.ItemDataRole.UserRole, subitem)
                    tree_item.addChild(sub_tree_item)
            
            self.navigation_tree.addTopLevelItem(tree_item)
        
        # توسيع جميع العناصر
        self.navigation_tree.expandAll()
    
    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        try:
            if self.connection_manager.is_online:
                # متصل
                self.connection_status_label.setText("🟢 متصل بالخادم")
                self.connection_status_label.setStyleSheet("color: #28a745; font-size: 12px;")
                self.mode_label.setText("النمط: متصل (MySQL)")
                self.status_connection.setText("متصل")
                self.status_connection.setStyleSheet("color: #28a745;")
            else:
                # غير متصل
                self.connection_status_label.setText("🔴 النمط المحلي")
                self.connection_status_label.setStyleSheet("color: #dc3545; font-size: 12px;")
                self.mode_label.setText("النمط: محلي (SQLite)")
                self.status_connection.setText("محلي")
                self.status_connection.setStyleSheet("color: #ffc107;")
        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة الاتصال: {str(e)}")
    
    def start_status_timer(self):
        """بدء مؤقت تحديث الحالة"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # كل ثانية
    
    def update_status(self):
        """تحديث شريط الحالة"""
        from datetime import datetime
        
        # تحديث الوقت
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.status_time.setText(current_time)
        
        # تحديث حالة الاتصال كل 30 ثانية
        if hasattr(self, '_last_connection_check'):
            if (datetime.now() - self._last_connection_check).seconds >= 30:
                self.update_connection_status()
                self._last_connection_check = datetime.now()
        else:
            self._last_connection_check = datetime.now()
    
    def handle_navigation_click(self, item, column):
        """معالجة النقر على عنصر التنقل"""
        item_data = item.data(0, Qt.ItemDataRole.UserRole)
        
        if item_data and 'url' in item_data:
            self.navigate_to_page(item_data['id'], item_data['title'])
    
    def navigate_to_page(self, page_id: str, page_title: str):
        """التنقل إلى صفحة معينة"""
        self.page_title_label.setText(page_title)
        self.status_message.setText(f"تم التنقل إلى: {page_title}")

        # التنقل الفعلي حسب معرف الصفحة
        if page_id == 'dashboard':
            self.content_stack.setCurrentWidget(self.dashboard_widget)
        elif page_id == 'view_transactions':
            self.content_stack.setCurrentWidget(self.transactions_widget)
        elif page_id == 'create_transaction':
            self.content_stack.setCurrentWidget(self.transactions_widget)
            # فتح نافذة إضافة معاملة جديدة
            self.transactions_widget.add_transaction()
        elif page_id == 'advanced_search':
            self.content_stack.setCurrentWidget(self.advanced_search_widget)
        elif page_id == 'admin_dashboard':
            self.content_stack.setCurrentWidget(self.admin_dashboard_widget)
        elif page_id == 'user_management':
            self.content_stack.setCurrentWidget(self.admin_dashboard_widget)
            # التبديل إلى تبويب إدارة المستخدمين
            if hasattr(self.admin_dashboard_widget, 'tabs'):
                self.admin_dashboard_widget.tabs.setCurrentIndex(1)
        elif page_id == 'lookup_management':
            self.content_stack.setCurrentWidget(self.admin_dashboard_widget)
            # التبديل إلى تبويب الجداول المساعدة
            if hasattr(self.admin_dashboard_widget, 'tabs'):
                self.admin_dashboard_widget.tabs.setCurrentIndex(2)
        elif page_id == 'reports':
            self.content_stack.setCurrentWidget(self.reports_widget)
        elif page_id == 'backup':
            self.content_stack.setCurrentWidget(self.backup_widget)
        else:
            # الصفحات الأخرى قيد التطوير
            self.logger.info(f"الصفحة {page_id} قيد التطوير")

        self.logger.info(f"تم التنقل إلى الصفحة: {page_id}")
    
    def handle_logout(self):
        """معالجة تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تأكيد تسجيل الخروج",
            "هل أنت متأكد من رغبتك في تسجيل الخروج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.auth_service.logout()
            self.user_logged_out.emit()
            self.close()
    
    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        self.status_message.setText("تم التحديث")
        self.update_connection_status()
        
        # تحديث الويدجت الحالي إذا كان يدعم التحديث
        current_widget = self.content_stack.currentWidget()
        if hasattr(current_widget, 'refresh'):
            current_widget.refresh()
    
    def sync_data(self):
        """مزامنة البيانات"""
        self.status_message.setText("جاري المزامنة...")
        # هنا يمكن إضافة منطق المزامنة
        self.logger.info("بدء عملية المزامنة")
    
    def show_about_dialog(self):
        """عرض نافذة حول البرنامج"""
        about_text = f"""
        <h2>{self.config.app_name}</h2>
        <p><b>الإصدار:</b> {self.config.get('APPLICATION', 'app_version', '1.0.0')}</p>
        <p><b>الوصف:</b> نظام متكامل لمتابعة المراسلات والمعاملات</p>
        <p><b>المطور:</b> Augment Agent</p>
        <p><b>التاريخ:</b> 2025</p>
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق التطبيق؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.logger.info("إغلاق التطبيق")

            # تنفيذ النسخ الاحتياطي عند الإغلاق إذا كان مفعلاً
            try:
                if hasattr(self, 'backup_widget') and self.backup_widget.backup_service:
                    self.backup_widget.backup_service.backup_on_shutdown()
                    self.backup_widget.backup_service.stop_scheduler()
            except Exception as e:
                self.logger.warning(f"خطأ في النسخ الاحتياطي عند الإغلاق: {str(e)}")

            # إيقاف المؤقت
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()

            event.accept()
        else:
            event.ignore()
