# -*- coding: utf-8 -*-
"""
خدمة التصدير
Export Service

تدير تصدير التقارير إلى Excel وPDF
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import os
from pathlib import Path
import json

try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfutils
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

from utils.logger import Logger

class ExportService:
    """خدمة التصدير"""
    
    def __init__(self):
        """تهيئة خدمة التصدير"""
        self.logger = Logger(__name__)
        
        # إعداد مجلد التصدير
        self.exports_dir = Path("exports")
        self.exports_dir.mkdir(exist_ok=True)
        
        # إعداد الخطوط العربية للـ PDF
        self._setup_arabic_fonts()
    
    def _setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            if PDF_AVAILABLE:
                # محاولة تسجيل خط عربي
                font_path = Path("assets/fonts/NotoSansArabic-Regular.ttf")
                if font_path.exists():
                    pdfmetrics.registerFont(TTFont('Arabic', str(font_path)))
                    self.arabic_font = 'Arabic'
                else:
                    self.arabic_font = 'Helvetica'  # خط احتياطي
            else:
                self.arabic_font = 'Helvetica'
        except Exception as e:
            self.logger.warning(f"تعذر إعداد الخطوط العربية: {str(e)}")
            self.arabic_font = 'Helvetica'
    
    # ==================== تصدير Excel ====================
    
    def export_to_excel(self, report_data: Dict, filename: str = None) -> str:
        """
        تصدير التقرير إلى Excel
        
        Args:
            report_data: بيانات التقرير
            filename: اسم الملف (اختياري)
            
        Returns:
            مسار الملف المُصدر
        """
        if not EXCEL_AVAILABLE:
            raise ImportError("مكتبة openpyxl غير متوفرة. قم بتثبيتها باستخدام: pip install openpyxl")
        
        try:
            # إنشاء اسم الملف
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"report_{timestamp}.xlsx"
            
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            filepath = self.exports_dir / filename
            
            # إنشاء ملف Excel
            workbook = openpyxl.Workbook()
            
            # حذف الورقة الافتراضية
            workbook.remove(workbook.active)
            
            # إضافة أوراق حسب نوع التقرير
            report_title = report_data.get('title', 'تقرير')
            
            if 'transactions' in report_data:
                self._add_transactions_sheet(workbook, report_data)
            
            if 'statistics' in report_data:
                self._add_statistics_sheet(workbook, report_data)
            
            if 'monthly_data' in report_data:
                self._add_monthly_sheet(workbook, report_data)
            
            if 'users_performance' in report_data:
                self._add_performance_sheet(workbook, report_data)
            
            if 'status_statistics' in report_data:
                self._add_status_sheet(workbook, report_data)
            
            if 'visa_statistics' in report_data:
                self._add_visa_sheet(workbook, report_data)
            
            # إضافة ورقة الملخص
            self._add_summary_sheet(workbook, report_data)
            
            # حفظ الملف
            workbook.save(filepath)
            
            self.logger.info(f"تم تصدير التقرير إلى Excel: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير Excel: {str(e)}")
            raise
    
    def _add_transactions_sheet(self, workbook, report_data):
        """إضافة ورقة المعاملات"""
        sheet = workbook.create_sheet("المعاملات")
        
        # العناوين
        headers = [
            "رقم الوارد", "تاريخ الوارد", "الموضوع", "نوع التأشيرة",
            "الحالة", "الأولوية", "الباحث", "تاريخ الاستحقاق", "تاريخ الإنجاز"
        ]
        
        # إضافة العناوين
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # إضافة البيانات
        transactions = report_data.get('transactions', [])
        for row, transaction in enumerate(transactions, 2):
            sheet.cell(row=row, column=1, value=transaction.get('head_incoming_no', ''))
            sheet.cell(row=row, column=2, value=transaction.get('head_incoming_date', ''))
            sheet.cell(row=row, column=3, value=transaction.get('subject', ''))
            sheet.cell(row=row, column=4, value=transaction.get('visa_type', ''))
            sheet.cell(row=row, column=5, value=transaction.get('request_status', ''))
            sheet.cell(row=row, column=6, value=transaction.get('priority', ''))
            sheet.cell(row=row, column=7, value=transaction.get('researcher_1_name', ''))
            sheet.cell(row=row, column=8, value=transaction.get('due_date', ''))
            sheet.cell(row=row, column=9, value=transaction.get('completion_date', ''))
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            sheet.column_dimensions[get_column_letter(col)].width = 15
    
    def _add_statistics_sheet(self, workbook, report_data):
        """إضافة ورقة الإحصائيات"""
        sheet = workbook.create_sheet("الإحصائيات")
        
        statistics = report_data.get('statistics', {})
        
        # عنوان الورقة
        sheet.cell(row=1, column=1, value="الإحصائيات العامة").font = Font(size=16, bold=True)
        
        row = 3
        for key, value in statistics.items():
            sheet.cell(row=row, column=1, value=self._translate_stat_key(key))
            sheet.cell(row=row, column=2, value=value)
            row += 1
        
        # تنسيق
        sheet.column_dimensions['A'].width = 25
        sheet.column_dimensions['B'].width = 15
    
    def _add_monthly_sheet(self, workbook, report_data):
        """إضافة ورقة البيانات الشهرية"""
        sheet = workbook.create_sheet("البيانات الشهرية")
        
        # العناوين
        headers = ["الشهر", "إجمالي المعاملات", "المكتملة", "المعلقة", "معدل الإنجاز %"]
        
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        
        # البيانات
        monthly_data = report_data.get('monthly_data', [])
        for row, month_data in enumerate(monthly_data, 2):
            sheet.cell(row=row, column=1, value=month_data.get('month_name', ''))
            sheet.cell(row=row, column=2, value=month_data.get('total', 0))
            sheet.cell(row=row, column=3, value=month_data.get('completed', 0))
            sheet.cell(row=row, column=4, value=month_data.get('pending', 0))
            sheet.cell(row=row, column=5, value=month_data.get('completion_rate', 0))
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            sheet.column_dimensions[get_column_letter(col)].width = 18
    
    def _add_performance_sheet(self, workbook, report_data):
        """إضافة ورقة الأداء"""
        sheet = workbook.create_sheet("تقرير الأداء")
        
        # العناوين
        headers = ["المستخدم", "إجمالي المعاملات", "المكتملة", "معدل الإنجاز %", "متوسط أيام الإنجاز"]
        
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E2EFDA", end_color="E2EFDA", fill_type="solid")
        
        # البيانات
        users_performance = report_data.get('users_performance', [])
        for row, user_perf in enumerate(users_performance, 2):
            user = user_perf.get('user', {})
            sheet.cell(row=row, column=1, value=user.get('full_name', ''))
            sheet.cell(row=row, column=2, value=user_perf.get('total', 0))
            sheet.cell(row=row, column=3, value=user_perf.get('completed', 0))
            sheet.cell(row=row, column=4, value=user_perf.get('completion_rate', 0))
            sheet.cell(row=row, column=5, value=user_perf.get('avg_processing_days', 0))
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            sheet.column_dimensions[get_column_letter(col)].width = 20
    
    def _add_status_sheet(self, workbook, report_data):
        """إضافة ورقة الحالات"""
        sheet = workbook.create_sheet("تقرير الحالات")
        
        # العناوين
        headers = ["الحالة", "العدد", "النسبة %", "متوسط وقت المعالجة"]
        
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="FCE4D6", end_color="FCE4D6", fill_type="solid")
        
        # البيانات
        status_statistics = report_data.get('status_statistics', [])
        for row, status_stat in enumerate(status_statistics, 2):
            sheet.cell(row=row, column=1, value=status_stat.get('status', ''))
            sheet.cell(row=row, column=2, value=status_stat.get('count', 0))
            sheet.cell(row=row, column=3, value=status_stat.get('percentage', 0))
            sheet.cell(row=row, column=4, value=status_stat.get('avg_processing_time', 0))
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            sheet.column_dimensions[get_column_letter(col)].width = 20
    
    def _add_visa_sheet(self, workbook, report_data):
        """إضافة ورقة أنواع التأشيرات"""
        sheet = workbook.create_sheet("أنواع التأشيرات")
        
        # العناوين
        headers = ["نوع التأشيرة", "العدد", "النسبة %", "المكتملة", "معدل الإنجاز %"]
        
        for col, header in enumerate(headers, 1):
            cell = sheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="EDEDED", end_color="EDEDED", fill_type="solid")
        
        # البيانات
        visa_statistics = report_data.get('visa_statistics', [])
        for row, visa_stat in enumerate(visa_statistics, 2):
            sheet.cell(row=row, column=1, value=visa_stat.get('visa_type', ''))
            sheet.cell(row=row, column=2, value=visa_stat.get('count', 0))
            sheet.cell(row=row, column=3, value=visa_stat.get('percentage', 0))
            sheet.cell(row=row, column=4, value=visa_stat.get('completed', 0))
            sheet.cell(row=row, column=5, value=visa_stat.get('completion_rate', 0))
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            sheet.column_dimensions[get_column_letter(col)].width = 20
    
    def _add_summary_sheet(self, workbook, report_data):
        """إضافة ورقة الملخص"""
        sheet = workbook.create_sheet("الملخص")
        workbook.active = sheet  # جعل ورقة الملخص هي الافتراضية
        
        # عنوان التقرير
        title = report_data.get('title', 'تقرير')
        sheet.cell(row=1, column=1, value=title).font = Font(size=18, bold=True)
        
        # تاريخ الإنشاء
        generated_at = report_data.get('generated_at', '')
        if generated_at:
            try:
                dt = datetime.fromisoformat(generated_at.replace('Z', '+00:00'))
                formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                sheet.cell(row=2, column=1, value=f"تاريخ الإنشاء: {formatted_date}")
            except:
                sheet.cell(row=2, column=1, value=f"تاريخ الإنشاء: {generated_at}")
        
        # معلومات التقرير
        row = 4
        
        # إحصائيات عامة
        if 'statistics' in report_data:
            sheet.cell(row=row, column=1, value="الإحصائيات العامة:").font = Font(bold=True)
            row += 1
            
            stats = report_data['statistics']
            for key, value in stats.items():
                sheet.cell(row=row, column=1, value=f"  {self._translate_stat_key(key)}: {value}")
                row += 1
            row += 1
        
        # ملخص إضافي
        if 'summary' in report_data:
            sheet.cell(row=row, column=1, value="الملخص:").font = Font(bold=True)
            row += 1
            
            summary = report_data['summary']
            for key, value in summary.items():
                sheet.cell(row=row, column=1, value=f"  {self._translate_stat_key(key)}: {value}")
                row += 1
        
        # تنسيق العمود
        sheet.column_dimensions['A'].width = 40
    
    # ==================== تصدير PDF ====================
    
    def export_to_pdf(self, report_data: Dict, filename: str = None) -> str:
        """
        تصدير التقرير إلى PDF
        
        Args:
            report_data: بيانات التقرير
            filename: اسم الملف (اختياري)
            
        Returns:
            مسار الملف المُصدر
        """
        if not PDF_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متوفرة. قم بتثبيتها باستخدام: pip install reportlab")
        
        try:
            # إنشاء اسم الملف
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"report_{timestamp}.pdf"
            
            if not filename.endswith('.pdf'):
                filename += '.pdf'
            
            filepath = self.exports_dir / filename
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # وسط
                fontName=self.arabic_font
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                fontName=self.arabic_font
            )
            
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                fontName=self.arabic_font
            )
            
            # عنوان التقرير
            title = report_data.get('title', 'تقرير')
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))
            
            # تاريخ الإنشاء
            generated_at = report_data.get('generated_at', '')
            if generated_at:
                try:
                    dt = datetime.fromisoformat(generated_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                    story.append(Paragraph(f"Generated: {formatted_date}", normal_style))
                except:
                    story.append(Paragraph(f"Generated: {generated_at}", normal_style))
                story.append(Spacer(1, 12))
            
            # إضافة المحتوى حسب نوع التقرير
            if 'statistics' in report_data:
                self._add_pdf_statistics(story, report_data['statistics'], heading_style, normal_style)
            
            if 'monthly_data' in report_data:
                self._add_pdf_monthly_data(story, report_data['monthly_data'], heading_style, normal_style)
            
            if 'users_performance' in report_data:
                self._add_pdf_performance_data(story, report_data['users_performance'], heading_style, normal_style)
            
            if 'status_statistics' in report_data:
                self._add_pdf_status_data(story, report_data['status_statistics'], heading_style, normal_style)
            
            if 'visa_statistics' in report_data:
                self._add_pdf_visa_data(story, report_data['visa_statistics'], heading_style, normal_style)
            
            # بناء المستند
            doc.build(story)
            
            self.logger.info(f"تم تصدير التقرير إلى PDF: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير PDF: {str(e)}")
            raise
    
    def _add_pdf_statistics(self, story, statistics, heading_style, normal_style):
        """إضافة الإحصائيات للـ PDF"""
        story.append(Paragraph("Statistics", heading_style))
        
        # إنشاء جدول الإحصائيات
        data = [['Statistic', 'Value']]
        for key, value in statistics.items():
            data.append([self._translate_stat_key(key), str(value)])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
    
    def _add_pdf_monthly_data(self, story, monthly_data, heading_style, normal_style):
        """إضافة البيانات الشهرية للـ PDF"""
        story.append(Paragraph("Monthly Data", heading_style))
        
        # إنشاء جدول البيانات الشهرية
        data = [['Month', 'Total', 'Completed', 'Pending', 'Completion Rate %']]
        for month_data in monthly_data:
            data.append([
                month_data.get('month_name', ''),
                str(month_data.get('total', 0)),
                str(month_data.get('completed', 0)),
                str(month_data.get('pending', 0)),
                str(month_data.get('completion_rate', 0))
            ])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
    
    def _add_pdf_performance_data(self, story, users_performance, heading_style, normal_style):
        """إضافة بيانات الأداء للـ PDF"""
        story.append(Paragraph("Performance Data", heading_style))
        
        # إنشاء جدول الأداء
        data = [['User', 'Total', 'Completed', 'Completion Rate %', 'Avg Days']]
        for user_perf in users_performance[:10]:  # أول 10 مستخدمين
            user = user_perf.get('user', {})
            data.append([
                user.get('full_name', ''),
                str(user_perf.get('total', 0)),
                str(user_perf.get('completed', 0)),
                str(user_perf.get('completion_rate', 0)),
                str(user_perf.get('avg_processing_days', 0))
            ])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.green),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
    
    def _add_pdf_status_data(self, story, status_statistics, heading_style, normal_style):
        """إضافة بيانات الحالات للـ PDF"""
        story.append(Paragraph("Status Statistics", heading_style))
        
        # إنشاء جدول الحالات
        data = [['Status', 'Count', 'Percentage %', 'Avg Processing Time']]
        for status_stat in status_statistics:
            data.append([
                status_stat.get('status', ''),
                str(status_stat.get('count', 0)),
                str(status_stat.get('percentage', 0)),
                str(status_stat.get('avg_processing_time', 0))
            ])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.orange),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightyellow),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
    
    def _add_pdf_visa_data(self, story, visa_statistics, heading_style, normal_style):
        """إضافة بيانات أنواع التأشيرات للـ PDF"""
        story.append(Paragraph("Visa Types Statistics", heading_style))
        
        # إنشاء جدول أنواع التأشيرات
        data = [['Visa Type', 'Count', 'Percentage %', 'Completed', 'Completion Rate %']]
        for visa_stat in visa_statistics:
            data.append([
                visa_stat.get('visa_type', ''),
                str(visa_stat.get('count', 0)),
                str(visa_stat.get('percentage', 0)),
                str(visa_stat.get('completed', 0)),
                str(visa_stat.get('completion_rate', 0))
            ])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lavender),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
    
    # ==================== وظائف مساعدة ====================
    
    def _translate_stat_key(self, key: str) -> str:
        """ترجمة مفاتيح الإحصائيات"""
        translations = {
            'total': 'الإجمالي',
            'completed': 'المكتملة',
            'pending': 'المعلقة',
            'overdue': 'المتأخرة',
            'completion_rate': 'معدل الإنجاز %',
            'avg_processing_days': 'متوسط أيام المعالجة',
            'total_users': 'إجمالي المستخدمين',
            'total_transactions': 'إجمالي المعاملات',
            'most_common_status': 'الحالة الأكثر شيوعاً',
            'most_requested_type': 'النوع الأكثر طلباً',
            'top_performer': 'أفضل أداء',
            'overall_completion_rate': 'معدل الإنجاز العام'
        }
        return translations.get(key, key)
    
    def get_export_formats(self) -> List[Dict]:
        """الحصول على صيغ التصدير المتاحة"""
        formats = []
        
        if EXCEL_AVAILABLE:
            formats.append({
                'id': 'excel',
                'name': 'Excel (.xlsx)',
                'description': 'تصدير إلى ملف Excel مع أوراق متعددة',
                'icon': '📊',
                'extension': '.xlsx'
            })
        
        if PDF_AVAILABLE:
            formats.append({
                'id': 'pdf',
                'name': 'PDF (.pdf)',
                'description': 'تصدير إلى ملف PDF للطباعة والمشاركة',
                'icon': '📄',
                'extension': '.pdf'
            })
        
        # JSON متاح دائماً
        formats.append({
            'id': 'json',
            'name': 'JSON (.json)',
            'description': 'تصدير البيانات الخام بصيغة JSON',
            'icon': '📋',
            'extension': '.json'
        })
        
        return formats
    
    def export_to_json(self, report_data: Dict, filename: str = None) -> str:
        """
        تصدير التقرير إلى JSON
        
        Args:
            report_data: بيانات التقرير
            filename: اسم الملف (اختياري)
            
        Returns:
            مسار الملف المُصدر
        """
        try:
            # إنشاء اسم الملف
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"report_{timestamp}.json"
            
            if not filename.endswith('.json'):
                filename += '.json'
            
            filepath = self.exports_dir / filename
            
            # حفظ البيانات
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"تم تصدير التقرير إلى JSON: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير JSON: {str(e)}")
            raise
