
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة المراسلات والمعاملات (الوارد والصادر)
IOTS - Incoming/Outgoing Transactions System

الملف الرئيسي لتطبيق سطح المكتب
Main Desktop Application Entry Point

المطور: Augment Agent
التاريخ: 2025-08-05
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt6.QtCore import Qt, QTranslator, QLocale
from PyQt6.QtGui import QIcon, QFont

from utils.config_manager import ConfigManager
from utils.logger import setup_logging
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from database.sqlite_only_manager import SQLiteOnlyManager as ConnectionManager
from services.auth_service import AuthService

class IOTSApplication:
    """
    الفئة الرئيسية لتطبيق نظام متابعة المراسلات والمعاملات
    """
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.app = None
        self.config = None
        self.connection_manager = None
        self.auth_service = None
        self.login_window = None
        self.main_window = None
        
    def initialize(self):
        """تهيئة جميع مكونات التطبيق"""
        try:
            print("بدء تهيئة التطبيق...")

            # إنشاء تطبيق Qt
            self.app = QApplication(sys.argv)
            print("✅ تم إنشاء تطبيق Qt")

            # تحميل التكوين
            self.config = ConfigManager()
            print("✅ تم تحميل التكوين")

            # إعداد نظام السجلات
            setup_logging(self.config)
            logging.info("تم بدء تشغيل التطبيق")
            print("✅ تم إعداد نظام السجلات")

            # إعداد الخط والاتجاه للعربية
            self.setup_arabic_support()
            print("✅ تم إعداد دعم العربية")

            # إعداد أيقونة التطبيق
            self.setup_application_icon()

            # تهيئة مدير الاتصال بقاعدة البيانات
            try:
                print("إنشاء مدير الاتصال...")
                self.connection_manager = ConnectionManager(self.config)
                logging.info("تم إنشاء مدير الاتصال بنجاح")
                print("✅ تم إنشاء مدير الاتصال")

                # عرض حالة الاتصال
                if self.connection_manager.is_online:
                    print("🌐 النمط: MySQL (متصل)")
                    logging.info("التطبيق يعمل في النمط المتصل")
                else:
                    print("💾 النمط: SQLite (محلي)")
                    print("💡 للاتصال بخادم MySQL:")
                    print("   1. ثبت XAMPP من: https://www.apachefriends.org/")
                    print("   2. شغل MySQL من XAMPP Control Panel")
                    print("   3. شغل: python setup_mysql.py")
                    logging.info("التطبيق يعمل في النمط المحلي")

            except Exception as e:
                logging.error(f"خطأ في إنشاء مدير الاتصال: {str(e)}")
                print(f"❌ خطأ في إنشاء مدير الاتصال: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

            # تهيئة خدمة المصادقة
            try:
                print("إنشاء خدمة المصادقة...")
                self.auth_service = AuthService(self.connection_manager, self.config)
                logging.info("تم إنشاء خدمة المصادقة بنجاح")
                print("✅ تم إنشاء خدمة المصادقة")
            except Exception as e:
                logging.error(f"خطأ في إنشاء خدمة المصادقة: {str(e)}")
                print(f"❌ خطأ في إنشاء خدمة المصادقة: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

            # اختبار الاتصال بقاعدة البيانات
            try:
                if self.connection_manager.test_connection():
                    logging.info("تم الاتصال بقاعدة البيانات بنجاح")
                    print("✅ تم الاتصال بقاعدة البيانات")
                else:
                    logging.warning("فشل الاتصال بقاعدة البيانات")
                    print("⚠️ فشل الاتصال بقاعدة البيانات")
            except Exception as e:
                logging.error(f"خطأ في اختبار الاتصال: {str(e)}")
                print(f"⚠️ خطأ في اختبار الاتصال: {str(e)}")
                # لا نرفع الاستثناء هنا لأن التطبيق يمكن أن يعمل بدون اتصال

            print("✅ تم إكمال التهيئة بنجاح")
            return True

        except Exception as e:
            error_msg = f"خطأ في تهيئة التطبيق: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_error_message("خطأ في التهيئة", error_msg)
            return False
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية والاتجاه من اليمين لليسار"""
        try:
            # تعيين اتجاه التطبيق
            self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            
            # تحميل خط عربي
            font_path = os.path.join("assets", "fonts", "Cairo-Regular.ttf")
            if os.path.exists(font_path):
                font_id = QApplication.addApplicationFont(font_path)
                if font_id != -1:
                    font_family = QApplication.fontFamilies(font_id)[0]
                    font = QFont(font_family, 10)
                    self.app.setFont(font)
            else:
                # استخدام خط افتراضي يدعم العربية
                font = QFont("Segoe UI", 10)
                self.app.setFont(font)
                
        except Exception as e:
            logging.warning(f"تحذير: لم يتم تحميل الخط العربي: {str(e)}")
    
    def setup_application_icon(self):
        """إعداد أيقونة التطبيق"""
        try:
            icon_path = os.path.join("assets", "images", "app_icon.png")
            if os.path.exists(icon_path):
                self.app.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            logging.warning(f"تحذير: لم يتم تحميل أيقونة التطبيق: {str(e)}")
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            print("عرض نافذة تسجيل الدخول...")
            self.login_window = LoginWindow(self.config, self.connection_manager)
            print("✅ تم إنشاء نافذة تسجيل الدخول")

            # ربط إشارة نجاح تسجيل الدخول
            self.login_window.accepted.connect(self.on_login_success)
            print("✅ تم ربط إشارات نافذة تسجيل الدخول")

            # عرض النافذة
            print("🚀 عرض نافذة تسجيل الدخول...")
            result = self.login_window.exec()
            return result == QDialog.DialogCode.Accepted

        except Exception as e:
            error_msg = f"خطأ في عرض نافذة تسجيل الدخول: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_error_message("خطأ", error_msg)
            return False

    def on_login_success(self):
        """معالجة نجاح تسجيل الدخول"""
        try:
            # إنشاء وعرض النافذة الرئيسية
            self.main_window = MainWindow(
                self.config,
                self.connection_manager,
                self.auth_service
            )

            # ربط إشارة تسجيل الخروج
            self.main_window.user_logged_out.connect(self.on_logout)

            # عرض النافذة الرئيسية
            self.main_window.show()

        except Exception as e:
            logging.error(f"خطأ في عرض النافذة الرئيسية: {str(e)}")
            self.show_error_message("خطأ", f"حدث خطأ أثناء عرض النافذة الرئيسية:\n{str(e)}")

    def on_logout(self):
        """معالجة تسجيل الخروج"""
        try:
            # إغلاق النافذة الرئيسية
            if self.main_window:
                self.main_window.close()
                self.main_window = None

            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login_window()

        except Exception as e:
            logging.error(f"خطأ في معالجة تسجيل الخروج: {str(e)}")
    
    def show_error_message(self, title, message):
        """عرض رسالة خطأ"""
        if self.app:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            msg_box.exec()
        else:
            print(f"خطأ: {title} - {message}")
    
    def run(self):
        """تشغيل التطبيق"""
        if not self.initialize():
            return 1
        
        if not self.show_login_window():
            return 1
        
        # تشغيل حلقة الأحداث الرئيسية
        return self.app.exec()

def main():
    """الدالة الرئيسية"""
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'data', 'reports', 'temp']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    # إنشاء وتشغيل التطبيق
    iots_app = IOTSApplication()
    return iots_app.run()

if __name__ == "__main__":
    sys.exit(main())

