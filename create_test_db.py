#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات SQLite تجريبية للاختبار
"""

import sqlite3
import os
from pathlib import Path

def create_test_database():
    """إنشاء قاعدة بيانات SQLite تجريبية"""
    
    # إنشاء مجلد data إذا لم يكن موجوداً
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    # مسار قاعدة البيانات
    db_path = data_dir / "transactions.db"
    
    # إنشاء الاتصال
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # إنشاء جدول تجريبي
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS test_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # إدراج بيانات تجريبية
    test_data = [
        ("معاملة تجريبية 1", "وصف المعاملة الأولى", "completed"),
        ("معاملة تجريبية 2", "وصف المعاملة الثانية", "pending"),
        ("معاملة تجريبية 3", "وصف المعاملة الثالثة", "in_progress"),
    ]
    
    cursor.executemany(
        "INSERT INTO test_transactions (title, description, status) VALUES (?, ?, ?)",
        test_data
    )
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"تم إنشاء قاعدة البيانات التجريبية: {db_path}")
    print(f"حجم الملف: {os.path.getsize(db_path)} بايت")

if __name__ == "__main__":
    create_test_database()
