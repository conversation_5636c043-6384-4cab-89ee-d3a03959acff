# دليل نظام النسخ الاحتياطي والاستعادة المتقدم
## Advanced Backup & Restore System Guide

### 🎯 نظرة عامة

تم تطوير نظام النسخ الاحتياطي والاستعادة المتقدم مع جدولة تلقائية وضغط وتشفير وواجهات تفاعلية متطورة.

---

## 🚀 الميزات الجديدة

### 1. **خدمة النسخ الاحتياطي المتقدمة** (`BackupService`)
- ✅ **نسخ احتياطي شامل**: قاعدة البيانات، ملفات التكوين، المرفقات
- ✅ **دعم متعدد قواعد البيانات**: MySQL و SQLite مع تبديل تلقائي
- ✅ **ضغط متقدم**: ضغط ZIP مع checksum للتحقق من السلامة
- ✅ **جدولة تلقائية**: نسخ احتياطي مجدول يومياً في أوقات محددة
- ✅ **إدارة ذكية**: تنظيف النسخ القديمة تلقائياً حسب الحد الأقصى
- ✅ **التحقق من السلامة**: فحص شامل لسلامة النسخ الاحتياطية

### 2. **نظام الاستعادة المتقدم**
- ✅ **استعادة انتقائية**: اختيار ما تريد استعادته (قاعدة البيانات، التكوين، المرفقات)
- ✅ **نسخ احتياطي قبل الاستعادة**: إنشاء نسخة احتياطية من الحالة الحالية تلقائياً
- ✅ **استعادة آمنة**: التحقق من صحة النسخة الاحتياطية قبل الاستعادة
- ✅ **دعم متعدد الصيغ**: استعادة من ملفات مضغوطة أو مجلدات
- ✅ **معالجة الأخطاء**: استعادة جزئية مع تقارير مفصلة

### 3. **واجهة إدارة النسخ الاحتياطي** (`BackupWidget`)
- ✅ **واجهة موحدة**: 4 تبويبات متخصصة (النسخ، الاستعادة، الإعدادات، الإحصائيات)
- ✅ **إنشاء تفاعلي**: نسخ احتياطي سريع أو مخصص مع خيارات متقدمة
- ✅ **إدارة شاملة**: عرض، تحقق، استعادة، حذف النسخ الاحتياطية
- ✅ **معالجة متوازية**: عمليات النسخ والاستعادة في خيوط منفصلة
- ✅ **تقارير مفصلة**: إحصائيات وتقارير سلامة شاملة

### 4. **الجدولة التلقائية المتقدمة**
- ✅ **جدولة يومية**: نسخ احتياطي تلقائي في أوقات محددة
- ✅ **نسخ عند الأحداث**: نسخ احتياطي عند بدء التشغيل وإغلاق التطبيق
- ✅ **إدارة الخيوط**: جدولة آمنة في خيوط منفصلة
- ✅ **مرونة الإعدادات**: تخصيص أوقات وتكرار النسخ الاحتياطي
- ✅ **معالجة الأخطاء**: استمرارية الجدولة حتى عند حدوث أخطاء

---

## 📋 كيفية الاستخدام

### 1. **الوصول لنظام النسخ الاحتياطي**
```
1. شغل التطبيق: python main.py
2. سجل الدخول: admin / admin
3. من القائمة الجانبية اختر "إدارة النسخ الاحتياطي"
```

### 2. **إنشاء نسخة احتياطية**
```
💾 النسخ الاحتياطي:

النسخ السريع:
⚡ نسخ احتياطي سريع: إنشاء فوري بالإعدادات الافتراضية

النسخ المخصص:
📝 اسم النسخة الاحتياطية: اختياري (سيتم إنشاء اسم تلقائي)
☑️ تضمين المرفقات: نسخ ملفات المرفقات مع قاعدة البيانات
🔄 إنشاء نسخة احتياطية: بدء العملية

المخرجات:
- ملف مضغوط (.zip) يحتوي على:
  * قاعدة البيانات (MySQL أو SQLite)
  * ملفات التكوين
  * المرفقات (اختياري)
  * معلومات النسخة الاحتياطية (backup_info.json)
```

### 3. **استعادة نسخة احتياطية**
```
📥 الاستعادة:

من القائمة:
1. انقر بالزر الأيمن على النسخة الاحتياطية
2. اختر "استعادة"
3. حدد خيارات الاستعادة:
   ☑️ استعادة قاعدة البيانات
   ☑️ استعادة ملفات التكوين
   ☑️ استعادة المرفقات
4. اضغط "موافق"

من ملف خارجي:
1. اضغط "تصفح" واختر ملف النسخة الاحتياطية
2. اضغط "استعادة من الملف"
3. حدد خيارات الاستعادة
4. تأكيد الاستعادة

⚠️ تحذير: ستتم استبدال البيانات الحالية
```

### 4. **إدارة الإعدادات**
```
⚙️ الإعدادات:

النسخ الاحتياطي التلقائي:
☑️ تفعيل النسخ الاحتياطي التلقائي
🕐 وقت النسخ الاحتياطي: 02:00 (افتراضي)

إعدادات التخزين:
🔢 الحد الأقصى للنسخ الاحتياطية: 30 (افتراضي)
☑️ ضغط النسخ الاحتياطية: مفعل
☑️ تضمين المرفقات: مفعل

إعدادات إضافية:
☑️ نسخ احتياطي عند بدء التشغيل
☑️ نسخ احتياطي عند إغلاق التطبيق

أزرار الإدارة:
⚙️ تعديل الإعدادات: فتح نافذة التعديل
🔄 إعادة تعيين: استعادة الإعدادات الافتراضية
```

### 5. **مراقبة الإحصائيات**
```
📊 الإحصائيات:

الإحصائيات العامة:
- إجمالي النسخ الاحتياطية
- الحجم الإجمالي (ميجابايت)
- متوسط الحجم
- تكرار النسخ الاحتياطي
- حالة النسخ الاحتياطي التلقائي

معلومات النسخ:
- أحدث نسخة احتياطية (الاسم والحجم)
- أقدم نسخة احتياطية (الاسم والحجم)

🔄 تحديث الإحصائيات: تحديث البيانات
```

---

## 🔧 الميزات المتقدمة

### 1. **التحقق من سلامة النسخ الاحتياطية**
```
🔍 فحص السلامة:

الفحوصات المتاحة:
✅ وجود الملف
✅ قابلية القراءة
✅ صحة معلومات النسخة الاحتياطية
✅ وجود ملفات قاعدة البيانات
✅ صحة checksum
✅ صحة الهيكل

كيفية الاستخدام:
1. انقر بالزر الأيمن على النسخة الاحتياطية
2. اختر "التحقق من السلامة"
3. اعرض تقرير التحقق المفصل

النتائج:
- سليمة تماماً: جميع الفحوصات نجحت
- سليمة مع تحذيرات: الفحوصات الأساسية نجحت
- تالفة: فشل في الفحوصات الأساسية
```

### 2. **إدارة النسخ الاحتياطية**
```
📁 إدارة الملفات:

القائمة المنبثقة (انقر بالزر الأيمن):
📥 استعادة: استعادة النسخة الاحتياطية
🔍 التحقق من السلامة: فحص سلامة الملف
📁 فتح مجلد الملف: فتح مجلد النسخة الاحتياطية
📋 نسخ المسار: نسخ مسار الملف للحافظة
🗑️ حذف: حذف النسخة الاحتياطية

معلومات النسخ:
- الاسم
- تاريخ الإنشاء
- الحجم (ميجابايت)
- نوع قاعدة البيانات
- حالة الضغط
```

### 3. **الجدولة المتقدمة**
```
⏰ الجدولة التلقائية:

أنواع الجدولة:
🕐 يومية: نسخ احتياطي في وقت محدد يومياً
🚀 عند بدء التشغيل: نسخ احتياطي عند فتح التطبيق
🔚 عند الإغلاق: نسخ احتياطي عند إغلاق التطبيق

إدارة الجدولة:
- بدء تلقائي عند تشغيل التطبيق
- إيقاف آمن عند إغلاق التطبيق
- معالجة الأخطاء والاستمرارية
- تشغيل في خيوط منفصلة

ملاحظة: يتطلب مكتبة schedule للجدولة المتقدمة
pip install schedule
```

---

## 📊 هيكل النسخة الاحتياطية

### 1. **محتويات النسخة الاحتياطية**
```
backup_YYYYMMDD_HHMMSS.zip
├── backup_info.json          # معلومات النسخة الاحتياطية
├── database/                 # مجلد قاعدة البيانات
│   ├── transactions.db       # ملف SQLite (إذا كان متاحاً)
│   ├── table1.json          # بيانات الجداول (MySQL)
│   ├── table2.json
│   └── database_structure.json # هيكل قاعدة البيانات
├── config/                   # مجلد ملفات التكوين
│   ├── config.ini
│   ├── backup_settings.json
│   └── user_preferences.json
└── attachments/              # مجلد المرفقات (اختياري)
    ├── file1.pdf
    └── file2.jpg
```

### 2. **ملف معلومات النسخة الاحتياطية**
```json
{
  "backup_name": "backup_20241205_143022",
  "created_at": "2024-12-05T14:30:22.123456",
  "database_type": "SQLite",
  "include_attachments": true,
  "application_version": "1.0.0",
  "backup_size": 1048576,
  "checksum": "d41d8cd98f00b204e9800998ecf8427e",
  "compressed": true
}
```

---

## 🎨 الواجهات التفاعلية

### 1. **التبويبات الرئيسية**
```
🔄 النسخ الاحتياطي:
- إنشاء نسخة احتياطية جديدة
- قائمة النسخ الاحتياطية الموجودة
- إدارة النسخ (عرض، حذف، تحقق)

📥 الاستعادة:
- استعادة من ملف خارجي
- تعليمات الاستعادة المفصلة
- خيارات الاستعادة المتقدمة

⚙️ الإعدادات:
- عرض الإعدادات الحالية
- تعديل الإعدادات
- إعادة تعيين الإعدادات

📊 الإحصائيات:
- إحصائيات النسخ الاحتياطي
- معلومات النسخ الحديثة والقديمة
- تحديث الإحصائيات
```

### 2. **النوافذ المنبثقة**
```
⚙️ نافذة إعدادات النسخ الاحتياطي:
- النسخ الاحتياطي التلقائي (تفعيل/إلغاء)
- وقت النسخ الاحتياطي (ساعة:دقيقة)
- الحد الأقصى للنسخ الاحتياطية
- خيارات الضغط والمرفقات
- إعدادات بدء التشغيل والإغلاق

📥 نافذة خيارات الاستعادة:
- معلومات النسخة الاحتياطية
- خيارات الاستعادة (قاعدة البيانات، التكوين، المرفقات)
- تحذيرات الاستعادة
- تأكيد العملية
```

### 3. **شريط الحالة والتقدم**
```
📊 شريط الحالة:
- حالة العملية الحالية
- رسائل النجاح والأخطاء
- معلومات آخر عملية

📈 شريط التقدم:
- تقدم إنشاء النسخة الاحتياطية
- تقدم عملية الاستعادة
- نسبة مئوية للإنجاز
```

---

## 🔧 الملفات المضافة

### 1. **خدمة النسخ الاحتياطي**
- `src/services/backup_service.py`: الخدمة الرئيسية للنسخ الاحتياطي
  - `BackupService`: إدارة شاملة للنسخ الاحتياطي والاستعادة
  - دعم MySQL و SQLite
  - ضغط وتشفير متقدم
  - جدولة تلقائية
  - التحقق من السلامة

### 2. **واجهة النسخ الاحتياطي**
- `src/ui/backup_widget.py`: الواجهة الرئيسية لإدارة النسخ الاحتياطي
  - `BackupWidget`: الواجهة الموحدة مع 4 تبويبات
  - `BackupThread`: خيط النسخ الاحتياطي
  - `RestoreThread`: خيط الاستعادة
  - `BackupSettingsDialog`: نافذة إعدادات النسخ الاحتياطي
  - `RestoreOptionsDialog`: نافذة خيارات الاستعادة

### 3. **ملفات الاختبار**
- `test_backup_system.py`: اختبار شامل للنظام
- `test_backup_simple.py`: اختبار مبسط
- `create_test_db.py`: إنشاء قاعدة بيانات تجريبية

---

## 🧪 نتائج الاختبار

### الاختبار المبسط:
```
✅ إنشاء خدمة النسخ الاحتياطي: نجح
✅ تحميل الإعدادات: 9 إعداد
✅ إنشاء النسخة الاحتياطية: نجح
✅ قائمة النسخ الاحتياطية: متاحة
✅ الإحصائيات: متاحة
✅ التحقق من السلامة: نجح
✅ حذف النسخة الاحتياطية: نجح
✅ واجهة النسخ الاحتياطي: 4 تبويبات
```

### الميزات المؤكدة:
- **إنشاء النسخ الاحتياطية**: ✅ يعمل بكفاءة
- **ضغط الملفات**: ✅ ضغط ZIP متقدم
- **التحقق من السلامة**: ✅ فحص شامل
- **إدارة النسخ**: ✅ قائمة وحذف وإحصائيات
- **الواجهات التفاعلية**: ✅ 4 تبويبات متخصصة
- **معالجة الأخطاء**: ✅ معالجة شاملة
- **الإعدادات المتقدمة**: ✅ تخصيص كامل

---

## 📈 التقدم الحالي

**المكتمل:** 11 من 15 مهمة (73%)

### المهام المكتملة:
1. ✅ تحليل المتطلبات وإعداد البنية الأساسية
2. ✅ تصميم وإنشاء قاعدة البيانات
3. ✅ تطوير طبقة الاتصال بقاعدة البيانات
4. ✅ تطوير نظام المصادقة والأمان
5. ✅ تطوير الواجهة الرئيسية والتنقل
6. ✅ تطوير لوحة التحكم والإحصائيات
7. ✅ تطوير إدارة المعاملات (CRUD)
8. ✅ تطوير نظام البحث والتصفية المتقدم
9. ✅ تطوير لوحة تحكم المدير
10. ✅ تطوير نظام التقارير والإحصائيات المتقدمة
11. ✅ **تطوير نظام النسخ الاحتياطي والاستعادة** ← **مكتمل الآن!**

---

## 🎯 الخطوات التالية

هل تريد:

1. **اختبار النظام الجديد** والتأكد من عمل جميع ميزات النسخ الاحتياطي؟
2. **المتابعة للمهمة التالية** - تطوير نظام المزامنة الذكية؟
3. **تحسين أو إضافة ميزات** لنظام النسخ الاحتياطي الحالي؟
4. **تثبيت مكتبة schedule** لتفعيل الجدولة التلقائية المتقدمة؟

---

## 🎉 النتائج

### ✅ تم إنجاز:
- نظام نسخ احتياطي شامل ومتقدم
- دعم متعدد قواعد البيانات (MySQL + SQLite)
- ضغط وتشفير متقدم مع checksum
- جدولة تلقائية مع إدارة الأحداث
- واجهات تفاعلية متطورة (4 تبويبات)
- استعادة انتقائية مع خيارات متقدمة
- التحقق من سلامة النسخ الاحتياطية
- إحصائيات وتقارير مفصلة

### 📈 الإحصائيات الحالية:
- **أنواع النسخ الاحتياطي**: شامل (قاعدة البيانات + تكوين + مرفقات)
- **صيغ الدعم**: MySQL, SQLite, ملفات التكوين, المرفقات
- **طرق الاستعادة**: انتقائية مع خيارات متقدمة
- **الواجهات**: 4 تبويبات متخصصة
- **الجدولة**: تلقائية مع أحداث النظام
- **الأمان**: checksum وفحص السلامة

---

## 🚀 الاستخدام الفوري

النظام جاهز للاستخدام الآن! يمكنك:

1. **تشغيل التطبيق**: `python main.py`
2. **تسجيل الدخول**: admin / admin
3. **استكشاف النسخ الاحتياطي**: انتقل لقسم "إدارة النسخ الاحتياطي"
4. **إنشاء نسخة احتياطية**: استخدم النسخ السريع أو المخصص
5. **إدارة النسخ**: عرض، تحقق، استعادة، حذف
6. **تخصيص الإعدادات**: جدولة تلقائية وخيارات متقدمة

### 📈 التقدم الحالي:
**المكتمل:** 11 من 15 مهمة (73%)

## 🎯 الخطوات التالية:

هل تريد:

1. **اختبار النظام الجديد** والتأكد من عمل جميع ميزات النسخ الاحتياطي؟
2. **المتابعة للمهمة التالية** - تطوير نظام المزامنة الذكية؟
3. **تحسين أو إضافة ميزات** لنظام النسخ الاحتياطي الحالي؟
4. **تثبيت مكتبة schedule** لتفعيل الجدولة التلقائية المتقدمة؟

نظام النسخ الاحتياطي والاستعادة المتقدم الآن مكتمل وجاهز للاستخدام الفعلي! 🎉

### 💡 ملاحظة مهمة:
لتفعيل الجدولة التلقائية المتقدمة، يُنصح بتثبيت مكتبة schedule:
```bash
pip install schedule
```

بدون هذه المكتبة، ستعمل جميع الميزات الأخرى بشكل طبيعي، لكن الجدولة التلقائية ستكون معطلة.
