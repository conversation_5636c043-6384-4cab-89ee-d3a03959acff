# -*- coding: utf-8 -*-
"""
خدمة البحث المتقدم
Advanced Search Service

محرك البحث المتقدم مع دعم البحث النصي والتصفية المعقدة
"""

from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, date
import re

from database.transaction_repository import TransactionRepository
from database.lookup_repositories import LookupService
from database.user_repository import UserRepository
from utils.logger import Logger

class SearchCriteria:
    """معايير البحث"""
    
    def __init__(self):
        # البحث النصي
        self.text_query: str = ""
        self.search_fields: List[str] = []
        self.search_mode: str = "contains"  # contains, starts_with, ends_with, exact
        
        # التصفية
        self.filters: Dict[str, Any] = {}
        
        # نطاق التاريخ
        self.date_from: Optional[str] = None
        self.date_to: Optional[str] = None
        self.date_field: str = "head_incoming_date"
        
        # الترتيب
        self.sort_field: str = "created_at"
        self.sort_direction: str = "DESC"
        
        # التصفح
        self.limit: Optional[int] = None
        self.offset: int = 0
        
        # خيارات متقدمة
        self.include_inactive: bool = False
        self.case_sensitive: bool = False

class SearchResult:
    """نتيجة البحث"""
    
    def __init__(self, transactions: List[Dict], total_count: int, 
                 search_time: float, criteria: SearchCriteria):
        self.transactions = transactions
        self.total_count = total_count
        self.search_time = search_time
        self.criteria = criteria
        self.has_more = len(transactions) < total_count if criteria.limit else False

class AdvancedSearchService:
    """خدمة البحث المتقدم"""
    
    def __init__(self, connection_manager):
        """
        تهيئة خدمة البحث المتقدم
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        self.transaction_repository = TransactionRepository(connection_manager)
        self.lookup_service = LookupService(connection_manager)
        self.user_repository = UserRepository(connection_manager)
        self.logger = Logger(__name__)
        
        # حقول البحث المتاحة
        self.searchable_fields = {
            'head_incoming_no': 'رقم الوارد',
            'subject': 'الموضوع',
            'researcher_notes': 'ملاحظات الباحث',
            'visa_type': 'نوع التأشيرة',
            'received_from': 'وارد من',
            'action_taken': 'الإجراء المتخذ',
            'request_status': 'حالة الطلب',
            'researcher_1_name': 'الباحث الأول',
            'researcher_2_name': 'الباحث الثاني',
            'data_entry_user': 'مدخل البيانات'
        }
        
        # حقول التصفية المتاحة
        self.filterable_fields = {
            'request_status_id': 'حالة الطلب',
            'visa_type_id': 'نوع التأشيرة',
            'received_from_id': 'وارد من',
            'action_taken_id': 'الإجراء المتخذ',
            'researcher_1_id': 'الباحث الأول',
            'researcher_2_id': 'الباحث الثاني',
            'user_id': 'مدخل البيانات',
            'priority': 'الأولوية',
            'created_by': 'منشئ المعاملة',
            'updated_by': 'محدث المعاملة'
        }
    
    def search(self, criteria: SearchCriteria) -> SearchResult:
        """
        تنفيذ البحث المتقدم
        
        Args:
            criteria: معايير البحث
            
        Returns:
            نتيجة البحث
        """
        start_time = datetime.now()
        
        try:
            # بناء الاستعلام
            query, params = self._build_search_query(criteria)
            
            # تنفيذ الاستعلام
            transactions = self.connection_manager.execute_query(query, params)
            
            # حساب العدد الكلي إذا كان هناك حد
            total_count = len(transactions)
            if criteria.limit and len(transactions) == criteria.limit:
                count_query, count_params = self._build_count_query(criteria)
                count_result = self.connection_manager.execute_query(count_query, count_params)
                total_count = count_result[0]['total_count'] if count_result else len(transactions)
            
            # حساب وقت البحث
            search_time = (datetime.now() - start_time).total_seconds()
            
            return SearchResult(transactions, total_count, search_time, criteria)
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث المتقدم: {str(e)}")
            search_time = (datetime.now() - start_time).total_seconds()
            return SearchResult([], 0, search_time, criteria)
    
    def _build_search_query(self, criteria: SearchCriteria) -> Tuple[str, tuple]:
        """بناء استعلام البحث"""
        
        # الاستعلام الأساسي
        query = """
            SELECT 
                t.id,
                t.head_incoming_no,
                t.head_incoming_date,
                t.subject,
                t.researcher_notes,
                t.priority,
                t.due_date,
                t.completion_date,
                t.created_at,
                t.updated_at,
                
                -- بيانات المستخدمين
                u1.full_name as data_entry_user,
                u2.full_name as researcher_1_name,
                u3.full_name as researcher_2_name,
                u4.full_name as created_by_name,
                u5.full_name as updated_by_name,
                
                -- بيانات الجداول المساعدة
                vt.visa_type,
                rfs.received_from,
                at.action_taken,
                rs.request_status,
                rs.status_color,
                
                -- المعرفات للتعديل
                t.user_id,
                t.researcher_1_id,
                t.researcher_2_id,
                t.visa_type_id,
                t.received_from_id,
                t.action_taken_id,
                t.request_status_id,
                t.created_by,
                t.updated_by
                
            FROM transactions t
            LEFT JOIN users u1 ON t.user_id = u1.user_id
            LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
            LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
            LEFT JOIN users u4 ON t.created_by = u4.user_id
            LEFT JOIN users u5 ON t.updated_by = u5.user_id
            LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
            LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
            LEFT JOIN actions_taken at ON t.action_taken_id = at.id
            LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
        """
        
        # بناء شروط WHERE
        conditions = []
        params = []
        
        # البحث النصي
        if criteria.text_query.strip():
            text_conditions = self._build_text_search_conditions(criteria)
            if text_conditions[0]:
                conditions.append(f"({text_conditions[0]})")
                params.extend(text_conditions[1])
        
        # التصفية
        filter_conditions = self._build_filter_conditions(criteria)
        if filter_conditions[0]:
            conditions.append(filter_conditions[0])
            params.extend(filter_conditions[1])
        
        # نطاق التاريخ
        date_conditions = self._build_date_conditions(criteria)
        if date_conditions[0]:
            conditions.append(date_conditions[0])
            params.extend(date_conditions[1])
        
        # إضافة شروط WHERE
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        # الترتيب
        sort_field = criteria.sort_field
        if sort_field in ['created_at', 'updated_at', 'head_incoming_date', 'due_date', 'completion_date']:
            sort_field = f"t.{sort_field}"
        query += f" ORDER BY {sort_field} {criteria.sort_direction}"
        
        # الحد والإزاحة
        if criteria.limit:
            query += f" LIMIT {criteria.limit}"
            if criteria.offset > 0:
                query += f" OFFSET {criteria.offset}"
        
        return query, tuple(params)
    
    def _build_text_search_conditions(self, criteria: SearchCriteria) -> Tuple[str, List]:
        """بناء شروط البحث النصي"""
        
        if not criteria.text_query.strip():
            return "", []
        
        search_fields = criteria.search_fields if criteria.search_fields else list(self.searchable_fields.keys())
        conditions = []
        params = []
        
        # تحضير النص للبحث
        search_text = criteria.text_query.strip()
        if not criteria.case_sensitive:
            search_text = search_text.lower()
        
        # بناء شرط البحث حسب النمط
        for field in search_fields:
            if field in self.searchable_fields:
                field_name = self._get_field_name(field)
                
                if criteria.search_mode == "exact":
                    if criteria.case_sensitive:
                        conditions.append(f"{field_name} = ?")
                    else:
                        conditions.append(f"LOWER({field_name}) = ?")
                    params.append(search_text)
                    
                elif criteria.search_mode == "starts_with":
                    if criteria.case_sensitive:
                        conditions.append(f"{field_name} LIKE ?")
                    else:
                        conditions.append(f"LOWER({field_name}) LIKE ?")
                    params.append(f"{search_text}%")
                    
                elif criteria.search_mode == "ends_with":
                    if criteria.case_sensitive:
                        conditions.append(f"{field_name} LIKE ?")
                    else:
                        conditions.append(f"LOWER({field_name}) LIKE ?")
                    params.append(f"%{search_text}")
                    
                else:  # contains (default)
                    if criteria.case_sensitive:
                        conditions.append(f"{field_name} LIKE ?")
                    else:
                        conditions.append(f"LOWER({field_name}) LIKE ?")
                    params.append(f"%{search_text}%")
        
        return " OR ".join(conditions), params
    
    def _build_filter_conditions(self, criteria: SearchCriteria) -> Tuple[str, List]:
        """بناء شروط التصفية"""
        
        if not criteria.filters:
            return "", []
        
        conditions = []
        params = []
        
        for field, value in criteria.filters.items():
            if field in self.filterable_fields and value is not None:
                if isinstance(value, list):
                    # تصفية متعددة القيم
                    if value:
                        placeholders = ",".join(["?" for _ in value])
                        conditions.append(f"t.{field} IN ({placeholders})")
                        params.extend(value)
                else:
                    # تصفية قيمة واحدة
                    conditions.append(f"t.{field} = ?")
                    params.append(value)
        
        # تصفية خاصة للمعاملات المتأخرة
        if criteria.filters.get('overdue_only'):
            if self.connection_manager.is_online:
                conditions.append("t.due_date < CURDATE() AND rs.request_status != 'مكتمل'")
            else:
                conditions.append("julianday(t.due_date) < julianday('now') AND rs.request_status != 'مكتمل'")
        
        # تصفية خاصة للمعاملات المكتملة
        if criteria.filters.get('completed_only'):
            conditions.append("rs.request_status = 'مكتمل'")
        
        # تصفية خاصة للمعاملات الجديدة
        if criteria.filters.get('new_only'):
            conditions.append("rs.request_status = 'جديد'")
        
        return " AND ".join(conditions), params
    
    def _build_date_conditions(self, criteria: SearchCriteria) -> Tuple[str, List]:
        """بناء شروط نطاق التاريخ"""
        
        conditions = []
        params = []
        
        if criteria.date_from:
            conditions.append(f"t.{criteria.date_field} >= ?")
            params.append(criteria.date_from)
        
        if criteria.date_to:
            conditions.append(f"t.{criteria.date_field} <= ?")
            params.append(criteria.date_to)
        
        return " AND ".join(conditions), params
    
    def _build_count_query(self, criteria: SearchCriteria) -> Tuple[str, tuple]:
        """بناء استعلام العد"""
        
        query = """
            SELECT COUNT(*) as total_count
            FROM transactions t
            LEFT JOIN users u1 ON t.user_id = u1.user_id
            LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
            LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
            LEFT JOIN users u4 ON t.created_by = u4.user_id
            LEFT JOIN users u5 ON t.updated_by = u5.user_id
            LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
            LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
            LEFT JOIN actions_taken at ON t.action_taken_id = at.id
            LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
        """
        
        # بناء شروط WHERE (نفس المنطق بدون LIMIT/OFFSET)
        conditions = []
        params = []
        
        # البحث النصي
        if criteria.text_query.strip():
            text_conditions = self._build_text_search_conditions(criteria)
            if text_conditions[0]:
                conditions.append(f"({text_conditions[0]})")
                params.extend(text_conditions[1])
        
        # التصفية
        filter_conditions = self._build_filter_conditions(criteria)
        if filter_conditions[0]:
            conditions.append(filter_conditions[0])
            params.extend(filter_conditions[1])
        
        # نطاق التاريخ
        date_conditions = self._build_date_conditions(criteria)
        if date_conditions[0]:
            conditions.append(date_conditions[0])
            params.extend(date_conditions[1])
        
        # إضافة شروط WHERE
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        return query, tuple(params)
    
    def _get_field_name(self, field: str) -> str:
        """الحصول على اسم الحقل في الاستعلام"""
        
        field_mapping = {
            'head_incoming_no': 't.head_incoming_no',
            'subject': 't.subject',
            'researcher_notes': 't.researcher_notes',
            'visa_type': 'vt.visa_type',
            'received_from': 'rfs.received_from',
            'action_taken': 'at.action_taken',
            'request_status': 'rs.request_status',
            'researcher_1_name': 'u2.full_name',
            'researcher_2_name': 'u3.full_name',
            'data_entry_user': 'u1.full_name'
        }
        
        return field_mapping.get(field, f't.{field}')
    
    def get_search_suggestions(self, field: str, query: str, limit: int = 10) -> List[str]:
        """
        الحصول على اقتراحات البحث
        
        Args:
            field: الحقل المراد البحث فيه
            query: النص المراد البحث عنه
            limit: عدد الاقتراحات
            
        Returns:
            قائمة بالاقتراحات
        """
        try:
            if field not in self.searchable_fields:
                return []
            
            field_name = self._get_field_name(field)
            
            # استعلام الاقتراحات
            suggestions_query = f"""
                SELECT DISTINCT {field_name} as suggestion
                FROM transactions t
                LEFT JOIN users u1 ON t.user_id = u1.user_id
                LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
                LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
                LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
                LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
                LEFT JOIN actions_taken at ON t.action_taken_id = at.id
                LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                WHERE LOWER({field_name}) LIKE ?
                AND {field_name} IS NOT NULL
                AND {field_name} != ''
                ORDER BY {field_name}
                LIMIT ?
            """
            
            params = (f"%{query.lower()}%", limit)
            results = self.connection_manager.execute_query(suggestions_query, params)
            
            return [result['suggestion'] for result in results if result['suggestion']]
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على اقتراحات البحث: {str(e)}")
            return []
    
    def get_filter_options(self, field: str) -> List[Dict]:
        """
        الحصول على خيارات التصفية
        
        Args:
            field: الحقل المراد الحصول على خياراته
            
        Returns:
            قائمة بخيارات التصفية
        """
        try:
            if field == 'request_status_id':
                return self.lookup_service.get_combo_data('request_statuses')
            elif field == 'visa_type_id':
                return self.lookup_service.get_combo_data('visa_types')
            elif field == 'received_from_id':
                return self.lookup_service.get_combo_data('received_from_sources')
            elif field == 'action_taken_id':
                return self.lookup_service.get_combo_data('actions_taken')
            elif field in ['researcher_1_id', 'researcher_2_id', 'user_id']:
                return self.user_repository.get_researchers()
            elif field == 'priority':
                return [
                    {'id': 'low', 'name': 'منخفض'},
                    {'id': 'medium', 'name': 'متوسط'},
                    {'id': 'high', 'name': 'عالي'},
                    {'id': 'urgent', 'name': 'عاجل'}
                ]
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على خيارات التصفية: {str(e)}")
            return []
    
    def save_search_preset(self, name: str, criteria: SearchCriteria, user_id: int) -> bool:
        """
        حفظ إعداد بحث مسبق
        
        Args:
            name: اسم الإعداد
            criteria: معايير البحث
            user_id: معرف المستخدم
            
        Returns:
            True إذا تم الحفظ بنجاح
        """
        try:
            # تحويل معايير البحث إلى JSON
            import json
            criteria_json = json.dumps({
                'text_query': criteria.text_query,
                'search_fields': criteria.search_fields,
                'search_mode': criteria.search_mode,
                'filters': criteria.filters,
                'date_from': criteria.date_from,
                'date_to': criteria.date_to,
                'date_field': criteria.date_field,
                'sort_field': criteria.sort_field,
                'sort_direction': criteria.sort_direction
            })
            
            # حفظ في قاعدة البيانات (يحتاج جدول search_presets)
            # هذا مثال - يحتاج إنشاء الجدول
            query = """
                INSERT INTO search_presets (name, criteria, user_id, created_at)
                VALUES (?, ?, ?, ?)
            """
            
            params = (name, criteria_json, user_id, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # تنفيذ الاستعلام (يحتاج تطوير)
            # result = self.connection_manager.execute_query(query, params)
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ إعداد البحث: {str(e)}")
            return False
