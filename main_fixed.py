#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة المراسلات والمعاملات (الوارد والصادر)
IOTS - Incoming/Outgoing Transactions System

الملف الرئيسي المصحح لتطبيق سطح المكتب
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class IOTSApplication:
    """الفئة الرئيسية لتطبيق نظام متابعة المراسلات والمعاملات"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.app = None
        self.config = None
        self.connection_manager = None
        self.auth_service = None
        self.login_window = None
        self.main_window = None
        
    def initialize(self):
        """تهيئة جميع مكونات التطبيق"""
        try:
            print("بدء تهيئة التطبيق...")
            
            # إنشاء تطبيق Qt
            self.app = QApplication(sys.argv)
            print("✅ تم إنشاء تطبيق Qt")
            
            # تحميل التكوين
            from utils.config_manager import ConfigManager
            self.config = ConfigManager()
            print("✅ تم تحميل التكوين")
            
            # إعداد نظام السجلات
            from utils.logger import setup_logging
            setup_logging(self.config)
            logging.info("تم بدء تشغيل التطبيق")
            print("✅ تم إعداد نظام السجلات")
            
            # إعداد الخط والاتجاه للعربية
            self.setup_arabic_support()
            print("✅ تم إعداد دعم العربية")
            
            # تهيئة مدير الاتصال بقاعدة البيانات (النسخة المحسنة)
            try:
                from database.simple_connection_manager import SimpleConnectionManager
                self.connection_manager = SimpleConnectionManager(self.config)
                logging.info("تم إنشاء مدير الاتصال المحسن بنجاح")
                print("✅ تم إنشاء مدير الاتصال المحسن")

                # عرض حالة الاتصال
                if self.connection_manager.is_online:
                    print("🌐 النمط: MySQL (متصل)")
                else:
                    print("💾 النمط: SQLite (محلي)")

            except Exception as e:
                logging.error(f"خطأ في إنشاء مدير الاتصال: {str(e)}")
                print(f"❌ خطأ في إنشاء مدير الاتصال: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

            # تهيئة خدمة المصادقة
            from services.auth_service import AuthService
            self.auth_service = AuthService(self.connection_manager, self.config)
            logging.info("تم إنشاء خدمة المصادقة بنجاح")
            print("✅ تم إنشاء خدمة المصادقة")

            # اختبار الاتصال بقاعدة البيانات
            if self.connection_manager.test_connection():
                logging.info("تم الاتصال بقاعدة البيانات بنجاح")
                print("✅ تم الاتصال بقاعدة البيانات")
            else:
                logging.warning("فشل الاتصال بقاعدة البيانات الرئيسية، سيتم التشغيل في النمط المحلي")
                print("⚠️ التشغيل في النمط المحلي")

            print("✅ تم إكمال التهيئة بنجاح")
            return True
            
        except Exception as e:
            error_msg = f"خطأ في تهيئة التطبيق: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_error_message("خطأ في التهيئة", error_msg)
            return False
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية والاتجاه من اليمين لليسار"""
        try:
            # تعيين اتجاه التطبيق
            self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            
            # استخدام خط افتراضي يدعم العربية
            font = QFont("Segoe UI", 10)
            self.app.setFont(font)
                
        except Exception as e:
            logging.warning(f"تحذير: لم يتم تحميل الخط العربي: {str(e)}")
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            print("عرض نافذة تسجيل الدخول...")
            
            from ui.login_window import LoginWindow
            self.login_window = LoginWindow(self.config, self.connection_manager)
            print("✅ تم إنشاء نافذة تسجيل الدخول")

            # ربط إشارة نجاح تسجيل الدخول
            self.login_window.accepted.connect(self.on_login_success)
            print("✅ تم ربط إشارات نافذة تسجيل الدخول")

            # عرض النافذة
            result = self.login_window.exec()
            return result == QDialog.DialogCode.Accepted

        except Exception as e:
            error_msg = f"خطأ في عرض نافذة تسجيل الدخول: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_error_message("خطأ", error_msg)
            return False

    def on_login_success(self):
        """معالجة نجاح تسجيل الدخول"""
        try:
            print("معالجة نجاح تسجيل الدخول...")
            
            # إنشاء وعرض النافذة الرئيسية
            from ui.main_window import MainWindow
            self.main_window = MainWindow(
                self.config,
                self.connection_manager,
                self.auth_service
            )
            print("✅ تم إنشاء النافذة الرئيسية")

            # ربط إشارة تسجيل الخروج
            self.main_window.user_logged_out.connect(self.on_logout)

            # عرض النافذة الرئيسية
            self.main_window.show()
            print("✅ تم عرض النافذة الرئيسية")

        except Exception as e:
            error_msg = f"خطأ في عرض النافذة الرئيسية: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()
            self.show_error_message("خطأ", error_msg)

    def on_logout(self):
        """معالجة تسجيل الخروج"""
        try:
            # إغلاق النافذة الرئيسية
            if self.main_window:
                self.main_window.close()
                self.main_window = None

            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login_window()

        except Exception as e:
            logging.error(f"خطأ في معالجة تسجيل الخروج: {str(e)}")
    
    def show_error_message(self, title, message):
        """عرض رسالة خطأ"""
        if self.app:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            msg_box.exec()
        else:
            print(f"خطأ: {title} - {message}")
    
    def run(self):
        """تشغيل التطبيق"""
        print("=" * 50)
        print("بدء تشغيل نظام متابعة المراسلات والمعاملات")
        print("=" * 50)
        
        if not self.initialize():
            print("❌ فشل في تهيئة التطبيق")
            return 1
        
        if not self.show_login_window():
            print("❌ فشل في عرض نافذة تسجيل الدخول")
            return 1
        
        # تشغيل حلقة الأحداث الرئيسية
        print("🚀 بدء تشغيل حلقة الأحداث...")
        return self.app.exec()

def main():
    """الدالة الرئيسية"""
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'data', 'reports', 'temp']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    # إنشاء وتشغيل التطبيق
    iots_app = IOTSApplication()
    return iots_app.run()

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
