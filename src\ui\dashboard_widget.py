# -*- coding: utf-8 -*-
"""
ويدجت لوحة التحكم
Dashboard Widget

لوحة التحكم الرئيسية مع الإحصائيات والمعلومات السريعة
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QScrollArea,
                            QProgressBar, QTableWidget, QTableWidgetItem,
                            QHeaderView, QSpacerItem, QSizePolicy, QTabWidget,
                            QComboBox, QGroupBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap, QPalette, QColor

from utils.logger import Logger
from services.statistics_service import StatisticsService
from ui.charts_widget import PieChartWidget, Bar<PERSON>hartWidget, LineChartWidget, ChartsContainer

class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    def __init__(self, title: str, value: str, icon: str = "📊", color: str = "#007bff"):
        super().__init__()
        self.setup_ui(title, value, icon, color)
    
    def setup_ui(self, title: str, value: str, icon: str, color: str):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
            QFrame:hover {{
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                color: {color};
                background-color: {color}20;
                border-radius: 25px;
                padding: 10px;
                min-width: 50px;
                max-width: 50px;
                min-height: 50px;
                max-height: 50px;
            }}
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)
        
        # النص
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        text_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
            }
        """)
        text_layout.addWidget(title_label)
        
        layout.addLayout(text_layout)
        layout.addStretch()
        
        # حفظ المراجع للتحديث
        self.value_label = value_label
        self.title_label = title_label
    
    def update_value(self, new_value: str):
        """تحديث القيمة"""
        self.value_label.setText(new_value)

class QuickActionButton(QPushButton):
    """زر إجراء سريع"""
    
    def __init__(self, title: str, description: str, icon: str = "⚡"):
        super().__init__()
        self.setup_ui(title, description, icon)
    
    def setup_ui(self, title: str, description: str, icon: str):
        """إعداد واجهة الزر"""
        self.setMinimumHeight(80)
        self.setStyleSheet("""
            QPushButton {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                text-align: left;
                padding: 15px;
            }
            QPushButton:hover {
                background-color: #f8f9fa;
                border-color: #007bff;
            }
            QPushButton:pressed {
                background-color: #e9ecef;
            }
        """)
        
        # تعيين النص
        self.setText(f"{icon}  {title}\n{description}")

class RecentTransactionsTable(QTableWidget):
    """جدول المعاملات الحديثة"""
    
    def __init__(self):
        super().__init__()
        self.setup_table()
    
    def setup_table(self):
        """إعداد الجدول"""
        # تعيين الأعمدة
        headers = ["رقم الوارد", "الموضوع", "التاريخ", "الحالة", "الباحث"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # إعداد الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # تعيين عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        # إخفاء الرقم التسلسلي
        self.verticalHeader().setVisible(False)
        
        # تعيين الارتفاع
        self.setMaximumHeight(300)

class DashboardWidget(QWidget):
    """ويدجت لوحة التحكم"""
    
    # إشارات مخصصة
    action_requested = pyqtSignal(str)
    
    def __init__(self, config_manager, connection_manager, auth_service, parent=None):
        super().__init__(parent)

        self.config = config_manager
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.logger = Logger(__name__)

        # خدمة الإحصائيات
        self.statistics_service = StatisticsService(connection_manager)

        # بيانات الإحصائيات
        self.stats_data = {
            'total_transactions': 0,
            'new_transactions': 0,
            'processing_transactions': 0,
            'completed_transactions': 0,
            'overdue_transactions': 0
        }

        self.setup_ui()
        self.setup_connections()
        self.load_dashboard_data()

        # بدء مؤقت التحديث
        self.start_refresh_timer()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)

        # تبويب النظرة العامة
        self.setup_overview_tab()

        # تبويب الرسوم البيانية
        self.setup_charts_tab()

        # تبويب التقارير التفصيلية
        self.setup_detailed_reports_tab()

        main_layout.addWidget(self.tab_widget)

    def setup_overview_tab(self):
        """إعداد تبويب النظرة العامة"""
        overview_widget = QWidget()

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setWidget(overview_widget)

        # التخطيط
        layout = QVBoxLayout(overview_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # رسالة الترحيب
        self.setup_welcome_section(layout)

        # بطاقات الإحصائيات
        self.setup_stats_cards(layout)

        # الإجراءات السريعة
        self.setup_quick_actions(layout)

        # المعاملات الحديثة
        self.setup_recent_transactions(layout)

        self.tab_widget.addTab(scroll_area, "النظرة العامة")

    def setup_charts_tab(self):
        """إعداد تبويب الرسوم البيانية"""
        charts_widget = QWidget()
        layout = QVBoxLayout(charts_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # شريط التحكم
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Shape.Box)
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
            }
        """)

        control_layout = QHBoxLayout(control_frame)

        # اختيار السنة
        year_label = QLabel("السنة:")
        self.year_combo = QComboBox()
        current_year = 2024
        for year in range(current_year - 2, current_year + 2):
            self.year_combo.addItem(str(year))
        self.year_combo.setCurrentText(str(current_year))

        # زر التحديث
        refresh_charts_btn = QPushButton("تحديث الرسوم البيانية")
        refresh_charts_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        control_layout.addWidget(year_label)
        control_layout.addWidget(self.year_combo)
        control_layout.addStretch()
        control_layout.addWidget(refresh_charts_btn)

        layout.addWidget(control_frame)

        # حاوي الرسوم البيانية
        self.charts_container = ChartsContainer()
        layout.addWidget(self.charts_container)

        # ربط الأحداث
        refresh_charts_btn.clicked.connect(self.refresh_charts)
        self.year_combo.currentTextChanged.connect(self.refresh_charts)

        self.tab_widget.addTab(charts_widget, "الرسوم البيانية")

    def setup_detailed_reports_tab(self):
        """إعداد تبويب التقارير التفصيلية"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)

        # إحصائيات الباحثين
        self.setup_researcher_stats(scroll_layout)

        # إحصائيات أنواع التأشيرات
        self.setup_visa_stats(scroll_layout)

        # إحصائيات الأولوية
        self.setup_priority_stats(scroll_layout)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        self.tab_widget.addTab(reports_widget, "التقارير التفصيلية")

    def setup_researcher_stats(self, layout):
        """إعداد إحصائيات الباحثين"""
        group_box = QGroupBox("إحصائيات الباحثين")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        group_layout = QVBoxLayout(group_box)

        # جدول الباحثين
        self.researcher_table = QTableWidget()
        self.researcher_table.setColumnCount(6)
        self.researcher_table.setHorizontalHeaderLabels([
            "الاسم", "المسندة", "المكتملة", "قيد المعالجة", "جديدة", "معدل الإنجاز %"
        ])

        # إعداد الجدول
        self.researcher_table.setAlternatingRowColors(True)
        self.researcher_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.researcher_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.researcher_table.verticalHeader().setVisible(False)
        self.researcher_table.setMaximumHeight(200)

        # تعيين عرض الأعمدة
        header = self.researcher_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        for i in range(1, 6):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        group_layout.addWidget(self.researcher_table)
        layout.addWidget(group_box)

    def setup_visa_stats(self, layout):
        """إعداد إحصائيات أنواع التأشيرات"""
        group_box = QGroupBox("إحصائيات أنواع التأشيرات")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        group_layout = QVBoxLayout(group_box)

        # جدول أنواع التأشيرات
        self.visa_table = QTableWidget()
        self.visa_table.setColumnCount(5)
        self.visa_table.setHorizontalHeaderLabels([
            "نوع التأشيرة", "العدد الكلي", "المكتملة", "النسبة %", "معدل الإنجاز %"
        ])

        # إعداد الجدول
        self.visa_table.setAlternatingRowColors(True)
        self.visa_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.visa_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.visa_table.verticalHeader().setVisible(False)
        self.visa_table.setMaximumHeight(200)

        # تعيين عرض الأعمدة
        header = self.visa_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        for i in range(1, 5):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        group_layout.addWidget(self.visa_table)
        layout.addWidget(group_box)

    def setup_priority_stats(self, layout):
        """إعداد إحصائيات الأولوية"""
        group_box = QGroupBox("إحصائيات الأولوية")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        group_layout = QHBoxLayout(group_box)

        # بطاقات الأولوية
        self.priority_cards = {}
        priorities = [
            ('urgent', 'عاجل', '#dc3545'),
            ('high', 'عالي', '#fd7e14'),
            ('medium', 'متوسط', '#ffc107'),
            ('low', 'منخفض', '#28a745')
        ]

        for priority_key, priority_name, color in priorities:
            card = StatCard(priority_name, "0", "⚡", color)
            self.priority_cards[priority_key] = card
            group_layout.addWidget(card)

        layout.addWidget(group_box)
    
    def setup_welcome_section(self, layout):
        """إعداد قسم الترحيب"""
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                border-radius: 10px;
                color: white;
            }
        """)
        
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setContentsMargins(20, 20, 20, 20)
        welcome_layout.setSpacing(10)
        
        # عنوان الترحيب
        current_user = self.auth_service.get_current_user()
        user_name = current_user.get('full_name', 'المستخدم') if current_user else 'المستخدم'
        
        welcome_title = QLabel(f"مرحباً، {user_name}")
        welcome_title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
            }
        """)
        welcome_layout.addWidget(welcome_title)
        
        # وصف الترحيب
        welcome_desc = QLabel("مرحباً بك في نظام متابعة المراسلات والمعاملات")
        welcome_desc.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
            }
        """)
        welcome_layout.addWidget(welcome_desc)
        
        layout.addWidget(welcome_frame)
    
    def setup_stats_cards(self, layout):
        """إعداد بطاقات الإحصائيات"""
        stats_label = QLabel("الإحصائيات السريعة")
        stats_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(stats_label)
        
        # تخطيط البطاقات
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        
        # بطاقة إجمالي المعاملات
        self.total_card = StatCard("إجمالي المعاملات", "0", "📋", "#007bff")
        cards_layout.addWidget(self.total_card, 0, 0)
        
        # بطاقة المعاملات الجديدة
        self.new_card = StatCard("معاملات جديدة", "0", "🆕", "#28a745")
        cards_layout.addWidget(self.new_card, 0, 1)
        
        # بطاقة قيد المعالجة
        self.processing_card = StatCard("قيد المعالجة", "0", "⏳", "#ffc107")
        cards_layout.addWidget(self.processing_card, 0, 2)
        
        # بطاقة المكتملة
        self.completed_card = StatCard("مكتملة", "0", "✅", "#17a2b8")
        cards_layout.addWidget(self.completed_card, 1, 0)
        
        # بطاقة المتأخرة
        self.overdue_card = StatCard("متأخرة", "0", "⚠️", "#dc3545")
        cards_layout.addWidget(self.overdue_card, 1, 1)
        
        # بطاقة معدل الإنجاز
        self.completion_card = StatCard("معدل الإنجاز", "0%", "📈", "#6f42c1")
        cards_layout.addWidget(self.completion_card, 1, 2)
        
        layout.addLayout(cards_layout)
    
    def setup_quick_actions(self, layout):
        """إعداد الإجراءات السريعة"""
        actions_label = QLabel("الإجراءات السريعة")
        actions_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(actions_label)
        
        # تخطيط الإجراءات
        actions_layout = QGridLayout()
        actions_layout.setSpacing(15)
        
        # إضافة معاملة جديدة
        new_transaction_btn = QuickActionButton(
            "إضافة معاملة جديدة",
            "إنشاء معاملة جديدة في النظام",
            "➕"
        )
        actions_layout.addWidget(new_transaction_btn, 0, 0)
        
        # البحث في المعاملات
        search_btn = QuickActionButton(
            "البحث في المعاملات",
            "البحث والتصفية في المعاملات",
            "🔍"
        )
        actions_layout.addWidget(search_btn, 0, 1)
        
        # التقارير
        reports_btn = QuickActionButton(
            "التقارير",
            "عرض وتصدير التقارير",
            "📊"
        )
        actions_layout.addWidget(reports_btn, 0, 2)
        
        # المزامنة
        sync_btn = QuickActionButton(
            "مزامنة البيانات",
            "مزامنة البيانات مع الخادم",
            "🔄"
        )
        actions_layout.addWidget(sync_btn, 1, 0)
        
        layout.addLayout(actions_layout)
        
        # حفظ المراجع للاتصالات
        self.action_buttons = {
            'new_transaction': new_transaction_btn,
            'search': search_btn,
            'reports': reports_btn,
            'sync': sync_btn
        }
    
    def setup_recent_transactions(self, layout):
        """إعداد قسم المعاملات الحديثة"""
        recent_label = QLabel("المعاملات الحديثة")
        recent_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(recent_label)
        
        # جدول المعاملات الحديثة
        self.recent_table = RecentTransactionsTable()
        layout.addWidget(self.recent_table)
        
        # زر عرض المزيد
        view_more_btn = QPushButton("عرض جميع المعاملات")
        view_more_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        layout.addWidget(view_more_btn)
        
        self.view_more_btn = view_more_btn
    
    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        # أزرار الإجراءات السريعة
        for action, button in self.action_buttons.items():
            button.clicked.connect(lambda checked, a=action: self.action_requested.emit(a))
        
        # زر عرض المزيد
        self.view_more_btn.clicked.connect(lambda: self.action_requested.emit('view_all_transactions'))
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            # تحميل الإحصائيات
            self.load_statistics()

            # تحميل المعاملات الحديثة
            self.load_recent_transactions()

            # تحميل الإحصائيات التفصيلية
            self.load_detailed_statistics()

            # تحديث الرسوم البيانية
            self.refresh_charts()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات لوحة التحكم: {str(e)}")

    def load_detailed_statistics(self):
        """تحميل الإحصائيات التفصيلية"""
        try:
            # تحميل إحصائيات الباحثين
            self.load_researcher_statistics()

            # تحميل إحصائيات أنواع التأشيرات
            self.load_visa_statistics()

            # تحميل إحصائيات الأولوية
            self.load_priority_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإحصائيات التفصيلية: {str(e)}")

    def load_researcher_statistics(self):
        """تحميل إحصائيات الباحثين"""
        try:
            researcher_stats = self.statistics_service.get_researcher_statistics()

            self.researcher_table.setRowCount(len(researcher_stats))

            for row, stats in enumerate(researcher_stats):
                self.researcher_table.setItem(row, 0, QTableWidgetItem(stats['name']))
                self.researcher_table.setItem(row, 1, QTableWidgetItem(str(stats['total_assigned'])))
                self.researcher_table.setItem(row, 2, QTableWidgetItem(str(stats['completed'])))
                self.researcher_table.setItem(row, 3, QTableWidgetItem(str(stats['processing'])))
                self.researcher_table.setItem(row, 4, QTableWidgetItem(str(stats['new_tasks'])))
                self.researcher_table.setItem(row, 5, QTableWidgetItem(f"{stats['completion_rate']}%"))

                # تلوين معدل الإنجاز
                completion_item = self.researcher_table.item(row, 5)
                if stats['completion_rate'] >= 80:
                    completion_item.setBackground(QColor("#d4edda"))
                elif stats['completion_rate'] >= 60:
                    completion_item.setBackground(QColor("#fff3cd"))
                else:
                    completion_item.setBackground(QColor("#f8d7da"))

        except Exception as e:
            self.logger.error(f"خطأ في تحميل إحصائيات الباحثين: {str(e)}")

    def load_visa_statistics(self):
        """تحميل إحصائيات أنواع التأشيرات"""
        try:
            visa_stats = self.statistics_service.get_visa_type_statistics()

            self.visa_table.setRowCount(len(visa_stats))

            for row, stats in enumerate(visa_stats):
                self.visa_table.setItem(row, 0, QTableWidgetItem(stats['visa_type']))
                self.visa_table.setItem(row, 1, QTableWidgetItem(str(stats['total_count'])))
                self.visa_table.setItem(row, 2, QTableWidgetItem(str(stats['completed_count'])))
                self.visa_table.setItem(row, 3, QTableWidgetItem(f"{stats['percentage']}%"))
                self.visa_table.setItem(row, 4, QTableWidgetItem(f"{stats['completion_rate']}%"))

        except Exception as e:
            self.logger.error(f"خطأ في تحميل إحصائيات أنواع التأشيرات: {str(e)}")

    def load_priority_statistics(self):
        """تحميل إحصائيات الأولوية"""
        try:
            priority_stats = self.statistics_service.get_priority_statistics()

            for priority_key, card in self.priority_cards.items():
                if priority_key in priority_stats:
                    stats = priority_stats[priority_key]
                    card.update_value(f"{stats['count']} ({stats['percentage']}%)")
                else:
                    card.update_value("0 (0%)")

        except Exception as e:
            self.logger.error(f"خطأ في تحميل إحصائيات الأولوية: {str(e)}")

    def refresh_charts(self):
        """تحديث الرسوم البيانية"""
        try:
            # مسح الرسوم البيانية الموجودة
            self.charts_container.clear_charts()

            # الحصول على السنة المختارة
            selected_year = int(self.year_combo.currentText()) if hasattr(self, 'year_combo') else 2024

            # رسم بياني شهري
            monthly_data = self.statistics_service.get_monthly_statistics(selected_year)
            if monthly_data:
                monthly_chart_data = [
                    {'label': item['month_name'], 'value': item['total']}
                    for item in monthly_data
                ]
                monthly_chart = LineChartWidget(monthly_chart_data, f"المعاملات الشهرية - {selected_year}")
                self.charts_container.add_chart(monthly_chart)

            # رسم بياني دائري لحالات المعاملات
            overview_stats = self.statistics_service.get_overview_statistics()
            if overview_stats:
                status_data = [
                    {'label': 'جديدة', 'value': overview_stats.get('new_transactions', 0)},
                    {'label': 'قيد المعالجة', 'value': overview_stats.get('processing_transactions', 0)},
                    {'label': 'مكتملة', 'value': overview_stats.get('completed_transactions', 0)},
                    {'label': 'مؤجلة', 'value': overview_stats.get('delayed_transactions', 0)},
                    {'label': 'ملغية', 'value': overview_stats.get('cancelled_transactions', 0)}
                ]
                # إزالة العناصر ذات القيمة صفر
                status_data = [item for item in status_data if item['value'] > 0]

                if status_data:
                    status_chart = PieChartWidget(status_data, "توزيع حالات المعاملات")
                    self.charts_container.add_chart(status_chart)

            # رسم بياني عمودي لأنواع التأشيرات
            visa_stats = self.statistics_service.get_visa_type_statistics()
            if visa_stats:
                visa_chart_data = [
                    {'label': item['visa_type'], 'value': item['total_count']}
                    for item in visa_stats[:6]  # أول 6 أنواع فقط
                ]
                if visa_chart_data:
                    visa_chart = BarChartWidget(visa_chart_data, "أكثر أنواع التأشيرات طلباً")
                    self.charts_container.add_chart(visa_chart)

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الرسوم البيانية: {str(e)}")
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # الحصول على الإحصائيات من الخدمة
            stats = self.statistics_service.get_overview_statistics()

            if stats:
                self.stats_data.update(stats)

                # تحديث البطاقات
                self.total_card.update_value(str(stats.get('total_transactions', 0)))
                self.new_card.update_value(str(stats.get('new_transactions', 0)))
                self.processing_card.update_value(str(stats.get('processing_transactions', 0)))
                self.completed_card.update_value(str(stats.get('completed_transactions', 0)))
                self.overdue_card.update_value(str(stats.get('overdue_transactions', 0)))
                self.completion_card.update_value(f"{stats.get('completion_rate', 0)}%")

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإحصائيات: {str(e)}")
    
    def load_recent_transactions(self):
        """تحميل المعاملات الحديثة"""
        try:
            # استعلام المعاملات الحديثة
            recent_query = """
                SELECT 
                    t.head_incoming_no,
                    SUBSTR(t.subject, 1, 50) || CASE WHEN LENGTH(t.subject) > 50 THEN '...' ELSE '' END as subject,
                    t.head_incoming_date,
                    rs.request_status,
                    u.full_name as researcher_name
                FROM transactions t
                LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
                LEFT JOIN users u ON t.researcher_1_id = u.user_id
                ORDER BY t.created_at DESC
                LIMIT 10
            """
            
            results = self.connection_manager.execute_query(recent_query)
            
            # تحديث الجدول
            self.recent_table.setRowCount(len(results))
            
            for row, transaction in enumerate(results):
                self.recent_table.setItem(row, 0, QTableWidgetItem(str(transaction.get('head_incoming_no', ''))))
                self.recent_table.setItem(row, 1, QTableWidgetItem(str(transaction.get('subject', ''))))
                self.recent_table.setItem(row, 2, QTableWidgetItem(str(transaction.get('head_incoming_date', ''))))
                self.recent_table.setItem(row, 3, QTableWidgetItem(str(transaction.get('request_status', ''))))
                self.recent_table.setItem(row, 4, QTableWidgetItem(str(transaction.get('researcher_name', ''))))
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل المعاملات الحديثة: {str(e)}")
    
    def start_refresh_timer(self):
        """بدء مؤقت التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh)
        self.refresh_timer.start(300000)  # كل 5 دقائق
    
    def refresh(self):
        """تحديث بيانات لوحة التحكم"""
        self.load_dashboard_data()
