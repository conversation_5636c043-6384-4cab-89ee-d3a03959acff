# -*- coding: utf-8 -*-
"""
مستودعات الجداول المساعدة
Lookup Tables Repositories

تدير عمليات قاعدة البيانات للجداول المساعدة
"""

from typing import List, Dict, Optional
from database.base_repository import BaseRepository

class VisaTypeRepository(BaseRepository):
    """مستودع أنواع التأشيرات"""
    
    def get_table_name(self) -> str:
        return 'visa_types'
    
    def get_active_visa_types(self) -> List[Dict]:
        """الحصول على أنواع التأشيرات النشطة"""
        return self.get_active_records()
    
    def find_by_name(self, visa_type: str) -> Optional[Dict]:
        """البحث عن نوع تأشيرة بالاسم"""
        results = self.find_all("visa_type = ?", (visa_type,))
        return results[0] if results else None

class ReceivedFromRepository(BaseRepository):
    """مستودع مصادر الورود"""
    
    def get_table_name(self) -> str:
        return 'received_from_sources'
    
    def get_active_sources(self) -> List[Dict]:
        """الحصول على مصادر الورود النشطة"""
        return self.get_active_records()
    
    def find_by_name(self, received_from: str) -> Optional[Dict]:
        """البحث عن مصدر ورود بالاسم"""
        results = self.find_all("received_from = ?", (received_from,))
        return results[0] if results else None

class ActionTakenRepository(BaseRepository):
    """مستودع الإجراءات المتخذة"""
    
    def get_table_name(self) -> str:
        return 'actions_taken'
    
    def get_active_actions(self) -> List[Dict]:
        """الحصول على الإجراءات النشطة"""
        return self.get_active_records()
    
    def find_by_name(self, action_taken: str) -> Optional[Dict]:
        """البحث عن إجراء بالاسم"""
        results = self.find_all("action_taken = ?", (action_taken,))
        return results[0] if results else None

class RequestStatusRepository(BaseRepository):
    """مستودع حالات الطلبات"""
    
    def get_table_name(self) -> str:
        return 'request_statuses'
    
    def get_active_statuses(self) -> List[Dict]:
        """الحصول على الحالات النشطة"""
        return self.get_active_records()
    
    def find_by_name(self, request_status: str) -> Optional[Dict]:
        """البحث عن حالة بالاسم"""
        results = self.find_all("request_status = ?", (request_status,))
        return results[0] if results else None
    
    def get_status_with_color(self, status_id: int) -> Optional[Dict]:
        """الحصول على الحالة مع اللون"""
        return self.find_by_id(status_id)

class LookupService:
    """خدمة الجداول المساعدة"""
    
    def __init__(self, connection_manager):
        """
        تهيئة خدمة الجداول المساعدة
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        
        # إنشاء المستودعات
        self.visa_types = VisaTypeRepository(connection_manager)
        self.received_from = ReceivedFromRepository(connection_manager)
        self.actions_taken = ActionTakenRepository(connection_manager)
        self.request_statuses = RequestStatusRepository(connection_manager)
    
    def get_all_lookup_data(self) -> Dict[str, List[Dict]]:
        """
        الحصول على جميع بيانات الجداول المساعدة
        
        Returns:
            قاموس يحتوي على جميع البيانات المساعدة
        """
        try:
            return {
                'visa_types': self.visa_types.get_active_visa_types(),
                'received_from_sources': self.received_from.get_active_sources(),
                'actions_taken': self.actions_taken.get_active_actions(),
                'request_statuses': self.request_statuses.get_active_statuses()
            }
        except Exception as e:
            return {
                'visa_types': [],
                'received_from_sources': [],
                'actions_taken': [],
                'request_statuses': []
            }
    
    def get_combo_data(self, table_name: str) -> List[Dict]:
        """
        الحصول على بيانات للقوائم المنسدلة
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            قائمة بالبيانات مناسبة للقوائم المنسدلة
        """
        try:
            if table_name == 'visa_types':
                return self.visa_types.get_active_visa_types()
            elif table_name == 'received_from_sources':
                return self.received_from.get_active_sources()
            elif table_name == 'actions_taken':
                return self.actions_taken.get_active_actions()
            elif table_name == 'request_statuses':
                return self.request_statuses.get_active_statuses()
            else:
                return []
        except Exception:
            return []
    
    def refresh_cache(self):
        """تحديث ذاكرة التخزين المؤقت"""
        # يمكن إضافة آلية تخزين مؤقت هنا إذا لزم الأمر
        pass
