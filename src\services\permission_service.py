# -*- coding: utf-8 -*-
"""
خدمة الصلاحيات
Permission Service

تدير صلاحيات المستخدمين والوصول للوظائف
"""

from typing import Dict, List, Optional, Set
from enum import Enum

from utils.logger import Logger

class Permission(Enum):
    """تعداد الصلاحيات"""
    
    # صلاحيات المعاملات
    VIEW_TRANSACTIONS = "view_transactions"
    CREATE_TRANSACTIONS = "create_transactions"
    EDIT_TRANSACTIONS = "edit_transactions"
    DELETE_TRANSACTIONS = "delete_transactions"
    ASSIGN_TRANSACTIONS = "assign_transactions"
    
    # صلاحيات المستخدمين
    VIEW_USERS = "view_users"
    CREATE_USERS = "create_users"
    EDIT_USERS = "edit_users"
    DELETE_USERS = "delete_users"
    MANAGE_PERMISSIONS = "manage_permissions"
    
    # صلاحيات التقارير
    VIEW_REPORTS = "view_reports"
    EXPORT_REPORTS = "export_reports"
    ADVANCED_REPORTS = "advanced_reports"
    
    # صلاحيات النظام
    SYSTEM_SETTINGS = "system_settings"
    DATABASE_BACKUP = "database_backup"
    VIEW_LOGS = "view_logs"
    SYNC_DATA = "sync_data"
    
    # صلاحيات الجداول المساعدة
    MANAGE_LOOKUP_TABLES = "manage_lookup_tables"

class Role(Enum):
    """تعداد الأدوار"""
    
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"

class PermissionService:
    """خدمة الصلاحيات"""
    
    def __init__(self):
        self.logger = Logger(__name__)
        self._setup_role_permissions()
    
    def _setup_role_permissions(self):
        """إعداد صلاحيات الأدوار"""
        
        # صلاحيات المدير - جميع الصلاحيات
        self.admin_permissions = {
            Permission.VIEW_TRANSACTIONS,
            Permission.CREATE_TRANSACTIONS,
            Permission.EDIT_TRANSACTIONS,
            Permission.DELETE_TRANSACTIONS,
            Permission.ASSIGN_TRANSACTIONS,
            
            Permission.VIEW_USERS,
            Permission.CREATE_USERS,
            Permission.EDIT_USERS,
            Permission.DELETE_USERS,
            Permission.MANAGE_PERMISSIONS,
            
            Permission.VIEW_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.ADVANCED_REPORTS,
            
            Permission.SYSTEM_SETTINGS,
            Permission.DATABASE_BACKUP,
            Permission.VIEW_LOGS,
            Permission.SYNC_DATA,
            
            Permission.MANAGE_LOOKUP_TABLES
        }
        
        # صلاحيات المستخدم العادي
        self.user_permissions = {
            Permission.VIEW_TRANSACTIONS,
            Permission.CREATE_TRANSACTIONS,
            Permission.EDIT_TRANSACTIONS,  # للمعاملات المسندة له فقط
            
            Permission.VIEW_REPORTS,
            Permission.EXPORT_REPORTS,
            
            Permission.SYNC_DATA
        }
        
        # صلاحيات المشاهد فقط
        self.viewer_permissions = {
            Permission.VIEW_TRANSACTIONS,
            Permission.VIEW_REPORTS
        }
        
        # خريطة الأدوار والصلاحيات
        self.role_permissions = {
            Role.ADMIN: self.admin_permissions,
            Role.USER: self.user_permissions,
            Role.VIEWER: self.viewer_permissions
        }
    
    def get_role_from_string(self, role_string: str) -> Optional[Role]:
        """
        تحويل النص إلى دور
        
        Args:
            role_string: النص
            
        Returns:
            الدور أو None
        """
        try:
            return Role(role_string.lower())
        except ValueError:
            return None
    
    def get_user_permissions(self, user_role: str) -> Set[Permission]:
        """
        الحصول على صلاحيات المستخدم
        
        Args:
            user_role: دور المستخدم
            
        Returns:
            مجموعة الصلاحيات
        """
        role = self.get_role_from_string(user_role)
        if role:
            return self.role_permissions.get(role, set())
        return set()
    
    def has_permission(self, user_role: str, required_permission: Permission) -> bool:
        """
        التحقق من وجود صلاحية
        
        Args:
            user_role: دور المستخدم
            required_permission: الصلاحية المطلوبة
            
        Returns:
            True إذا كان المستخدم يملك الصلاحية
        """
        user_permissions = self.get_user_permissions(user_role)
        return required_permission in user_permissions
    
    def can_view_transactions(self, user_role: str) -> bool:
        """التحقق من صلاحية عرض المعاملات"""
        return self.has_permission(user_role, Permission.VIEW_TRANSACTIONS)
    
    def can_create_transactions(self, user_role: str) -> bool:
        """التحقق من صلاحية إنشاء المعاملات"""
        return self.has_permission(user_role, Permission.CREATE_TRANSACTIONS)
    
    def can_edit_transactions(self, user_role: str, transaction_user_id: int = None, 
                             current_user_id: int = None) -> bool:
        """
        التحقق من صلاحية تعديل المعاملات
        
        Args:
            user_role: دور المستخدم
            transaction_user_id: معرف مستخدم المعاملة
            current_user_id: معرف المستخدم الحالي
            
        Returns:
            True إذا كان يمكن التعديل
        """
        if not self.has_permission(user_role, Permission.EDIT_TRANSACTIONS):
            return False
        
        # المدير يمكنه تعديل جميع المعاملات
        if user_role == Role.ADMIN.value:
            return True
        
        # المستخدم العادي يمكنه تعديل معاملاته فقط
        if transaction_user_id and current_user_id:
            return transaction_user_id == current_user_id
        
        return True
    
    def can_delete_transactions(self, user_role: str) -> bool:
        """التحقق من صلاحية حذف المعاملات"""
        return self.has_permission(user_role, Permission.DELETE_TRANSACTIONS)
    
    def can_assign_transactions(self, user_role: str) -> bool:
        """التحقق من صلاحية إسناد المعاملات"""
        return self.has_permission(user_role, Permission.ASSIGN_TRANSACTIONS)
    
    def can_manage_users(self, user_role: str) -> bool:
        """التحقق من صلاحية إدارة المستخدمين"""
        return self.has_permission(user_role, Permission.MANAGE_PERMISSIONS)
    
    def can_view_reports(self, user_role: str) -> bool:
        """التحقق من صلاحية عرض التقارير"""
        return self.has_permission(user_role, Permission.VIEW_REPORTS)
    
    def can_export_reports(self, user_role: str) -> bool:
        """التحقق من صلاحية تصدير التقارير"""
        return self.has_permission(user_role, Permission.EXPORT_REPORTS)
    
    def can_access_system_settings(self, user_role: str) -> bool:
        """التحقق من صلاحية الوصول لإعدادات النظام"""
        return self.has_permission(user_role, Permission.SYSTEM_SETTINGS)
    
    def can_manage_lookup_tables(self, user_role: str) -> bool:
        """التحقق من صلاحية إدارة الجداول المساعدة"""
        return self.has_permission(user_role, Permission.MANAGE_LOOKUP_TABLES)
    
    def get_accessible_menu_items(self, user_role: str) -> List[Dict]:
        """
        الحصول على عناصر القائمة المتاحة للمستخدم
        
        Args:
            user_role: دور المستخدم
            
        Returns:
            قائمة بعناصر القائمة
        """
        menu_items = []
        
        # لوحة التحكم - متاحة للجميع
        menu_items.append({
            'id': 'dashboard',
            'title': 'لوحة التحكم',
            'icon': 'dashboard',
            'url': '/dashboard'
        })
        
        # المعاملات
        if self.can_view_transactions(user_role):
            transactions_submenu = []
            
            if self.can_view_transactions(user_role):
                transactions_submenu.append({
                    'id': 'view_transactions',
                    'title': 'عرض المعاملات',
                    'url': '/transactions'
                })
            
            if self.can_create_transactions(user_role):
                transactions_submenu.append({
                    'id': 'create_transaction',
                    'title': 'إضافة معاملة',
                    'url': '/transactions/create'
                })
            
            menu_items.append({
                'id': 'transactions',
                'title': 'المعاملات',
                'icon': 'transactions',
                'submenu': transactions_submenu
            })
        
        # التقارير
        if self.can_view_reports(user_role):
            reports_submenu = []
            
            reports_submenu.append({
                'id': 'basic_reports',
                'title': 'التقارير الأساسية',
                'url': '/reports/basic'
            })
            
            if self.has_permission(user_role, Permission.ADVANCED_REPORTS):
                reports_submenu.append({
                    'id': 'advanced_reports',
                    'title': 'التقارير المتقدمة',
                    'url': '/reports/advanced'
                })
            
            menu_items.append({
                'id': 'reports',
                'title': 'التقارير',
                'icon': 'reports',
                'submenu': reports_submenu
            })
        
        # الإدارة - للمديرين فقط
        if user_role == Role.ADMIN.value:
            admin_submenu = []
            
            if self.can_manage_users(user_role):
                admin_submenu.append({
                    'id': 'manage_users',
                    'title': 'إدارة المستخدمين',
                    'url': '/admin/users'
                })
            
            if self.can_manage_lookup_tables(user_role):
                admin_submenu.append({
                    'id': 'lookup_tables',
                    'title': 'الجداول المساعدة',
                    'url': '/admin/lookup-tables'
                })
            
            if self.can_access_system_settings(user_role):
                admin_submenu.append({
                    'id': 'system_settings',
                    'title': 'إعدادات النظام',
                    'url': '/admin/settings'
                })
            
            admin_submenu.append({
                'id': 'system_logs',
                'title': 'سجلات النظام',
                'url': '/admin/logs'
            })
            
            menu_items.append({
                'id': 'admin',
                'title': 'الإدارة',
                'icon': 'admin',
                'submenu': admin_submenu
            })
        
        return menu_items
    
    def get_permission_description(self, permission: Permission) -> str:
        """
        الحصول على وصف الصلاحية
        
        Args:
            permission: الصلاحية
            
        Returns:
            وصف الصلاحية
        """
        descriptions = {
            Permission.VIEW_TRANSACTIONS: "عرض المعاملات",
            Permission.CREATE_TRANSACTIONS: "إنشاء معاملات جديدة",
            Permission.EDIT_TRANSACTIONS: "تعديل المعاملات",
            Permission.DELETE_TRANSACTIONS: "حذف المعاملات",
            Permission.ASSIGN_TRANSACTIONS: "إسناد المعاملات للباحثين",
            
            Permission.VIEW_USERS: "عرض المستخدمين",
            Permission.CREATE_USERS: "إنشاء مستخدمين جدد",
            Permission.EDIT_USERS: "تعديل بيانات المستخدمين",
            Permission.DELETE_USERS: "حذف المستخدمين",
            Permission.MANAGE_PERMISSIONS: "إدارة صلاحيات المستخدمين",
            
            Permission.VIEW_REPORTS: "عرض التقارير",
            Permission.EXPORT_REPORTS: "تصدير التقارير",
            Permission.ADVANCED_REPORTS: "التقارير المتقدمة",
            
            Permission.SYSTEM_SETTINGS: "إعدادات النظام",
            Permission.DATABASE_BACKUP: "نسخ احتياطي لقاعدة البيانات",
            Permission.VIEW_LOGS: "عرض سجلات النظام",
            Permission.SYNC_DATA: "مزامنة البيانات",
            
            Permission.MANAGE_LOOKUP_TABLES: "إدارة الجداول المساعدة"
        }
        
        return descriptions.get(permission, permission.value)
