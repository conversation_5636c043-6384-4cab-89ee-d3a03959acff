# -*- coding: utf-8 -*-
"""
مستودع المعاملات
Transaction Repository

يدير عمليات قاعدة البيانات الخاصة بالمعاملات
"""

from typing import List, Dict, Optional, Tuple
from datetime import datetime, date

from database.base_repository import BaseRepository

class TransactionRepository(BaseRepository):
    """مستودع المعاملات"""
    
    def get_table_name(self) -> str:
        """إرجاع اسم جدول المعاملات"""
        return 'transactions'
    
    def get_primary_key(self) -> str:
        """إرجاع اسم المفتاح الأساسي"""
        return 'id'
    
    def create_transaction(self, transaction_data: Dict) -> Optional[int]:
        """
        إنشاء معاملة جديدة
        
        Args:
            transaction_data: بيانات المعاملة
            
        Returns:
            معرف المعاملة الجديدة
        """
        try:
            # التحقق من عدم تكرار رقم الوارد
            existing = self.find_by_incoming_number(transaction_data.get('head_incoming_no'))
            if existing:
                raise ValueError(f"رقم الوارد '{transaction_data.get('head_incoming_no')}' موجود بالفعل")
            
            # إضافة تاريخ الإنشاء والمنشئ
            if self.connection_manager.is_online:
                # MySQL
                transaction_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                transaction_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            else:
                # SQLite
                transaction_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                transaction_data['updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            return self.insert(transaction_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المعاملة: {str(e)}")
            raise
    
    def find_by_incoming_number(self, incoming_no: str) -> Optional[Dict]:
        """
        البحث عن معاملة برقم الوارد
        
        Args:
            incoming_no: رقم الوارد
            
        Returns:
            بيانات المعاملة أو None
        """
        try:
            results = self.find_all("head_incoming_no = ?", (incoming_no,))
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المعاملة برقم الوارد {incoming_no}: {str(e)}")
            raise
    
    def get_transactions_with_details(self, limit: int = None, offset: int = 0, 
                                    where_clause: str = "", params: tuple = None) -> List[Dict]:
        """
        الحصول على المعاملات مع التفاصيل الكاملة
        
        Args:
            limit: حد النتائج
            offset: إزاحة النتائج
            where_clause: شرط WHERE إضافي
            params: معاملات الشرط
            
        Returns:
            قائمة بالمعاملات مع التفاصيل
        """
        try:
            # استعلام شامل مع الربط
            query = """
                SELECT 
                    t.id,
                    t.head_incoming_no,
                    t.head_incoming_date,
                    t.subject,
                    t.researcher_notes,
                    t.priority,
                    t.due_date,
                    t.completion_date,
                    t.created_at,
                    t.updated_at,
                    
                    -- بيانات المستخدمين
                    u1.full_name as data_entry_user,
                    u2.full_name as researcher_1_name,
                    u3.full_name as researcher_2_name,
                    u4.full_name as created_by_name,
                    u5.full_name as updated_by_name,
                    
                    -- بيانات الجداول المساعدة
                    vt.visa_type,
                    rfs.received_from,
                    at.action_taken,
                    rs.request_status,
                    rs.status_color,
                    
                    -- المعرفات للتعديل
                    t.user_id,
                    t.researcher_1_id,
                    t.researcher_2_id,
                    t.visa_type_id,
                    t.received_from_id,
                    t.action_taken_id,
                    t.request_status_id,
                    t.created_by,
                    t.updated_by
                    
                FROM transactions t
                LEFT JOIN users u1 ON t.user_id = u1.user_id
                LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
                LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
                LEFT JOIN users u4 ON t.created_by = u4.user_id
                LEFT JOIN users u5 ON t.updated_by = u5.user_id
                LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
                LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
                LEFT JOIN actions_taken at ON t.action_taken_id = at.id
                LEFT JOIN request_statuses rs ON t.request_status_id = rs.id
            """
            
            # إضافة شرط WHERE إضافي
            if where_clause:
                query += f" WHERE {where_clause}"
            
            # ترتيب النتائج
            query += " ORDER BY t.created_at DESC"
            
            # إضافة الحد والإزاحة
            if limit:
                query += f" LIMIT {limit}"
                if offset > 0:
                    query += f" OFFSET {offset}"
            
            return self.connection_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المعاملات مع التفاصيل: {str(e)}")
            raise
    
    def search_transactions(self, search_term: str, search_fields: List[str] = None) -> List[Dict]:
        """
        البحث في المعاملات
        
        Args:
            search_term: مصطلح البحث
            search_fields: الحقول المراد البحث فيها
            
        Returns:
            قائمة بنتائج البحث
        """
        try:
            if not search_fields:
                search_fields = ['head_incoming_no', 'subject', 'researcher_notes']
            
            # إنشاء شروط البحث
            conditions = []
            params = []
            
            for field in search_fields:
                conditions.append(f"t.{field} LIKE ?")
                params.append(f"%{search_term}%")
            
            where_clause = " OR ".join(conditions)
            return self.get_transactions_with_details(where_clause=where_clause, params=tuple(params))
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث في المعاملات: {str(e)}")
            raise
    
    def filter_transactions(self, filters: Dict) -> List[Dict]:
        """
        تصفية المعاملات
        
        Args:
            filters: قاموس بمعايير التصفية
            
        Returns:
            قائمة بالمعاملات المصفاة
        """
        try:
            conditions = []
            params = []
            
            # تصفية حسب الحالة
            if filters.get('status_id'):
                conditions.append("t.request_status_id = ?")
                params.append(filters['status_id'])
            
            # تصفية حسب الباحث
            if filters.get('researcher_id'):
                conditions.append("(t.researcher_1_id = ? OR t.researcher_2_id = ?)")
                params.extend([filters['researcher_id'], filters['researcher_id']])
            
            # تصفية حسب نوع التأشيرة
            if filters.get('visa_type_id'):
                conditions.append("t.visa_type_id = ?")
                params.append(filters['visa_type_id'])
            
            # تصفية حسب جهة الورود
            if filters.get('received_from_id'):
                conditions.append("t.received_from_id = ?")
                params.append(filters['received_from_id'])
            
            # تصفية حسب الأولوية
            if filters.get('priority'):
                conditions.append("t.priority = ?")
                params.append(filters['priority'])
            
            # تصفية حسب نطاق التاريخ
            if filters.get('date_from'):
                conditions.append("t.head_incoming_date >= ?")
                params.append(filters['date_from'])
            
            if filters.get('date_to'):
                conditions.append("t.head_incoming_date <= ?")
                params.append(filters['date_to'])
            
            # تصفية المعاملات المتأخرة
            if filters.get('overdue_only'):
                if self.connection_manager.is_online:
                    conditions.append("t.due_date < CURDATE() AND rs.request_status != 'مكتمل'")
                else:
                    conditions.append("julianday(t.due_date) < julianday('now') AND rs.request_status != 'مكتمل'")
            
            where_clause = " AND ".join(conditions) if conditions else ""
            return self.get_transactions_with_details(where_clause=where_clause, params=tuple(params))
            
        except Exception as e:
            self.logger.error(f"خطأ في تصفية المعاملات: {str(e)}")
            raise
    
    def get_user_transactions(self, user_id: int, role: str = 'researcher') -> List[Dict]:
        """
        الحصول على معاملات مستخدم معين
        
        Args:
            user_id: معرف المستخدم
            role: دور المستخدم (researcher, data_entry)
            
        Returns:
            قائمة بمعاملات المستخدم
        """
        try:
            if role == 'researcher':
                where_clause = "(t.researcher_1_id = ? OR t.researcher_2_id = ?)"
                params = (user_id, user_id)
            elif role == 'data_entry':
                where_clause = "t.user_id = ?"
                params = (user_id,)
            else:
                where_clause = "(t.user_id = ? OR t.researcher_1_id = ? OR t.researcher_2_id = ?)"
                params = (user_id, user_id, user_id)
            
            return self.get_transactions_with_details(where_clause=where_clause, params=params)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معاملات المستخدم {user_id}: {str(e)}")
            raise
    
    def update_transaction_status(self, transaction_id: int, status_id: int, 
                                action_id: int = None, notes: str = None, 
                                updated_by: int = None) -> bool:
        """
        تحديث حالة المعاملة
        
        Args:
            transaction_id: معرف المعاملة
            status_id: معرف الحالة الجديدة
            action_id: معرف الإجراء المتخذ
            notes: ملاحظات إضافية
            updated_by: من قام بالتحديث
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            update_data = {
                'request_status_id': status_id,
                'updated_by': updated_by
            }
            
            if action_id:
                update_data['action_taken_id'] = action_id
            
            if notes:
                update_data['researcher_notes'] = notes
            
            # إذا كانت الحالة "مكتمل" أضف تاريخ الإنجاز
            status_name = self.get_status_name(status_id)
            if status_name == 'مكتمل':
                update_data['completion_date'] = date.today().strftime('%Y-%m-%d')
            
            return self.update(transaction_id, update_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة المعاملة {transaction_id}: {str(e)}")
            raise
    
    def assign_researcher(self, transaction_id: int, researcher_id: int, 
                         position: int = 1, assigned_by: int = None) -> bool:
        """
        إسناد معاملة لباحث
        
        Args:
            transaction_id: معرف المعاملة
            researcher_id: معرف الباحث
            position: موقع الباحث (1 أو 2)
            assigned_by: من قام بالإسناد
            
        Returns:
            True إذا تم الإسناد بنجاح
        """
        try:
            field_name = f'researcher_{position}_id'
            update_data = {
                field_name: researcher_id,
                'updated_by': assigned_by
            }
            
            return self.update(transaction_id, update_data)
            
        except Exception as e:
            self.logger.error(f"خطأ في إسناد المعاملة {transaction_id} للباحث {researcher_id}: {str(e)}")
            raise
    
    def get_status_name(self, status_id: int) -> Optional[str]:
        """الحصول على اسم الحالة"""
        try:
            query = "SELECT request_status FROM request_statuses WHERE id = ?"
            results = self.connection_manager.execute_query(query, (status_id,))
            return results[0]['request_status'] if results else None
        except Exception:
            return None
    
    def get_overdue_transactions(self) -> List[Dict]:
        """الحصول على المعاملات المتأخرة"""
        try:
            if self.connection_manager.is_online:
                where_clause = "t.due_date < CURDATE() AND rs.request_status != 'مكتمل'"
            else:
                where_clause = "julianday(t.due_date) < julianday('now') AND rs.request_status != 'مكتمل'"
            
            return self.get_transactions_with_details(where_clause=where_clause)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المعاملات المتأخرة: {str(e)}")
            raise
    
    def get_transactions_by_date_range(self, start_date: str, end_date: str) -> List[Dict]:
        """الحصول على المعاملات في نطاق تاريخي"""
        try:
            where_clause = "t.head_incoming_date BETWEEN ? AND ?"
            params = (start_date, end_date)
            
            return self.get_transactions_with_details(where_clause=where_clause, params=params)
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المعاملات في النطاق {start_date} - {end_date}: {str(e)}")
            raise
