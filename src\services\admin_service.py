# -*- coding: utf-8 -*-
"""
خدمة إدارة النظام
Admin Service

تدير عمليات المدير مثل إدارة المستخدمين والجداول المساعدة
"""

from typing import List, Dict, Optional, Tuple
from datetime import datetime, date
import hashlib

from database.user_repository import UserRepository
from database.lookup_repositories import LookupService
from database.transaction_repository import TransactionRepository
from services.statistics_service import StatisticsService
from utils.logger import Logger

class AdminService:
    """خدمة إدارة النظام"""
    
    def __init__(self, connection_manager):
        """
        تهيئة خدمة الإدارة
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        self.user_repository = UserRepository(connection_manager)
        self.lookup_service = LookupService(connection_manager)
        self.transaction_repository = TransactionRepository(connection_manager)
        self.statistics_service = StatisticsService(connection_manager)
        self.logger = Logger(__name__)
    
    # ==================== إدارة المستخدمين ====================
    
    def get_all_users_with_stats(self) -> List[Dict]:
        """
        الحصول على جميع المستخدمين مع إحصائياتهم
        
        Returns:
            قائمة بالمستخدمين مع الإحصائيات
        """
        try:
            users = self.user_repository.get_all_users()
            
            for user in users:
                # إحصائيات المستخدم
                user_stats = self.get_user_statistics(user['user_id'])
                user.update(user_stats)
                
                # معلومات إضافية
                user['last_login_formatted'] = self.format_datetime(user.get('last_login'))
                user['created_at_formatted'] = self.format_datetime(user.get('created_at'))
                user['status_text'] = 'نشط' if user.get('is_active') else 'غير نشط'
                user['permission_text'] = self.get_permission_text(user.get('permission'))
            
            return users
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على المستخدمين مع الإحصائيات: {str(e)}")
            return []
    
    def get_user_statistics(self, user_id: int) -> Dict:
        """
        الحصول على إحصائيات مستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            إحصائيات المستخدم
        """
        try:
            stats = {
                'total_transactions': 0,
                'completed_transactions': 0,
                'pending_transactions': 0,
                'completion_rate': 0.0,
                'avg_completion_days': 0.0
            }
            
            # المعاملات المسندة للمستخدم
            user_transactions = self.transaction_repository.get_user_transactions(user_id)
            
            if user_transactions:
                stats['total_transactions'] = len(user_transactions)
                
                completed = [t for t in user_transactions if t.get('completion_date')]
                stats['completed_transactions'] = len(completed)
                stats['pending_transactions'] = stats['total_transactions'] - stats['completed_transactions']
                
                if stats['total_transactions'] > 0:
                    stats['completion_rate'] = (stats['completed_transactions'] / stats['total_transactions']) * 100
                
                # متوسط أيام الإنجاز
                if completed:
                    total_days = 0
                    valid_completions = 0
                    
                    for transaction in completed:
                        try:
                            start_date = datetime.strptime(transaction['head_incoming_date'], '%Y-%m-%d')
                            end_date = datetime.strptime(transaction['completion_date'], '%Y-%m-%d')
                            days = (end_date - start_date).days
                            if days >= 0:
                                total_days += days
                                valid_completions += 1
                        except:
                            continue
                    
                    if valid_completions > 0:
                        stats['avg_completion_days'] = total_days / valid_completions
            
            return stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات المستخدم {user_id}: {str(e)}")
            return {
                'total_transactions': 0,
                'completed_transactions': 0,
                'pending_transactions': 0,
                'completion_rate': 0.0,
                'avg_completion_days': 0.0
            }
    
    def create_user(self, user_data: Dict) -> Tuple[bool, str]:
        """
        إنشاء مستخدم جديد
        
        Args:
            user_data: بيانات المستخدم
            
        Returns:
            (نجح, رسالة)
        """
        try:
            # التحقق من البيانات المطلوبة
            required_fields = ['user_name', 'user_pass', 'full_name', 'email', 'permission']
            for field in required_fields:
                if not user_data.get(field):
                    return False, f"الحقل '{field}' مطلوب"
            
            # التحقق من عدم تكرار اسم المستخدم
            existing_user = self.user_repository.find_by_username(user_data['user_name'])
            if existing_user:
                return False, f"اسم المستخدم '{user_data['user_name']}' موجود بالفعل"
            
            # التحقق من عدم تكرار البريد الإلكتروني
            existing_email = self.user_repository.find_by_email(user_data['email'])
            if existing_email:
                return False, f"البريد الإلكتروني '{user_data['email']}' موجود بالفعل"
            
            # إنشاء المستخدم
            user_id = self.user_repository.create_user(
                username=user_data['user_name'],
                password=user_data['user_pass'],
                full_name=user_data['full_name'],
                email=user_data['email'],
                phone=user_data.get('phone', ''),
                permission=user_data['permission']
            )
            
            if user_id:
                return True, "تم إنشاء المستخدم بنجاح"
            else:
                return False, "فشل في إنشاء المستخدم"
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المستخدم: {str(e)}")
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"
    
    def update_user(self, user_id: int, user_data: Dict) -> Tuple[bool, str]:
        """
        تحديث بيانات مستخدم
        
        Args:
            user_id: معرف المستخدم
            user_data: البيانات الجديدة
            
        Returns:
            (نجح, رسالة)
        """
        try:
            # التحقق من وجود المستخدم
            existing_user = self.user_repository.find_by_id(user_id)
            if not existing_user:
                return False, "المستخدم غير موجود"
            
            # التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
            if 'user_name' in user_data and user_data['user_name'] != existing_user['user_name']:
                duplicate_user = self.user_repository.find_by_username(user_data['user_name'])
                if duplicate_user:
                    return False, f"اسم المستخدم '{user_data['user_name']}' موجود بالفعل"
            
            # التحقق من عدم تكرار البريد الإلكتروني (إذا تم تغييره)
            if 'email' in user_data and user_data['email'] != existing_user['email']:
                duplicate_email = self.user_repository.find_by_email(user_data['email'])
                if duplicate_email:
                    return False, f"البريد الإلكتروني '{user_data['email']}' موجود بالفعل"
            
            # تشفير كلمة المرور إذا تم تغييرها
            if 'user_pass' in user_data and user_data['user_pass']:
                user_data['user_pass'] = self.user_repository.hash_password(user_data['user_pass'])
            else:
                # إزالة كلمة المرور من البيانات إذا كانت فارغة
                user_data.pop('user_pass', None)
            
            # تحديث المستخدم
            success = self.user_repository.update(user_id, user_data)
            
            if success:
                return True, "تم تحديث المستخدم بنجاح"
            else:
                return False, "فشل في تحديث المستخدم"
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث المستخدم {user_id}: {str(e)}")
            return False, f"خطأ في تحديث المستخدم: {str(e)}"
    
    def toggle_user_status(self, user_id: int) -> Tuple[bool, str]:
        """
        تبديل حالة المستخدم (نشط/غير نشط)
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            (نجح, رسالة)
        """
        try:
            user = self.user_repository.find_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"
            
            new_status = 0 if user['is_active'] else 1
            success = self.user_repository.update(user_id, {'is_active': new_status})
            
            if success:
                status_text = "تم تفعيل" if new_status else "تم إلغاء تفعيل"
                return True, f"{status_text} المستخدم بنجاح"
            else:
                return False, "فشل في تغيير حالة المستخدم"
                
        except Exception as e:
            self.logger.error(f"خطأ في تبديل حالة المستخدم {user_id}: {str(e)}")
            return False, f"خطأ في تغيير حالة المستخدم: {str(e)}"
    
    def reset_user_password(self, user_id: int, new_password: str) -> Tuple[bool, str]:
        """
        إعادة تعيين كلمة مرور المستخدم
        
        Args:
            user_id: معرف المستخدم
            new_password: كلمة المرور الجديدة
            
        Returns:
            (نجح, رسالة)
        """
        try:
            if len(new_password) < 6:
                return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
            
            hashed_password = self.user_repository.hash_password(new_password)
            success = self.user_repository.update(user_id, {'user_pass': hashed_password})
            
            if success:
                return True, "تم إعادة تعيين كلمة المرور بنجاح"
            else:
                return False, "فشل في إعادة تعيين كلمة المرور"
                
        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين كلمة مرور المستخدم {user_id}: {str(e)}")
            return False, f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"
    
    # ==================== إدارة الجداول المساعدة ====================
    
    def get_lookup_table_data(self, table_name: str) -> List[Dict]:
        """
        الحصول على بيانات جدول مساعد
        
        Args:
            table_name: اسم الجدول
            
        Returns:
            بيانات الجدول
        """
        try:
            return self.lookup_service.get_combo_data(table_name)
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على بيانات الجدول {table_name}: {str(e)}")
            return []
    
    def add_lookup_item(self, table_name: str, item_data: Dict) -> Tuple[bool, str]:
        """
        إضافة عنصر لجدول مساعد
        
        Args:
            table_name: اسم الجدول
            item_data: بيانات العنصر
            
        Returns:
            (نجح, رسالة)
        """
        try:
            # الحصول على المستودع المناسب
            repository = self._get_lookup_repository(table_name)
            if not repository:
                return False, f"جدول غير مدعوم: {table_name}"
            
            # إضافة العنصر
            item_id = repository.insert(item_data)
            
            if item_id:
                return True, "تم إضافة العنصر بنجاح"
            else:
                return False, "فشل في إضافة العنصر"
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة عنصر للجدول {table_name}: {str(e)}")
            return False, f"خطأ في إضافة العنصر: {str(e)}"
    
    def update_lookup_item(self, table_name: str, item_id: int, item_data: Dict) -> Tuple[bool, str]:
        """
        تحديث عنصر في جدول مساعد
        
        Args:
            table_name: اسم الجدول
            item_id: معرف العنصر
            item_data: البيانات الجديدة
            
        Returns:
            (نجح, رسالة)
        """
        try:
            # الحصول على المستودع المناسب
            repository = self._get_lookup_repository(table_name)
            if not repository:
                return False, f"جدول غير مدعوم: {table_name}"
            
            # تحديث العنصر
            success = repository.update(item_id, item_data)
            
            if success:
                return True, "تم تحديث العنصر بنجاح"
            else:
                return False, "فشل في تحديث العنصر"
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث عنصر في الجدول {table_name}: {str(e)}")
            return False, f"خطأ في تحديث العنصر: {str(e)}"
    
    def delete_lookup_item(self, table_name: str, item_id: int) -> Tuple[bool, str]:
        """
        حذف عنصر من جدول مساعد
        
        Args:
            table_name: اسم الجدول
            item_id: معرف العنصر
            
        Returns:
            (نجح, رسالة)
        """
        try:
            # التحقق من عدم استخدام العنصر في المعاملات
            if self._is_lookup_item_used(table_name, item_id):
                return False, "لا يمكن حذف العنصر لأنه مستخدم في معاملات موجودة"
            
            # الحصول على المستودع المناسب
            repository = self._get_lookup_repository(table_name)
            if not repository:
                return False, f"جدول غير مدعوم: {table_name}"
            
            # حذف العنصر
            success = repository.delete(item_id)
            
            if success:
                return True, "تم حذف العنصر بنجاح"
            else:
                return False, "فشل في حذف العنصر"
                
        except Exception as e:
            self.logger.error(f"خطأ في حذف عنصر من الجدول {table_name}: {str(e)}")
            return False, f"خطأ في حذف العنصر: {str(e)}"
    
    def _get_lookup_repository(self, table_name: str):
        """الحصول على مستودع الجدول المساعد"""
        if table_name == 'visa_types':
            return self.lookup_service.visa_types
        elif table_name == 'received_from_sources':
            return self.lookup_service.received_from
        elif table_name == 'actions_taken':
            return self.lookup_service.actions_taken
        elif table_name == 'request_statuses':
            return self.lookup_service.request_statuses
        else:
            return None
    
    def _is_lookup_item_used(self, table_name: str, item_id: int) -> bool:
        """التحقق من استخدام عنصر الجدول المساعد في المعاملات"""
        try:
            field_mapping = {
                'visa_types': 'visa_type_id',
                'received_from_sources': 'received_from_id',
                'actions_taken': 'action_taken_id',
                'request_statuses': 'request_status_id'
            }
            
            field_name = field_mapping.get(table_name)
            if not field_name:
                return False
            
            # البحث في المعاملات
            query = f"SELECT COUNT(*) as count FROM transactions WHERE {field_name} = ?"
            results = self.connection_manager.execute_query(query, (item_id,))
            
            return results[0]['count'] > 0 if results else False
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من استخدام العنصر: {str(e)}")
            return True  # في حالة الخطأ، نفترض أنه مستخدم لتجنب الحذف الخاطئ
    
    # ==================== إحصائيات النظام ====================
    
    def get_system_statistics(self) -> Dict:
        """
        الحصول على إحصائيات النظام العامة
        
        Returns:
            إحصائيات النظام
        """
        try:
            stats = {}
            
            # إحصائيات المستخدمين
            all_users = self.user_repository.get_all_users()
            stats['total_users'] = len(all_users)
            stats['active_users'] = len([u for u in all_users if u.get('is_active')])
            stats['inactive_users'] = stats['total_users'] - stats['active_users']
            
            # إحصائيات المعاملات
            transaction_stats = self.statistics_service.get_overview_statistics()
            stats.update(transaction_stats)
            
            # إحصائيات الجداول المساعدة
            lookup_data = self.lookup_service.get_all_lookup_data()
            stats['visa_types_count'] = len(lookup_data.get('visa_types', []))
            stats['received_from_count'] = len(lookup_data.get('received_from_sources', []))
            stats['actions_count'] = len(lookup_data.get('actions_taken', []))
            stats['statuses_count'] = len(lookup_data.get('request_statuses', []))
            
            # معلومات قاعدة البيانات
            stats['database_type'] = 'MySQL' if self.connection_manager.is_online else 'SQLite'
            stats['last_backup'] = self.get_last_backup_date()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات النظام: {str(e)}")
            return {}
    
    def get_last_backup_date(self) -> Optional[str]:
        """الحصول على تاريخ آخر نسخة احتياطية"""
        try:
            # هذا مثال - يمكن تطوير نظام النسخ الاحتياطي لاحقاً
            return None
        except Exception:
            return None
    
    # ==================== وظائف مساعدة ====================
    
    def format_datetime(self, datetime_str: Optional[str]) -> str:
        """تنسيق التاريخ والوقت"""
        if not datetime_str:
            return "غير محدد"
        
        try:
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            return datetime_str
    
    def get_permission_text(self, permission: str) -> str:
        """الحصول على نص الصلاحية"""
        permission_map = {
            'admin': 'مدير',
            'user': 'مستخدم',
            'viewer': 'مشاهد'
        }
        return permission_map.get(permission, permission)
    
    def get_available_permissions(self) -> List[Dict]:
        """الحصول على الصلاحيات المتاحة"""
        return [
            {'value': 'admin', 'text': 'مدير'},
            {'value': 'user', 'text': 'مستخدم'},
            {'value': 'viewer', 'text': 'مشاهد'}
        ]
