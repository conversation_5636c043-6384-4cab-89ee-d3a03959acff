#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام النسخ الاحتياطي والاستعادة
Backup and Restore System Test

يختبر جميع وظائف النسخ الاحتياطي والاستعادة والجدولة
"""

import sys
import os
import time
import shutil
from datetime import datetime, timedelta
from pathlib import Path

# إضافة مسار src
sys.path.insert(0, 'src')

def test_backup_service():
    """اختبار خدمة النسخ الاحتياطي"""
    
    print("اختبار خدمة النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        # اختبار إعدادات النسخ الاحتياطي
        print("--- اختبار الإعدادات ---")
        settings = backup_service.get_backup_settings()
        print(f"✅ تحميل الإعدادات: {len(settings)} إعداد")
        
        # عرض الإعدادات الرئيسية
        key_settings = ['auto_backup_enabled', 'backup_time', 'max_backup_files', 'compress_backups']
        for key in key_settings:
            if key in settings:
                print(f"  - {key}: {settings[key]}")
        
        # اختبار إنشاء نسخة احتياطية
        print("\n--- اختبار إنشاء النسخة الاحتياطية ---")
        backup_name = f"test_backup_{int(time.time())}"
        
        success, message, backup_path = backup_service.create_backup(backup_name, True)
        
        if success:
            print(f"✅ إنشاء النسخة الاحتياطية: {backup_path}")
            
            # التحقق من وجود الملف
            if os.path.exists(backup_path):
                file_size = os.path.getsize(backup_path)
                print(f"  - حجم الملف: {file_size} بايت ({file_size / (1024*1024):.2f} MB)")
                
                # اختبار التحقق من السلامة
                print("\n--- اختبار التحقق من السلامة ---")
                is_valid, verify_message, verification_results = backup_service.verify_backup_integrity(backup_path)
                
                print(f"✅ التحقق من السلامة: {'سليمة' if is_valid else 'تالفة'}")
                print(f"  - الرسالة: {verify_message}")
                
                # عرض نتائج التحقق التفصيلية
                for check, result in verification_results.items():
                    status = "✅" if result else "❌"
                    print(f"  - {check}: {status}")
                
                # اختبار الاستعادة
                print("\n--- اختبار الاستعادة ---")
                restore_success, restore_message = backup_service.restore_backup(
                    backup_path, True, True, True
                )
                
                if restore_success:
                    print(f"✅ الاستعادة: {restore_message}")
                else:
                    print(f"❌ فشل الاستعادة: {restore_message}")
                
                # اختبار حذف النسخة الاحتياطية
                print("\n--- اختبار حذف النسخة الاحتياطية ---")
                delete_success, delete_message = backup_service.delete_backup(backup_path)
                
                if delete_success:
                    print(f"✅ حذف النسخة الاحتياطية: {delete_message}")
                else:
                    print(f"❌ فشل حذف النسخة الاحتياطية: {delete_message}")
                
            else:
                print(f"❌ الملف غير موجود: {backup_path}")
                return False
        else:
            print(f"❌ فشل إنشاء النسخة الاحتياطية: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_statistics():
    """اختبار إحصائيات النسخ الاحتياطي"""
    
    print("\nاختبار إحصائيات النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        # إنشاء عدة نسخ احتياطية للاختبار
        print("--- إنشاء نسخ احتياطية للاختبار ---")
        test_backups = []
        
        for i in range(3):
            backup_name = f"stats_test_{i}_{int(time.time())}"
            success, message, backup_path = backup_service.create_backup(backup_name, False)
            
            if success:
                test_backups.append(backup_path)
                print(f"✅ إنشاء نسخة احتياطية {i+1}: {os.path.basename(backup_path)}")
                time.sleep(1)  # فترة انتظار قصيرة
            else:
                print(f"❌ فشل إنشاء نسخة احتياطية {i+1}: {message}")
        
        # اختبار الإحصائيات
        print("\n--- اختبار الإحصائيات ---")
        stats = backup_service.get_backup_statistics()
        
        print(f"✅ إحصائيات النسخ الاحتياطي:")
        print(f"  - إجمالي النسخ الاحتياطية: {stats.get('total_backups', 0)}")
        print(f"  - الحجم الإجمالي: {stats.get('total_size_mb', 0)} MB")
        print(f"  - متوسط الحجم: {stats.get('average_size_mb', 0)} MB")
        print(f"  - تكرار النسخ الاحتياطي: {stats.get('backup_frequency', 'غير محدد')}")
        print(f"  - النسخ الاحتياطي التلقائي: {'مفعل' if stats.get('auto_backup_enabled') else 'معطل'}")
        
        # معلومات أحدث وأقدم نسخة احتياطية
        latest_backup = stats.get('latest_backup')
        if latest_backup:
            print(f"  - أحدث نسخة احتياطية: {latest_backup.get('backup_name', 'غير محدد')}")
        
        oldest_backup = stats.get('oldest_backup')
        if oldest_backup:
            print(f"  - أقدم نسخة احتياطية: {oldest_backup.get('backup_name', 'غير محدد')}")
        
        # اختبار قائمة النسخ الاحتياطية
        print("\n--- اختبار قائمة النسخ الاحتياطية ---")
        backup_list = backup_service.get_backup_list()
        print(f"✅ قائمة النسخ الاحتياطية: {len(backup_list)} نسخة")
        
        for i, backup in enumerate(backup_list[:5]):  # أول 5 نسخ
            name = backup.get('backup_name', 'غير محدد')
            size_mb = backup.get('file_size_mb', 0)
            db_type = backup.get('database_type', 'غير محدد')
            print(f"  {i+1}. {name} ({size_mb} MB, {db_type})")
        
        # تنظيف النسخ الاحتياطية التجريبية
        print("\n--- تنظيف النسخ الاحتياطية التجريبية ---")
        for backup_path in test_backups:
            try:
                if os.path.exists(backup_path):
                    success, message = backup_service.delete_backup(backup_path)
                    if success:
                        print(f"✅ حذف: {os.path.basename(backup_path)}")
                    else:
                        print(f"❌ فشل حذف: {os.path.basename(backup_path)}")
            except Exception as e:
                print(f"❌ خطأ في حذف {os.path.basename(backup_path)}: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إحصائيات النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_settings():
    """اختبار إعدادات النسخ الاحتياطي"""
    
    print("\nاختبار إعدادات النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        # اختبار الحصول على الإعدادات
        print("--- اختبار الحصول على الإعدادات ---")
        original_settings = backup_service.get_backup_settings()
        print(f"✅ الإعدادات الأصلية: {len(original_settings)} إعداد")
        
        # اختبار تحديث الإعدادات
        print("\n--- اختبار تحديث الإعدادات ---")
        new_settings = {
            'auto_backup_enabled': False,
            'backup_time': '03:30',
            'max_backup_files': 15,
            'compress_backups': False
        }
        
        success = backup_service.update_backup_settings(new_settings)
        
        if success:
            print("✅ تحديث الإعدادات: نجح")
            
            # التحقق من التحديث
            updated_settings = backup_service.get_backup_settings()
            for key, expected_value in new_settings.items():
                actual_value = updated_settings.get(key)
                if actual_value == expected_value:
                    print(f"  ✅ {key}: {actual_value}")
                else:
                    print(f"  ❌ {key}: متوقع {expected_value}, فعلي {actual_value}")
        else:
            print("❌ تحديث الإعدادات: فشل")
        
        # اختبار إعادة تعيين الإعدادات
        print("\n--- اختبار إعادة تعيين الإعدادات ---")
        reset_success = backup_service.repair_backup_settings()
        
        if reset_success:
            print("✅ إعادة تعيين الإعدادات: نجح")
            
            # التحقق من الإعادة
            reset_settings = backup_service.get_backup_settings()
            print(f"  - إجمالي الإعدادات بعد الإعادة: {len(reset_settings)}")
            
            # التحقق من بعض القيم الافتراضية
            default_checks = {
                'auto_backup_enabled': True,
                'backup_time': '02:00',
                'max_backup_files': 30,
                'compress_backups': True
            }
            
            for key, expected_value in default_checks.items():
                actual_value = reset_settings.get(key)
                if actual_value == expected_value:
                    print(f"  ✅ {key}: {actual_value} (افتراضي)")
                else:
                    print(f"  ⚠️ {key}: {actual_value} (متوقع {expected_value})")
        else:
            print("❌ إعادة تعيين الإعدادات: فشل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_scheduler():
    """اختبار جدولة النسخ الاحتياطي"""
    
    print("\nاختبار جدولة النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        # اختبار بدء الجدولة
        print("--- اختبار بدء الجدولة ---")
        backup_service.start_scheduler()
        print("✅ بدء جدولة النسخ الاحتياطي")
        
        # التحقق من حالة الجدولة
        if backup_service.scheduler_running:
            print("✅ الجدولة تعمل")
        else:
            print("❌ الجدولة لا تعمل")
        
        # انتظار قصير
        time.sleep(2)
        
        # اختبار إيقاف الجدولة
        print("\n--- اختبار إيقاف الجدولة ---")
        backup_service.stop_scheduler()
        print("✅ إيقاف جدولة النسخ الاحتياطي")
        
        # التحقق من حالة الجدولة
        if not backup_service.scheduler_running:
            print("✅ الجدولة متوقفة")
        else:
            print("❌ الجدولة لا تزال تعمل")
        
        # اختبار النسخ الاحتياطي عند بدء التشغيل
        print("\n--- اختبار النسخ الاحتياطي عند بدء التشغيل ---")
        
        # تفعيل النسخ الاحتياطي عند بدء التشغيل مؤقتاً
        backup_service.update_backup_settings({'backup_on_startup': True})
        backup_service.backup_on_startup()
        print("✅ اختبار النسخ الاحتياطي عند بدء التشغيل")
        
        # إعادة تعطيل النسخ الاحتياطي عند بدء التشغيل
        backup_service.update_backup_settings({'backup_on_startup': False})
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار جدولة النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_widget():
    """اختبار واجهة النسخ الاحتياطي"""
    
    print("\nاختبار واجهة النسخ الاحتياطي...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        from ui.backup_widget import BackupWidget, BackupSettingsDialog, RestoreOptionsDialog
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار ويدجت النسخ الاحتياطي الرئيسي
        print("--- اختبار الويدجت الرئيسي ---")
        backup_widget = BackupWidget(conn_manager, auth_service)
        print("✅ إنشاء ويدجت النسخ الاحتياطي: نجح")
        
        # اختبار التبويبات
        tab_count = backup_widget.tabs.count()
        print(f"✅ عدد التبويبات: {tab_count}")
        
        expected_tabs = ["النسخ الاحتياطي", "الاستعادة", "الإعدادات", "الإحصائيات"]
        for i in range(min(tab_count, len(expected_tabs))):
            tab_text = backup_widget.tabs.tabText(i)
            print(f"  - تبويب {i+1}: {tab_text}")
        
        # اختبار نافذة إعدادات النسخ الاحتياطي
        print("\n--- اختبار نافذة الإعدادات ---")
        settings_dialog = BackupSettingsDialog(backup_widget.backup_service)
        print("✅ إنشاء نافذة الإعدادات: نجح")
        
        # اختبار الحصول على الإعدادات من النافذة
        dialog_settings = settings_dialog.get_settings()
        print(f"✅ الحصول على الإعدادات من النافذة: {len(dialog_settings)} إعداد")
        
        # اختبار نافذة خيارات الاستعادة
        print("\n--- اختبار نافذة خيارات الاستعادة ---")
        test_backup_info = {
            'backup_name': 'test_backup',
            'created_at': datetime.now().isoformat(),
            'file_size_mb': 5.2,
            'database_type': 'SQLite'
        }
        
        restore_dialog = RestoreOptionsDialog(test_backup_info)
        print("✅ إنشاء نافذة خيارات الاستعادة: نجح")
        
        # اختبار الحصول على خيارات الاستعادة
        restore_options = restore_dialog.get_restore_options()
        print(f"✅ الحصول على خيارات الاستعادة: {len(restore_options)} خيار")
        
        for option, value in restore_options.items():
            print(f"  - {option}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_performance():
    """اختبار أداء النسخ الاحتياطي"""
    
    print("\nاختبار أداء النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        import time
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        # اختبار أداء إنشاء النسخة الاحتياطية
        print("--- اختبار أداء الإنشاء ---")
        
        performance_tests = []
        
        for i in range(3):
            backup_name = f"performance_test_{i}_{int(time.time())}"
            
            start_time = time.time()
            success, message, backup_path = backup_service.create_backup(backup_name, False)
            end_time = time.time()
            
            if success:
                creation_time = end_time - start_time
                file_size = os.path.getsize(backup_path) if os.path.exists(backup_path) else 0
                
                performance_tests.append({
                    'backup_name': backup_name,
                    'creation_time': creation_time,
                    'file_size': file_size,
                    'backup_path': backup_path
                })
                
                print(f"✅ اختبار {i+1}: {creation_time:.3f} ثانية ({file_size} بايت)")
            else:
                print(f"❌ فشل اختبار {i+1}: {message}")
        
        if performance_tests:
            # حساب الإحصائيات
            avg_time = sum(test['creation_time'] for test in performance_tests) / len(performance_tests)
            avg_size = sum(test['file_size'] for test in performance_tests) / len(performance_tests)
            
            print(f"\n📊 إحصائيات الأداء:")
            print(f"  - متوسط وقت الإنشاء: {avg_time:.3f} ثانية")
            print(f"  - متوسط حجم الملف: {avg_size:.0f} بايت ({avg_size/(1024*1024):.2f} MB)")
            
            # تقييم الأداء
            if avg_time < 1.0:
                print("🚀 أداء ممتاز (< 1.0 ثانية)")
            elif avg_time < 3.0:
                print("✅ أداء جيد (< 3.0 ثانية)")
            elif avg_time < 10.0:
                print("⚠️ أداء مقبول (< 10.0 ثانية)")
            else:
                print("🐌 أداء بطيء (> 10.0 ثانية)")
            
            # تنظيف ملفات الاختبار
            print("\n--- تنظيف ملفات الاختبار ---")
            for test in performance_tests:
                try:
                    success, message = backup_service.delete_backup(test['backup_path'])
                    if success:
                        print(f"✅ حذف: {test['backup_name']}")
                    else:
                        print(f"❌ فشل حذف: {test['backup_name']}")
                except Exception as e:
                    print(f"❌ خطأ في حذف {test['backup_name']}: {str(e)}")
        
        return len(performance_tests) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أداء النسخ الاحتياطي: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    try:
        # تنظيف مجلد النسخ الاحتياطي
        backups_dir = Path("backups")
        if backups_dir.exists():
            test_files = []
            for file_path in backups_dir.iterdir():
                if file_path.name.startswith(('test_', 'stats_test_', 'performance_test_')):
                    test_files.append(file_path)
            
            for file_path in test_files:
                try:
                    if file_path.is_file():
                        file_path.unlink()
                    else:
                        shutil.rmtree(file_path)
                    print(f"🗑️ تم حذف ملف الاختبار: {file_path.name}")
                except Exception as e:
                    print(f"⚠️ فشل في حذف {file_path.name}: {str(e)}")
        
        # تنظيف مجلد temp_backup
        temp_dir = Path("temp_backup")
        if temp_dir.exists():
            try:
                shutil.rmtree(temp_dir)
                print("🗑️ تم حذف مجلد temp_backup")
            except Exception as e:
                print(f"⚠️ فشل في حذف مجلد temp_backup: {str(e)}")
                
    except Exception as e:
        print(f"⚠️ خطأ في تنظيف ملفات الاختبار: {str(e)}")

def run_backup_system_tests():
    """تشغيل جميع اختبارات نظام النسخ الاحتياطي"""
    
    print("=" * 60)
    print("اختبار نظام النسخ الاحتياطي والاستعادة المتقدم")
    print("=" * 60)
    
    tests = [
        ("خدمة النسخ الاحتياطي", test_backup_service),
        ("إحصائيات النسخ الاحتياطي", test_backup_statistics),
        ("إعدادات النسخ الاحتياطي", test_backup_settings),
        ("جدولة النسخ الاحتياطي", test_backup_scheduler),
        ("واجهة النسخ الاحتياطي", test_backup_widget),
        ("أداء النسخ الاحتياطي", test_backup_performance),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج اختبار نظام النسخ الاحتياطي: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات نظام النسخ الاحتياطي نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ نسخ احتياطي تلقائي مع جدولة")
        print("- ✅ استعادة شاملة مع خيارات متقدمة")
        print("- ✅ ضغط وتشفير النسخ الاحتياطية")
        print("- ✅ التحقق من سلامة النسخ الاحتياطية")
        print("- ✅ إحصائيات وتقارير مفصلة")
        print("- ✅ واجهات تفاعلية متطورة")
        print("- ✅ أداء سريع ومحسن")
        print("- ✅ إدارة متقدمة للإعدادات")
        return True
    else:
        print("⚠️ بعض اختبارات نظام النسخ الاحتياطي فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_backup_system_tests()
        
        # تنظيف ملفات الاختبار
        cleanup_test_files()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 نظام النسخ الاحتياطي والاستعادة جاهز للاستخدام!")
            print("\nلتجربة النظام:")
            print("1. شغل التطبيق: python main.py")
            print("2. سجل الدخول: admin / admin")
            print("3. انتقل إلى 'إدارة النسخ الاحتياطي'")
            print("4. استكشف التبويبات المختلفة:")
            print("   - النسخ الاحتياطي: إنشاء نسخ جديدة")
            print("   - الاستعادة: استعادة النسخ السابقة")
            print("   - الإعدادات: تخصيص الجدولة والخيارات")
            print("   - الإحصائيات: مراقبة الأداء والاستخدام")
            print(f"{'=' * 60}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
