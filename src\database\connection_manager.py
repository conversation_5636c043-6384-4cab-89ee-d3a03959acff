# -*- coding: utf-8 -*-
"""
مدير الاتصال بقاعدة البيانات
Database Connection Manager

يدير الاتصال بقواعد البيانات MySQL و SQLite مع التبديل التلقائي
"""

import os
import sqlite3
import logging
from typing import Optional, Union, Any, Dict, List
from contextlib import contextmanager
from pathlib import Path

try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from utils.logger import Logger

class ConnectionManager:
    """مدير الاتصال بقاعدة البيانات"""
    
    def __init__(self, config_manager):
        """
        تهيئة مدير الاتصال
        
        Args:
            config_manager: مدير التكوين
        """
        self.config = config_manager
        self.logger = Logger(__name__)
        self.mysql_connection = None
        self.sqlite_connection = None
        self.current_mode = None  # 'mysql' أو 'sqlite'
        
        # إعداد قاعدة البيانات المحلية
        self._setup_local_database()
        
        # تحديد نمط التشغيل
        self._determine_connection_mode()
    
    def _setup_local_database(self):
        """إعداد قاعدة البيانات المحلية SQLite"""
        try:
            sqlite_path = self.config.sqlite_database
            
            # إنشاء مجلد البيانات إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(sqlite_path), exist_ok=True)
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            if not os.path.exists(sqlite_path):
                self.logger.info("إنشاء قاعدة البيانات المحلية...")
                self._create_sqlite_database()
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد قاعدة البيانات المحلية: {str(e)}")
    
    def _create_sqlite_database(self):
        """إنشاء قاعدة البيانات المحلية من السكريبت"""
        try:
            schema_file = os.path.join("src", "database", "sqlite_schema.sql")

            if os.path.exists(schema_file):
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema_sql = f.read()

                conn = sqlite3.connect(self.config.sqlite_database)
                cursor = conn.cursor()

                # تنفيذ السكريبت بأجزاء منفصلة
                statements = schema_sql.split(';')
                for statement in statements:
                    statement = statement.strip()
                    if statement and not statement.startswith('--'):
                        try:
                            cursor.execute(statement)
                        except Exception as stmt_error:
                            self.logger.warning(f"تحذير في تنفيذ الاستعلام: {stmt_error}")

                conn.commit()
                conn.close()

                self.logger.info("تم إنشاء قاعدة البيانات المحلية بنجاح")
            else:
                self.logger.warning(f"ملف السكريبت غير موجود: {schema_file}")
                # إنشاء قاعدة بيانات بسيطة
                self._create_basic_sqlite_database()

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات المحلية: {str(e)}")
            # محاولة إنشاء قاعدة بيانات بسيطة
            self._create_basic_sqlite_database()

    def _create_basic_sqlite_database(self):
        """إنشاء قاعدة بيانات بسيطة"""
        try:
            conn = sqlite3.connect(self.config.sqlite_database)
            cursor = conn.cursor()

            # إنشاء جدول المستخدمين الأساسي
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT NOT NULL UNIQUE,
                    user_pass TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    permission TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT (datetime('now', 'localtime'))
                )
            """)

            conn.commit()
            conn.close()

            self.logger.info("تم إنشاء قاعدة البيانات الأساسية")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات الأساسية: {str(e)}")
    
    def _determine_connection_mode(self):
        """تحديد نمط الاتصال (MySQL أو SQLite)"""
        if self.test_mysql_connection():
            self.current_mode = 'mysql'
            self.logger.info("تم تحديد نمط MySQL للاتصال")
        else:
            self.current_mode = 'sqlite'
            self.logger.info("تم تحديد نمط SQLite للاتصال (النمط المحلي)")
    
    def test_mysql_connection(self) -> bool:
        """اختبار الاتصال بقاعدة بيانات MySQL"""
        if not MYSQL_AVAILABLE:
            self.logger.warning("مكتبة mysql.connector غير متوفرة")
            return False

        try:
            connection = mysql.connector.connect(
                host=self.config.mysql_host,
                port=self.config.mysql_port,
                database=self.config.mysql_database,
                user=self.config.mysql_user,
                password=self.config.mysql_password,
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci',
                autocommit=True,
                connection_timeout=5
            )

            if connection.is_connected():
                connection.close()
                return True

        except Exception as e:
            self.logger.warning(f"فشل الاتصال بـ MySQL: {str(e)}")

        return False
    
    def test_sqlite_connection(self) -> bool:
        """اختبار الاتصال بقاعدة بيانات SQLite"""
        try:
            if os.path.exists(self.config.sqlite_database):
                conn = sqlite3.connect(self.config.sqlite_database)
                conn.close()
                return True
        except Exception as e:
            self.logger.error(f"فشل الاتصال بـ SQLite: {str(e)}")
            
        return False
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات الحالية"""
        if self.current_mode == 'mysql':
            return self.test_mysql_connection()
        else:
            return self.test_sqlite_connection()
    
    @contextmanager
    def get_connection(self):
        """
        الحصول على اتصال بقاعدة البيانات مع إدارة السياق
        
        Yields:
            اتصال قاعدة البيانات
        """
        connection = None
        try:
            if self.current_mode == 'mysql':
                connection = self._get_mysql_connection()
            else:
                connection = self._get_sqlite_connection()
            
            yield connection
            
        except Exception as e:
            self.logger.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
            raise
        finally:
            if connection:
                try:
                    connection.close()
                except:
                    pass
    
    def _get_mysql_connection(self):
        """الحصول على اتصال MySQL"""
        if not MYSQL_AVAILABLE:
            raise Exception("مكتبة mysql.connector غير متوفرة")

        return mysql.connector.connect(
            host=self.config.mysql_host,
            port=self.config.mysql_port,
            database=self.config.mysql_database,
            user=self.config.mysql_user,
            password=self.config.mysql_password,
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci',
            autocommit=False
        )
    
    def _get_sqlite_connection(self):
        """الحصول على اتصال SQLite"""
        conn = sqlite3.connect(self.config.sqlite_database)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        conn.execute("PRAGMA foreign_keys = ON")  # تفعيل المفاتيح الخارجية
        return conn
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """
        تنفيذ استعلام SELECT وإرجاع النتائج
        
        Args:
            query: الاستعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            قائمة بالنتائج
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if self.current_mode == 'mysql':
                    columns = [desc[0] for desc in cursor.description]
                    results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                else:
                    results = [dict(row) for row in cursor.fetchall()]
                
                return results
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            raise
    
    def execute_non_query(self, query: str, params: tuple = None) -> int:
        """
        تنفيذ استعلام INSERT/UPDATE/DELETE
        
        Args:
            query: الاستعلام SQL
            params: معاملات الاستعلام
            
        Returns:
            عدد الصفوف المتأثرة
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            raise
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        تنفيذ استعلام متعدد
        
        Args:
            query: الاستعلام SQL
            params_list: قائمة بمعاملات الاستعلامات
            
        Returns:
            عدد الصفوف المتأثرة
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلامات المتعددة: {str(e)}")
            raise
    
    def get_last_insert_id(self) -> Optional[int]:
        """الحصول على آخر معرف تم إدراجه"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if self.current_mode == 'mysql':
                    cursor.execute("SELECT LAST_INSERT_ID()")
                else:
                    cursor.execute("SELECT last_insert_rowid()")
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على آخر معرف: {str(e)}")
            return None
    
    def switch_to_mysql(self) -> bool:
        """التبديل إلى نمط MySQL"""
        if self.test_mysql_connection():
            self.current_mode = 'mysql'
            self.logger.info("تم التبديل إلى نمط MySQL")
            return True
        return False
    
    def switch_to_sqlite(self) -> bool:
        """التبديل إلى نمط SQLite"""
        if self.test_sqlite_connection():
            self.current_mode = 'sqlite'
            self.logger.info("تم التبديل إلى نمط SQLite")
            return True
        return False
    
    @property
    def is_online(self) -> bool:
        """هل التطبيق في النمط المتصل؟"""
        return self.current_mode == 'mysql'
    
    @property
    def is_offline(self) -> bool:
        """هل التطبيق في النمط المحلي؟"""
        return self.current_mode == 'sqlite'
