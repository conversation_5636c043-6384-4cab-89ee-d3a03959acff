# -*- coding: utf-8 -*-
"""
ويدجت إدارة الجداول المساعدة
Lookup Tables Management Widget

واجهة إدارة الجداول المساعدة للمدير
"""

from typing import Dict, List, Optional
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QPushButton, QLineEdit,
                            QComboBox, QLabel, QFrame, QMessageBox, QDialog,
                            QFormLayout, QCheckBox, QGroupBox, QTabWidget,
                            QTextEdit, QColorDialog, QSpinBox)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from ui.base_widget import BaseWidget
from services.admin_service import AdminService
from utils.logger import Logger

class LookupItemDialog(QDialog):
    """نافذة نموذج عنصر الجدول المساعد"""
    
    item_saved = pyqtSignal(dict)
    
    def __init__(self, admin_service: AdminService, table_name: str, 
                 item_data: Dict = None, parent=None):
        super().__init__(parent)
        
        self.admin_service = admin_service
        self.table_name = table_name
        self.item_data = item_data
        self.is_edit_mode = item_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_item_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        table_titles = {
            'visa_types': 'أنواع التأشيرات',
            'received_from_sources': 'مصادر الورود',
            'actions_taken': 'الإجراءات المتخذة',
            'request_statuses': 'حالات الطلبات'
        }
        
        table_title = table_titles.get(self.table_name, self.table_name)
        action = "تعديل" if self.is_edit_mode else "إضافة"
        title = f"{action} عنصر - {table_title}"
        
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(450, 400)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(title_label)
        
        # النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # الحقول حسب نوع الجدول
        self.setup_form_fields(form_layout)
        
        layout.addWidget(form_frame)
        
        # الأزرار
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet(self.get_button_style("#27ae60"))
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style("#95a5a6"))
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        layout.addWidget(buttons_frame)
    
    def setup_form_fields(self, form_layout):
        """إعداد حقول النموذج حسب نوع الجدول"""
        if self.table_name == 'visa_types':
            # نوع التأشيرة
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(self.get_input_style())
            self.name_edit.setMaxLength(100)
            form_layout.addRow("نوع التأشيرة:", self.name_edit)
            
            # الوصف
            self.description_edit = QTextEdit()
            self.description_edit.setStyleSheet(self.get_input_style())
            self.description_edit.setMaximumHeight(80)
            form_layout.addRow("الوصف:", self.description_edit)
            
        elif self.table_name == 'received_from_sources':
            # مصدر الورود
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(self.get_input_style())
            self.name_edit.setMaxLength(100)
            form_layout.addRow("مصدر الورود:", self.name_edit)
            
            # معلومات الاتصال
            self.contact_edit = QLineEdit()
            self.contact_edit.setStyleSheet(self.get_input_style())
            self.contact_edit.setMaxLength(200)
            form_layout.addRow("معلومات الاتصال:", self.contact_edit)
            
        elif self.table_name == 'actions_taken':
            # الإجراء المتخذ
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(self.get_input_style())
            self.name_edit.setMaxLength(100)
            form_layout.addRow("الإجراء المتخذ:", self.name_edit)
            
            # الوصف
            self.description_edit = QTextEdit()
            self.description_edit.setStyleSheet(self.get_input_style())
            self.description_edit.setMaximumHeight(80)
            form_layout.addRow("الوصف:", self.description_edit)
            
        elif self.table_name == 'request_statuses':
            # حالة الطلب
            self.name_edit = QLineEdit()
            self.name_edit.setStyleSheet(self.get_input_style())
            self.name_edit.setMaxLength(50)
            form_layout.addRow("حالة الطلب:", self.name_edit)

            # لون الحالة
            color_layout = QHBoxLayout()

            self.color_edit = QLineEdit()
            self.color_edit.setStyleSheet(self.get_input_style())
            self.color_edit.setMaxLength(7)
            self.color_edit.setText("#007bff")
            color_layout.addWidget(self.color_edit)

            self.color_button = QPushButton("اختيار اللون")
            self.color_button.setStyleSheet(self.get_button_style("#3498db"))
            self.color_button.clicked.connect(self.choose_color)
            color_layout.addWidget(self.color_button)

            form_layout.addRow("لون الحالة:", color_layout)
        
        # حالة النشاط (لجميع الجداول)
        self.active_checkbox = QCheckBox("العنصر نشط")
        self.active_checkbox.setChecked(True)
        self.active_checkbox.setStyleSheet("font-weight: bold; color: #27ae60;")
        form_layout.addRow("", self.active_checkbox)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_button.clicked.connect(self.save_item)
        self.cancel_button.clicked.connect(self.reject)
    
    def choose_color(self):
        """اختيار اللون"""
        if hasattr(self, 'color_edit'):
            current_color = QColor(self.color_edit.text())
            color = QColorDialog.getColor(current_color, self, "اختيار لون الحالة")
            
            if color.isValid():
                self.color_edit.setText(color.name())
    
    def load_item_data(self):
        """تحميل بيانات العنصر للتعديل"""
        if not self.item_data:
            return
        
        # الحقل الرئيسي
        main_field = self.get_main_field_name()
        if main_field and main_field in self.item_data:
            self.name_edit.setText(str(self.item_data[main_field]))
        
        # الحقول الإضافية
        if hasattr(self, 'description_edit') and 'description' in self.item_data:
            self.description_edit.setPlainText(str(self.item_data.get('description', '')))
        
        if hasattr(self, 'contact_edit') and 'contact_info' in self.item_data:
            self.contact_edit.setText(str(self.item_data.get('contact_info', '')))
        
        if hasattr(self, 'color_edit') and 'status_color' in self.item_data:
            self.color_edit.setText(str(self.item_data.get('status_color', '#007bff')))
        
        # حالة النشاط
        self.active_checkbox.setChecked(bool(self.item_data.get('is_active', 1)))
    
    def get_main_field_name(self) -> str:
        """الحصول على اسم الحقل الرئيسي"""
        field_mapping = {
            'visa_types': 'visa_type',
            'received_from_sources': 'received_from',
            'actions_taken': 'action_taken',
            'request_statuses': 'request_status'
        }
        return field_mapping.get(self.table_name, '')
    
    def save_item(self):
        """حفظ العنصر"""
        try:
            # جمع البيانات
            item_data = {}
            
            # الحقل الرئيسي
            main_field = self.get_main_field_name()
            if main_field:
                name_value = self.name_edit.text().strip()
                if not name_value:
                    self.show_error("الاسم مطلوب")
                    return
                item_data[main_field] = name_value
            
            # الحقول الإضافية
            if hasattr(self, 'description_edit'):
                item_data['description'] = self.description_edit.toPlainText().strip()
            
            if hasattr(self, 'contact_edit'):
                item_data['contact_info'] = self.contact_edit.text().strip()
            
            if hasattr(self, 'color_edit'):
                color_value = self.color_edit.text().strip()
                if not color_value.startswith('#') or len(color_value) != 7:
                    self.show_error("لون غير صحيح. يجب أن يكون بصيغة #RRGGBB")
                    return
                item_data['status_color'] = color_value
            
            # حالة النشاط
            item_data['is_active'] = 1 if self.active_checkbox.isChecked() else 0
            
            # حفظ العنصر
            if self.is_edit_mode:
                success, message = self.admin_service.update_lookup_item(
                    self.table_name, self.item_data['id'], item_data
                )
            else:
                success, message = self.admin_service.add_lookup_item(
                    self.table_name, item_data
                )
            
            if success:
                self.show_success(message)
                self.item_saved.emit(item_data)
                self.accept()
            else:
                self.show_error(message)
                
        except Exception as e:
            self.show_error(f"خطأ في حفظ العنصر: {str(e)}")
    
    def get_input_style(self) -> str:
        """نمط حقول الإدخال"""
        return """
            QLineEdit, QTextEdit, QSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus {
                border-color: #3498db;
            }
        """
    
    def get_button_style(self, color: str) -> str:
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#95a5a6": "#7f8c8d",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)
    
    def show_error(self, message: str):
        """عرض رسالة خطأ"""
        QMessageBox.critical(self, "خطأ", message)
    
    def show_success(self, message: str):
        """عرض رسالة نجاح"""
        QMessageBox.information(self, "نجح", message)

class LookupTableWidget(QWidget):
    """ويدجت جدول مساعد واحد"""
    
    def __init__(self, admin_service: AdminService, table_name: str, table_title: str, parent=None):
        super().__init__(parent)
        
        self.admin_service = admin_service
        self.table_name = table_name
        self.table_title = table_title
        self.current_items = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_items()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # شريط الأدوات
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # العنوان
        title_label = QLabel(self.table_title)
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار الإجراءات
        self.add_button = QPushButton("➕ إضافة")
        self.add_button.setStyleSheet(self.get_button_style("#27ae60"))
        toolbar_layout.addWidget(self.add_button)
        
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet(self.get_button_style("#3498db"))
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addWidget(toolbar_frame)
        
        # الجدول
        self.table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.table)
        
        # شريط المعلومات
        self.info_label = QLabel("عدد العناصر: 0")
        self.info_label.setStyleSheet("color: #6c757d; font-weight: bold; padding: 5px;")
        layout.addWidget(self.info_label)
    
    def setup_table(self):
        """إعداد الجدول"""
        # تحديد الأعمدة حسب نوع الجدول
        if self.table_name == 'visa_types':
            headers = ["المعرف", "نوع التأشيرة", "الوصف", "الحالة", "الإجراءات"]
        elif self.table_name == 'received_from_sources':
            headers = ["المعرف", "مصدر الورود", "معلومات الاتصال", "الحالة", "الإجراءات"]
        elif self.table_name == 'actions_taken':
            headers = ["المعرف", "الإجراء المتخذ", "الوصف", "الحالة", "الإجراءات"]
        elif self.table_name == 'request_statuses':
            headers = ["المعرف", "حالة الطلب", "اللون", "الحالة", "الإجراءات"]
        else:
            headers = ["المعرف", "الاسم", "الحالة", "الإجراءات"]
        
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # إعداد الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.verticalHeader().setVisible(False)
        
        # تعيين عرض الأعمدة
        header = self.table.horizontalHeader()
        for i in range(len(headers) - 1):
            if i == 0:  # المعرف
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
            elif i == len(headers) - 2:  # الحالة
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
        
        # عمود الإجراءات
        header.setSectionResizeMode(len(headers) - 1, QHeaderView.ResizeMode.ResizeToContents)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_button.clicked.connect(self.add_item)
        self.refresh_button.clicked.connect(self.load_items)
    
    def load_items(self):
        """تحميل عناصر الجدول"""
        try:
            items = self.admin_service.get_lookup_table_data(self.table_name)
            self.current_items = items
            self.populate_table()
            self.update_info_label()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def populate_table(self):
        """ملء الجدول"""
        self.table.setRowCount(len(self.current_items))
        
        for row, item in enumerate(self.current_items):
            # المعرف
            self.table.setItem(row, 0, QTableWidgetItem(str(item.get('id', ''))))
            
            # الحقول حسب نوع الجدول
            if self.table_name == 'visa_types':
                self.table.setItem(row, 1, QTableWidgetItem(str(item.get('visa_type', ''))))
                self.table.setItem(row, 2, QTableWidgetItem(str(item.get('description', ''))))
                
            elif self.table_name == 'received_from_sources':
                self.table.setItem(row, 1, QTableWidgetItem(str(item.get('received_from', ''))))
                self.table.setItem(row, 2, QTableWidgetItem(str(item.get('contact_info', ''))))
                
            elif self.table_name == 'actions_taken':
                self.table.setItem(row, 1, QTableWidgetItem(str(item.get('action_taken', ''))))
                self.table.setItem(row, 2, QTableWidgetItem(str(item.get('description', ''))))
                
            elif self.table_name == 'request_statuses':
                self.table.setItem(row, 1, QTableWidgetItem(str(item.get('request_status', ''))))

                # عرض اللون
                color_item = QTableWidgetItem("●")
                color_value = item.get('status_color', '#007bff')
                color_item.setForeground(QColor(color_value))
                color_item.setFont(QFont("Arial", 16))
                self.table.setItem(row, 2, color_item)
            
            # حالة النشاط
            status_col = 3 if self.table_name == 'request_statuses' else 3
            status_item = QTableWidgetItem("نشط" if item.get('is_active') else "غير نشط")
            status_color = "#27ae60" if item.get('is_active') else "#e74c3c"
            status_item.setBackground(QColor(status_color + "30"))
            status_item.setForeground(QColor(status_color))
            status_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.table.setItem(row, status_col, status_item)

            # الإجراءات
            actions_col = 4 if self.table_name == 'request_statuses' else 4
            actions_widget = self.create_actions_widget(item)
            self.table.setCellWidget(row, actions_col, actions_widget)
    
    def create_actions_widget(self, item: Dict) -> QWidget:
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل العنصر")
        edit_btn.setMaximumSize(25, 25)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_item(item))
        layout.addWidget(edit_btn)
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف العنصر")
        delete_btn.setMaximumSize(25, 25)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_item(item))
        layout.addWidget(delete_btn)
        
        return widget
    
    def add_item(self):
        """إضافة عنصر جديد"""
        try:
            dialog = LookupItemDialog(self.admin_service, self.table_name, parent=self)
            dialog.item_saved.connect(self.on_item_saved)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإضافة: {str(e)}")
    
    def edit_item(self, item: Dict):
        """تعديل عنصر"""
        try:
            dialog = LookupItemDialog(self.admin_service, self.table_name, item, parent=self)
            dialog.item_saved.connect(self.on_item_saved)
            dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التعديل: {str(e)}")
    
    def delete_item(self, item: Dict):
        """حذف عنصر"""
        try:
            main_field = self.get_main_field_name()
            item_name = item.get(main_field, f"العنصر {item.get('id')}")
            
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف '{item_name}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                success, message = self.admin_service.delete_lookup_item(
                    self.table_name, item['id']
                )
                
                if success:
                    QMessageBox.information(self, "نجح", message)
                    self.load_items()
                else:
                    QMessageBox.critical(self, "خطأ", message)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف العنصر: {str(e)}")
    
    def on_item_saved(self, item_data: Dict):
        """معالجة حفظ العنصر"""
        self.load_items()
    
    def update_info_label(self):
        """تحديث تسمية المعلومات"""
        total_items = len(self.current_items)
        active_items = len([item for item in self.current_items if item.get('is_active')])
        
        self.info_label.setText(f"عدد العناصر: {total_items} (نشط: {active_items})")
    
    def get_main_field_name(self) -> str:
        """الحصول على اسم الحقل الرئيسي"""
        field_mapping = {
            'visa_types': 'visa_type',
            'received_from_sources': 'received_from',
            'actions_taken': 'action_taken',
            'request_statuses': 'request_status'
        }
        return field_mapping.get(self.table_name, 'name')
    
    def get_button_style(self, color: str) -> str:
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)

class LookupManagementWidget(BaseWidget):
    """ويدجت إدارة الجداول المساعدة"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.admin_service = AdminService(connection_manager)

        super().__init__("إدارة الجداول المساعدة", parent)
    
    def setup_content(self):
        """إعداد المحتوى"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel("🗂️ إدارة الجداول المساعدة")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 10px 15px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إضافة التبويبات
        tables_config = [
            ('visa_types', '🛂 أنواع التأشيرات'),
            ('received_from_sources', '📨 مصادر الورود'),
            ('actions_taken', '⚡ الإجراءات المتخذة'),
            ('request_statuses', '📊 حالات الطلبات')
        ]
        
        for table_name, table_title in tables_config:
            table_widget = LookupTableWidget(
                self.admin_service, table_name, table_title
            )
            self.tabs.addTab(table_widget, table_title)
        
        layout.addWidget(self.tabs)
        
        self.content_layout.addLayout(layout)
    
    def refresh(self):
        """تحديث جميع التبويبات"""
        for i in range(self.tabs.count()):
            widget = self.tabs.widget(i)
            if hasattr(widget, 'load_items'):
                widget.load_items()
