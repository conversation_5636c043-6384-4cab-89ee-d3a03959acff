#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تلقائي: إعداد القاعدة (MySQL إن توفر / SQLite) + تحديث الإعدادات + تشغيل التطبيق
Auto Runner: DB setup (MySQL if available / SQLite) + config update + start app
"""

import os
import sys
import subprocess
from pathlib import Path

# أضف مسار src لو احتجنا وحدات داخلية
sys.path.insert(0, 'src')

MYSQL_AVAILABLE = False
try:
    import mysql.connector  # type: ignore
    MYSQL_AVAILABLE = True
except Exception:
    MYSQL_AVAILABLE = False


def update_config_mysql(host: str, port: int, db: str, user: str, password: str):
    import configparser
    cfg_path = Path('config') / 'configuration.ini'
    cfg = configparser.ConfigParser()
    cfg.read(cfg_path, encoding='utf-8')
    if 'DATABASE' not in cfg:
        cfg['DATABASE'] = {}
    cfg['DATABASE']['mysql_host'] = host
    cfg['DATABASE']['mysql_port'] = str(port)
    cfg['DATABASE']['mysql_database'] = db
    cfg['DATABASE']['mysql_user'] = user
    cfg['DATABASE']['mysql_password'] = password
    # تأكد من إعداد SQLite أيضاً
    if 'sqlite_database' not in cfg['DATABASE']:
        cfg['DATABASE']['sqlite_database'] = 'data/iots_local.db'
    cfg_path.parent.mkdir(exist_ok=True)
    with open(cfg_path, 'w', encoding='utf-8') as f:
        cfg.write(f)


def run(cmd: list[str]) -> int:
    print('> ' + ' '.join(cmd))
    return subprocess.call(cmd, shell=False)


def main():
    print('=' * 60)
    print('تشغيل تلقائي: إعداد القاعدة + تشغيل التطبيق')
    print('=' * 60)

    # 1) إنشاء المجلدات
    for d in ['logs', 'data', 'reports', 'temp', 'config']:
        Path(d).mkdir(exist_ok=True)

    # 2) محاولة استخدام MySQL إذا كان متوفراً
    used_mysql = False
    if MYSQL_AVAILABLE:
        print('اكتشاف MySQL...')
        for host in ['localhost', '127.0.0.1']:
            try:
                conn = mysql.connector.connect(host=host, port=3306, user='root', password='', connection_timeout=1)  # type: ignore
                if conn.is_connected():
                    print(f'✅ تم العثور على MySQL على {host}:3306')
                    conn.close()
                    # حدث الإعداد
                    update_config_mysql(host, 3306, 'iots_system', 'root', '')
                    # شغّل مُهيئ MySQL لدينا
                    code = run([sys.executable, 'setup_mysql.py'])
                    if code == 0:
                        used_mysql = True
                        break
            except Exception:
                pass

    if not used_mysql:
        print('سيتم استخدام SQLite (النمط المحلي)')
        # شغّل مُهيئ SQLite إن لزم (موجود داخل app عبر ConnectionManagers).
        # لضمان وجود قاعدة، شغّل سكريبتنا القديم إن وجد
        if Path('setup_database.py').exists():
            run([sys.executable, 'setup_database.py'])

    # 3) تشغيل التطبيق
    print('\nتشغيل التطبيق...')
    # نُفضّل main.py (واجهة المستخدم القياسية)
    code = run([sys.executable, 'main.py'])
    if code != 0:
        print('⚠️ تعذّر تشغيل main.py — سنحاول النسخة العاملة main_working.py')
        run([sys.executable, 'main_working.py'])


if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print('\nتم الإيقاف من قبل المستخدم')
        sys.exit(1)

