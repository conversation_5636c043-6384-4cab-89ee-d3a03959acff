# -*- coding: utf-8 -*-
"""
نموذج المعاملة
Transaction Model

يمثل بيانات المعاملة ويدير العمليات المتعلقة بها
"""

from typing import Dict, Optional, List
from datetime import datetime, date
from dataclasses import dataclass, field

@dataclass
class Transaction:
    """نموذج المعاملة"""
    
    # المعرف
    id: Optional[int] = None
    
    # البيانات الأساسية
    head_incoming_no: str = ""
    head_incoming_date: str = ""
    subject: str = ""
    researcher_notes: str = ""
    
    # المعرفات الخارجية
    user_id: Optional[int] = None
    researcher_1_id: Optional[int] = None
    researcher_2_id: Optional[int] = None
    visa_type_id: Optional[int] = None
    received_from_id: Optional[int] = None
    action_taken_id: Optional[int] = None
    request_status_id: Optional[int] = None
    
    # معلومات إضافية
    priority: str = "medium"
    due_date: Optional[str] = None
    completion_date: Optional[str] = None
    
    # معلومات التتبع
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_by: Optional[int] = None
    updated_by: Optional[int] = None
    
    # البيانات المرتبطة (للعرض)
    data_entry_user: str = ""
    researcher_1_name: str = ""
    researcher_2_name: str = ""
    created_by_name: str = ""
    updated_by_name: str = ""
    visa_type: str = ""
    received_from: str = ""
    action_taken: str = ""
    request_status: str = ""
    status_color: str = "#000000"
    
    def __post_init__(self):
        """معالجة ما بعد التهيئة"""
        # تعيين التاريخ الحالي إذا لم يكن محدداً
        if not self.head_incoming_date:
            self.head_incoming_date = date.today().strftime('%Y-%m-%d')
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Transaction':
        """
        إنشاء نموذج من قاموس
        
        Args:
            data: قاموس البيانات
            
        Returns:
            نموذج المعاملة
        """
        # تنظيف البيانات وتحويل None إلى قيم افتراضية
        cleaned_data = {}
        for key, value in data.items():
            if value is None:
                if key in ['id', 'user_id', 'researcher_1_id', 'researcher_2_id', 
                          'visa_type_id', 'received_from_id', 'action_taken_id', 
                          'request_status_id', 'created_by', 'updated_by']:
                    cleaned_data[key] = None
                else:
                    cleaned_data[key] = ""
            else:
                cleaned_data[key] = value
        
        return cls(**cleaned_data)
    
    def to_dict(self, include_relations: bool = False) -> Dict:
        """
        تحويل النموذج إلى قاموس
        
        Args:
            include_relations: هل تشمل البيانات المرتبطة
            
        Returns:
            قاموس البيانات
        """
        data = {
            'id': self.id,
            'head_incoming_no': self.head_incoming_no,
            'head_incoming_date': self.head_incoming_date,
            'subject': self.subject,
            'researcher_notes': self.researcher_notes,
            'user_id': self.user_id,
            'researcher_1_id': self.researcher_1_id,
            'researcher_2_id': self.researcher_2_id,
            'visa_type_id': self.visa_type_id,
            'received_from_id': self.received_from_id,
            'action_taken_id': self.action_taken_id,
            'request_status_id': self.request_status_id,
            'priority': self.priority,
            'due_date': self.due_date,
            'completion_date': self.completion_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'created_by': self.created_by,
            'updated_by': self.updated_by
        }
        
        if include_relations:
            data.update({
                'data_entry_user': self.data_entry_user,
                'researcher_1_name': self.researcher_1_name,
                'researcher_2_name': self.researcher_2_name,
                'created_by_name': self.created_by_name,
                'updated_by_name': self.updated_by_name,
                'visa_type': self.visa_type,
                'received_from': self.received_from,
                'action_taken': self.action_taken,
                'request_status': self.request_status,
                'status_color': self.status_color
            })
        
        return data
    
    def validate(self) -> List[str]:
        """
        التحقق من صحة البيانات
        
        Returns:
            قائمة بأخطاء التحقق
        """
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not self.head_incoming_no.strip():
            errors.append("رقم الوارد مطلوب")
        
        if not self.head_incoming_date:
            errors.append("تاريخ الوارد مطلوب")
        
        if not self.subject.strip():
            errors.append("موضوع المعاملة مطلوب")
        
        if not self.user_id:
            errors.append("مدخل البيانات مطلوب")
        
        # التحقق من صحة التاريخ
        if self.head_incoming_date:
            try:
                datetime.strptime(self.head_incoming_date, '%Y-%m-%d')
            except ValueError:
                errors.append("تاريخ الوارد غير صحيح")
        
        # التحقق من تاريخ الاستحقاق
        if self.due_date:
            try:
                due_date_obj = datetime.strptime(self.due_date, '%Y-%m-%d').date()
                incoming_date_obj = datetime.strptime(self.head_incoming_date, '%Y-%m-%d').date()
                
                if due_date_obj < incoming_date_obj:
                    errors.append("تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الوارد")
            except ValueError:
                errors.append("تاريخ الاستحقاق غير صحيح")
        
        # التحقق من الأولوية
        valid_priorities = ['low', 'medium', 'high', 'urgent']
        if self.priority not in valid_priorities:
            errors.append("الأولوية غير صحيحة")
        
        # التحقق من طول النصوص
        if len(self.head_incoming_no) > 50:
            errors.append("رقم الوارد طويل جداً (أقصى 50 حرف)")
        
        if len(self.subject) > 500:
            errors.append("موضوع المعاملة طويل جداً (أقصى 500 حرف)")
        
        return errors
    
    def is_valid(self) -> bool:
        """التحقق من صحة البيانات"""
        return len(self.validate()) == 0
    
    def is_overdue(self) -> bool:
        """التحقق من تأخر المعاملة"""
        if not self.due_date or self.completion_date:
            return False
        
        try:
            due_date_obj = datetime.strptime(self.due_date, '%Y-%m-%d').date()
            return due_date_obj < date.today()
        except ValueError:
            return False
    
    def days_since_received(self) -> int:
        """عدد الأيام منذ الاستلام"""
        try:
            received_date = datetime.strptime(self.head_incoming_date, '%Y-%m-%d').date()
            return (date.today() - received_date).days
        except ValueError:
            return 0
    
    def days_until_due(self) -> Optional[int]:
        """عدد الأيام حتى الاستحقاق"""
        if not self.due_date:
            return None
        
        try:
            due_date_obj = datetime.strptime(self.due_date, '%Y-%m-%d').date()
            return (due_date_obj - date.today()).days
        except ValueError:
            return None
    
    def get_priority_display(self) -> str:
        """الحصول على نص الأولوية للعرض"""
        priority_map = {
            'low': 'منخفض',
            'medium': 'متوسط',
            'high': 'عالي',
            'urgent': 'عاجل'
        }
        return priority_map.get(self.priority, self.priority)
    
    def get_priority_color(self) -> str:
        """الحصول على لون الأولوية"""
        color_map = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }
        return color_map.get(self.priority, '#6c757d')
    
    def get_status_badge_style(self) -> str:
        """الحصول على نمط شارة الحالة"""
        return f"background-color: {self.status_color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;"
    
    def copy(self) -> 'Transaction':
        """إنشاء نسخة من المعاملة"""
        return Transaction.from_dict(self.to_dict(include_relations=True))
    
    def __str__(self) -> str:
        """تمثيل نصي للمعاملة"""
        return f"معاملة {self.head_incoming_no}: {self.subject[:50]}..."
