#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من التطبيق الرئيسي للاختبار
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication, QMessageBox, QDialog
from PyQt6.QtCore import Qt

def test_imports():
    """اختبار الواردات"""
    print("اختبار الواردات...")
    
    try:
        from utils.config_manager import ConfigManager
        from utils.logger import setup_logging
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        from ui.login_window import LoginWindow
        
        print("✅ جميع الواردات نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواردات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_initialization():
    """اختبار التهيئة"""
    print("\nاختبار التهيئة...")
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        print("✅ تم إنشاء تطبيق Qt")
        
        # تحميل التكوين
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print("✅ تم تحميل التكوين")
        
        # إعداد نظام السجلات
        from utils.logger import setup_logging
        setup_logging(config)
        logging.info("تم بدء تشغيل التطبيق")
        print("✅ تم إعداد نظام السجلات")
        
        # تهيئة مدير الاتصال بقاعدة البيانات
        from database.connection_manager import ConnectionManager
        connection_manager = ConnectionManager(config)
        print("✅ تم إنشاء مدير الاتصال")
        
        # تهيئة خدمة المصادقة
        from services.auth_service import AuthService
        auth_service = AuthService(connection_manager, config)
        print("✅ تم إنشاء خدمة المصادقة")
        
        # اختبار الاتصال بقاعدة البيانات
        if connection_manager.test_connection():
            print("✅ اختبار الاتصال بقاعدة البيانات نجح")
        else:
            print("⚠️ فشل الاتصال بقاعدة البيانات الرئيسية، سيتم التشغيل في النمط المحلي")
        
        return app, config, connection_manager, auth_service
        
    except Exception as e:
        print(f"❌ خطأ في التهيئة: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_login_window(app, config, connection_manager):
    """اختبار نافذة تسجيل الدخول"""
    print("\nاختبار نافذة تسجيل الدخول...")
    
    try:
        from ui.login_window import LoginWindow
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow(config, connection_manager)
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # عرض النافذة لثانيتين ثم إغلاقها
        from PyQt6.QtCore import QTimer
        
        def close_login():
            login_window.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(close_login)
        timer.start(3000)  # 3 ثواني
        
        login_window.show()
        print("✅ تم عرض نافذة تسجيل الدخول")
        
        # تشغيل حلقة الأحداث
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نافذة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("اختبار التطبيق الرئيسي المبسط")
    print("=" * 50)
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'data', 'reports', 'temp']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    # اختبار الواردات
    if not test_imports():
        print("❌ فشل في اختبار الواردات")
        return 1
    
    # اختبار التهيئة
    result = test_initialization()
    if not result:
        print("❌ فشل في اختبار التهيئة")
        return 1
    
    app, config, connection_manager, auth_service = result
    
    # اختبار نافذة تسجيل الدخول
    if not test_login_window(app, config, connection_manager):
        print("❌ فشل في اختبار نافذة تسجيل الدخول")
        return 1
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("التطبيق جاهز للتشغيل!")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
