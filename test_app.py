#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق المبسط
"""

import sys
import os

# إضافة مسار src
sys.path.insert(0, 'src')

def test_imports():
    """اختبار الواردات"""
    print("اختبار الواردات...")
    
    try:
        print("1. اختبار PyQt6...")
        from PyQt6.QtWidgets import QApplication, QMessageBox
        print("   ✅ PyQt6 متاح")
        
        print("2. اختبار إدارة التكوين...")
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print("   ✅ إدارة التكوين متاحة")
        
        print("3. اختبار إدارة الاتصال...")
        from database.connection_manager import ConnectionManager
        conn_manager = ConnectionManager(config)
        print("   ✅ إدارة الاتصال متاحة")
        
        print("4. اختبار خدمة المصادقة...")
        from services.auth_service import AuthService
        auth_service = AuthService(conn_manager, config)
        print("   ✅ خدمة المصادقة متاحة")
        
        print("5. اختبار نافذة تسجيل الدخول...")
        from ui.login_window import LoginWindow
        print("   ✅ نافذة تسجيل الدخول متاحة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الواردات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        
        # اختبار الاتصال
        if conn_manager.test_connection():
            print("   ✅ الاتصال بقاعدة البيانات ناجح")
            
            # اختبار استعلام بسيط
            conn = conn_manager.get_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM users")
                count = cursor.fetchone()[0]
                print(f"   ✅ عدد المستخدمين: {count}")
                cursor.close()
                conn.close()
                return True
            else:
                print("   ❌ فشل في الحصول على الاتصال")
                return False
        else:
            print("   ❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_login():
    """اختبار تسجيل الدخول"""
    print("\nاختبار تسجيل الدخول...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار تسجيل الدخول
        user = auth_service.authenticate_user("admin", "admin")
        
        if user:
            print(f"   ✅ تسجيل الدخول ناجح: {user.get('full_name', 'غير محدد')}")
            return True
        else:
            print("   ❌ فشل في تسجيل الدخول")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في تسجيل الدخول: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gui():
    """اختبار الواجهة الرسومية"""
    print("\nاختبار الواجهة الرسومية...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        
        # إنشاء تطبيق Qt
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("   ✅ تم إنشاء تطبيق Qt")
        
        # اختبار نافذة بسيطة
        msg = QMessageBox()
        msg.setWindowTitle("اختبار")
        msg.setText("التطبيق يعمل بنجاح!")
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        
        print("   ✅ تم إنشاء نافذة الاختبار")
        print("   📝 سيتم عرض نافذة الاختبار...")
        
        # عرض النافذة لثانيتين ثم إغلاقها
        from PyQt6.QtCore import QTimer
        
        def close_app():
            msg.close()
            app.quit()
        
        timer = QTimer()
        timer.timeout.connect(close_app)
        timer.start(2000)  # 2 ثانية
        
        msg.show()
        app.exec()
        
        print("   ✅ الواجهة الرسومية تعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الواجهة الرسومية: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("اختبار التطبيق المبسط")
    print("=" * 50)
    
    tests = [
        ("الواردات", test_imports),
        ("قاعدة البيانات", test_database),
        ("تسجيل الدخول", test_login),
        ("الواجهة الرسومية", test_gui),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"اختبار: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 50}")
    print(f"نتائج الاختبار: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\nيمكنك الآن تشغيل التطبيق الكامل:")
        print("python main.py")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
