# -*- coding: utf-8 -*-
"""
مدير التكوين
Configuration Manager

يدير إعدادات التطبيق من ملف configuration.ini
"""

import os
import configparser
import logging
from typing import Any, Optional

class ConfigManager:
    """مدير التكوين للتطبيق"""
    
    def __init__(self, config_file: str = "config/configuration.ini"):
        """
        تهيئة مدير التكوين
        
        Args:
            config_file: مسار ملف التكوين
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """تحميل ملف التكوين"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
                logging.info(f"تم تحميل ملف التكوين: {self.config_file}")
            else:
                logging.warning(f"ملف التكوين غير موجود: {self.config_file}")
                self.create_default_config()
        except Exception as e:
            logging.error(f"خطأ في تحميل ملف التكوين: {str(e)}")
            self.create_default_config()
    
    def create_default_config(self):
        """إنشاء ملف تكوين افتراضي"""
        try:
            # إنشاء مجلد config إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            # إعدادات افتراضية
            default_config = {
                'DATABASE': {
                    'mysql_host': '************',
                    'mysql_port': '3306',
                    'mysql_database': 'iots_system',
                    'mysql_user': 'root',
                    'mysql_password': '',
                    'sqlite_database': 'data/iots_local.db'
                },
                'APPLICATION': {
                    'app_name': 'نظام متابعة المراسلات والمعاملات',
                    'app_version': '1.0.0',
                    'app_language': 'ar',
                    'app_direction': 'rtl',
                    'window_width': '1200',
                    'window_height': '800',
                    'window_resizable': 'true'
                },
                'SECURITY': {
                    'password_hash_rounds': '12',
                    'session_timeout': '3600',
                    'max_login_attempts': '3'
                },
                'SYNC': {
                    'auto_sync_enabled': 'true',
                    'sync_interval': '300',
                    'conflict_resolution': 'latest_wins'
                },
                'NOTIFICATIONS': {
                    'desktop_notifications': 'true',
                    'notification_timeout': '5000',
                    'sound_enabled': 'true'
                },
                'REPORTS': {
                    'default_export_format': 'pdf',
                    'reports_directory': 'reports/',
                    'temp_directory': 'temp/'
                },
                'LOGGING': {
                    'log_level': 'INFO',
                    'log_file': 'logs/application.log',
                    'max_log_size': '10485760',
                    'backup_count': '5'
                }
            }
            
            for section, options in default_config.items():
                self.config.add_section(section)
                for key, value in options.items():
                    self.config.set(section, key, value)
            
            self.save_config()
            logging.info("تم إنشاء ملف التكوين الافتراضي")
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء ملف التكوين الافتراضي: {str(e)}")
    
    def save_config(self):
        """حفظ ملف التكوين"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            logging.info("تم حفظ ملف التكوين")
        except Exception as e:
            logging.error(f"خطأ في حفظ ملف التكوين: {str(e)}")
    
    def get(self, section: str, key: str, fallback: Any = None) -> Any:
        """
        الحصول على قيمة من ملف التكوين
        
        Args:
            section: اسم القسم
            key: اسم المفتاح
            fallback: القيمة الافتراضية
            
        Returns:
            قيمة الإعداد
        """
        try:
            return self.config.get(section, key, fallback=fallback)
        except Exception:
            return fallback
    
    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """الحصول على قيمة رقمية صحيحة"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except Exception:
            return fallback
    
    def getfloat(self, section: str, key: str, fallback: float = 0.0) -> float:
        """الحصول على قيمة رقمية عشرية"""
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except Exception:
            return fallback
    
    def getboolean(self, section: str, key: str, fallback: bool = False) -> bool:
        """الحصول على قيمة منطقية"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except Exception:
            return fallback
    
    def set(self, section: str, key: str, value: Any):
        """
        تعيين قيمة في ملف التكوين
        
        Args:
            section: اسم القسم
            key: اسم المفتاح
            value: القيمة الجديدة
        """
        try:
            if not self.config.has_section(section):
                self.config.add_section(section)
            self.config.set(section, key, str(value))
        except Exception as e:
            logging.error(f"خطأ في تعيين قيمة التكوين: {str(e)}")
    
    # خصائص سريعة للوصول للإعدادات المهمة
    @property
    def mysql_host(self) -> str:
        return self.get('DATABASE', 'mysql_host', '************')
    
    @property
    def mysql_port(self) -> int:
        return self.getint('DATABASE', 'mysql_port', 3306)
    
    @property
    def mysql_database(self) -> str:
        return self.get('DATABASE', 'mysql_database', 'iots_system')
    
    @property
    def mysql_user(self) -> str:
        return self.get('DATABASE', 'mysql_user', 'root')
    
    @property
    def mysql_password(self) -> str:
        return self.get('DATABASE', 'mysql_password', '')
    
    @property
    def sqlite_database(self) -> str:
        return self.get('DATABASE', 'sqlite_database', 'data/iots_local.db')
    
    @property
    def app_name(self) -> str:
        return self.get('APPLICATION', 'app_name', 'نظام متابعة المراسلات والمعاملات')
    
    @property
    def window_width(self) -> int:
        return self.getint('APPLICATION', 'window_width', 1200)
    
    @property
    def window_height(self) -> int:
        return self.getint('APPLICATION', 'window_height', 800)
