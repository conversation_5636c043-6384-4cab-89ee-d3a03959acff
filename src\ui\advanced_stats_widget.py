# -*- coding: utf-8 -*-
"""
ويدجت الإحصائيات المتقدمة
Advanced Statistics Widget

يعرض إحصائيات متقدمة ومؤشرات أداء
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QProgressBar,
                            QScrollArea, QGroupBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QColor, QPainter, QPen, QBrush, QLinearGradient

from ui.base_widget import BaseWidget
from services.statistics_service import StatisticsService
from utils.logger import Logger

class AnimatedProgressBar(QProgressBar):
    """شريط تقدم متحرك"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(True)
        self.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                border-radius: 6px;
            }
        """)
        
        # الرسوم المتحركة
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(1000)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def animate_to_value(self, target_value):
        """تحريك الشريط إلى القيمة المستهدفة"""
        self.animation.setStartValue(self.value())
        self.animation.setEndValue(target_value)
        self.animation.start()

class KPICard(QFrame):
    """بطاقة مؤشر أداء رئيسي"""
    
    def __init__(self, title: str, value: str, target: str = "", 
                 trend: str = "stable", color: str = "#007bff", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.current_value = value
        self.target_value = target
        self.trend = trend  # up, down, stable
        self.color = color
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 {self.color}15);
                border: 1px solid {self.color}40;
                border-radius: 12px;
                border-left: 4px solid {self.color};
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 {self.color}25);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                color: #6c757d;
                font-weight: bold;
            }}
        """)
        layout.addWidget(title_label)
        
        # القيمة الحالية
        self.value_label = QLabel(self.current_value)
        self.value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {self.color};
            }}
        """)
        layout.addWidget(self.value_label)
        
        # الهدف والاتجاه
        if self.target_value:
            target_layout = QHBoxLayout()
            
            target_label = QLabel(f"الهدف: {self.target_value}")
            target_label.setStyleSheet("font-size: 11px; color: #6c757d;")
            target_layout.addWidget(target_label)
            
            target_layout.addStretch()
            
            # أيقونة الاتجاه
            trend_icon = self.get_trend_icon()
            trend_label = QLabel(trend_icon)
            trend_label.setStyleSheet(f"font-size: 16px; color: {self.get_trend_color()};")
            target_layout.addWidget(trend_label)
            
            layout.addLayout(target_layout)
    
    def get_trend_icon(self):
        """الحصول على أيقونة الاتجاه"""
        if self.trend == "up":
            return "📈"
        elif self.trend == "down":
            return "📉"
        else:
            return "➡️"
    
    def get_trend_color(self):
        """الحصول على لون الاتجاه"""
        if self.trend == "up":
            return "#28a745"
        elif self.trend == "down":
            return "#dc3545"
        else:
            return "#6c757d"
    
    def update_value(self, new_value: str, new_trend: str = None):
        """تحديث القيمة"""
        self.current_value = new_value
        self.value_label.setText(new_value)
        
        if new_trend:
            self.trend = new_trend

class PerformanceGauge(QWidget):
    """مقياس الأداء الدائري"""
    
    def __init__(self, title: str, value: float, max_value: float = 100, 
                 color: str = "#007bff", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.value = value
        self.max_value = max_value
        self.color = QColor(color)
        
        self.setMinimumSize(150, 150)
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
    
    def paintEvent(self, event):
        """رسم المقياس"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        rect = self.rect()
        center_x = rect.width() // 2
        center_y = rect.height() // 2
        radius = min(center_x, center_y) - 20
        
        # رسم الدائرة الخلفية
        painter.setPen(QPen(QColor("#e9ecef"), 8))
        painter.drawEllipse(center_x - radius, center_y - radius, 
                          radius * 2, radius * 2)
        
        # رسم القوس المملوء
        if self.max_value > 0:
            angle = int((self.value / self.max_value) * 360 * 16)
            painter.setPen(QPen(self.color, 8))
            painter.drawArc(center_x - radius, center_y - radius,
                          radius * 2, radius * 2, 90 * 16, -angle)
        
        # رسم النص
        painter.setPen(QColor("#2c3e50"))
        
        # القيمة
        painter.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        value_text = f"{self.value:.1f}"
        if self.max_value == 100:
            value_text += "%"
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, value_text)
        
        # العنوان
        painter.setFont(QFont("Arial", 10))
        title_rect = rect.adjusted(0, radius + 10, 0, 0)
        painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, self.title)
    
    def update_value(self, new_value: float):
        """تحديث القيمة"""
        self.value = new_value
        self.update()

class TrendChart(QWidget):
    """رسم بياني للاتجاهات"""
    
    def __init__(self, title: str, data: list, color: str = "#007bff", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.data = data  # قائمة بالقيم
        self.color = QColor(color)
        
        self.setMinimumSize(200, 100)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
    
    def paintEvent(self, event):
        """رسم الاتجاه"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if not self.data or len(self.data) < 2:
            painter.setPen(QColor("#6c757d"))
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "لا توجد بيانات")
            return
        
        rect = self.rect()
        margin = 20
        chart_rect = rect.adjusted(margin, margin, -margin, -margin)
        
        # العثور على أقصى وأدنى قيمة
        max_val = max(self.data)
        min_val = min(self.data)
        
        if max_val == min_val:
            max_val += 1
        
        # حساب النقاط
        points = []
        step_x = chart_rect.width() / (len(self.data) - 1)
        
        for i, value in enumerate(self.data):
            x = chart_rect.x() + i * step_x
            y = chart_rect.bottom() - ((value - min_val) / (max_val - min_val)) * chart_rect.height()
            points.append((x, y))
        
        # رسم المنطقة المملوءة
        if len(points) > 1:
            # إنشاء مسار للمنطقة
            gradient = QLinearGradient(0, chart_rect.top(), 0, chart_rect.bottom())
            gradient.setColorAt(0, QColor(self.color.red(), self.color.green(), self.color.blue(), 100))
            gradient.setColorAt(1, QColor(self.color.red(), self.color.green(), self.color.blue(), 20))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.PenStyle.NoPen)
            
            # رسم المضلع
            from PyQt6.QtGui import QPolygonF
            from PyQt6.QtCore import QPointF
            
            polygon = QPolygonF()
            polygon.append(QPointF(points[0][0], chart_rect.bottom()))
            
            for x, y in points:
                polygon.append(QPointF(x, y))
            
            polygon.append(QPointF(points[-1][0], chart_rect.bottom()))
            painter.drawPolygon(polygon)
        
        # رسم الخط
        painter.setPen(QPen(self.color, 2))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(self.color))
        for x, y in points:
            painter.drawEllipse(x - 3, y - 3, 6, 6)
        
        # رسم العنوان
        painter.setPen(QColor("#2c3e50"))
        painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        painter.drawText(rect.x() + 5, rect.y() + 15, self.title)

class AdvancedStatsWidget(BaseWidget):
    """ويدجت الإحصائيات المتقدمة"""
    
    def __init__(self, connection_manager, parent=None):
        # جهّز الاعتماد قبل بناء BaseWidget
        self.connection_manager = connection_manager
        self.statistics_service = StatisticsService(connection_manager)

        super().__init__("الإحصائيات المتقدمة", parent)

        # مؤقت التحديث
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # كل دقيقة
    
    def setup_content(self):
        """إعداد المحتوى"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # مؤشرات الأداء الرئيسية
        self.setup_kpi_section(scroll_layout)
        
        # مقاييس الأداء
        self.setup_gauges_section(scroll_layout)
        
        # الاتجاهات
        self.setup_trends_section(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        self.content_layout.addWidget(scroll_area)
        
        # تحميل البيانات الأولية
        self.refresh_data()
    
    def setup_kpi_section(self, layout):
        """إعداد قسم مؤشرات الأداء الرئيسية"""
        kpi_group = QGroupBox("مؤشرات الأداء الرئيسية")
        kpi_layout = QGridLayout(kpi_group)
        kpi_layout.setSpacing(15)
        
        # إنشاء البطاقات
        self.kpi_cards = {
            'completion_rate': KPICard("معدل الإنجاز", "0%", "85%", "stable", "#28a745"),
            'avg_processing_time': KPICard("متوسط وقت المعالجة", "0 يوم", "7 أيام", "stable", "#007bff"),
            'overdue_rate': KPICard("معدل التأخير", "0%", "<5%", "stable", "#dc3545"),
            'efficiency': KPICard("الكفاءة", "0%", "90%", "stable", "#6f42c1")
        }
        
        # ترتيب البطاقات
        positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
        for i, (key, card) in enumerate(self.kpi_cards.items()):
            row, col = positions[i]
            kpi_layout.addWidget(card, row, col)
        
        layout.addWidget(kpi_group)
    
    def setup_gauges_section(self, layout):
        """إعداد قسم المقاييس"""
        gauges_group = QGroupBox("مقاييس الأداء")
        gauges_layout = QHBoxLayout(gauges_group)
        gauges_layout.setSpacing(20)
        
        # إنشاء المقاييس
        self.gauges = {
            'completion': PerformanceGauge("الإنجاز", 0, 100, "#28a745"),
            'quality': PerformanceGauge("الجودة", 0, 100, "#007bff"),
            'speed': PerformanceGauge("السرعة", 0, 100, "#ffc107")
        }
        
        for gauge in self.gauges.values():
            gauges_layout.addWidget(gauge)
        
        gauges_layout.addStretch()
        layout.addWidget(gauges_group)
    
    def setup_trends_section(self, layout):
        """إعداد قسم الاتجاهات"""
        trends_group = QGroupBox("الاتجاهات")
        trends_layout = QVBoxLayout(trends_group)
        trends_layout.setSpacing(15)
        
        # رسوم الاتجاهات
        self.trend_charts = {
            'daily': TrendChart("الاتجاه اليومي", [], "#007bff"),
            'weekly': TrendChart("الاتجاه الأسبوعي", [], "#28a745")
        }
        
        for chart in self.trend_charts.values():
            trends_layout.addWidget(chart)
        
        layout.addWidget(trends_group)
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # الحصول على الإحصائيات العامة
            stats = self.statistics_service.get_overview_statistics()
            
            if stats:
                # تحديث مؤشرات الأداء
                completion_rate = stats.get('completion_rate', 0)
                self.kpi_cards['completion_rate'].update_value(f"{completion_rate}%")
                
                avg_days = stats.get('avg_completion_days', 0)
                self.kpi_cards['avg_processing_time'].update_value(f"{avg_days:.1f} يوم")
                
                overdue_rate = stats.get('overdue_rate', 0)
                self.kpi_cards['overdue_rate'].update_value(f"{overdue_rate}%")
                
                # حساب الكفاءة (مثال)
                efficiency = max(0, 100 - overdue_rate)
                self.kpi_cards['efficiency'].update_value(f"{efficiency:.1f}%")
                
                # تحديث المقاييس
                self.gauges['completion'].update_value(completion_rate)
                self.gauges['quality'].update_value(efficiency)
                self.gauges['speed'].update_value(max(0, 100 - avg_days * 10))
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات المتقدمة: {str(e)}")
    
    def refresh(self):
        """تحديث الويدجت"""
        self.refresh_data()
