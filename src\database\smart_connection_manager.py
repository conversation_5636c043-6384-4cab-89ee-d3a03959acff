# -*- coding: utf-8 -*-
"""
مدير الاتصال الذكي
Smart Connection Manager

يحاول الاتصال بـ MySQL أولاً، وإذا فشل يتحول إلى SQLite
"""

import os
import sqlite3
import logging
from typing import Optional, Dict, List, Any
from contextlib import contextmanager

# محاولة استيراد MySQL
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

class SmartConnectionManager:
    """مدير الاتصال الذكي"""
    
    def __init__(self, config_manager):
        """تهيئة مدير الاتصال"""
        self.config = config_manager
        self.current_mode = None
        self.mysql_available = False
        
        # التأكد من وجود قاعدة البيانات المحلية
        self._ensure_sqlite_exists()
        
        # تحديد نمط الاتصال
        self._determine_connection_mode()
    
    def _ensure_sqlite_exists(self):
        """التأكد من وجود قاعدة بيانات SQLite"""
        try:
            sqlite_path = self.config.sqlite_database
            os.makedirs(os.path.dirname(sqlite_path), exist_ok=True)
            
            if not os.path.exists(sqlite_path):
                self._create_sqlite_database()
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء قاعدة بيانات SQLite: {str(e)}")
    
    def _create_sqlite_database(self):
        """إنشاء قاعدة بيانات SQLite"""
        try:
            conn = sqlite3.connect(self.config.sqlite_database)
            cursor = conn.cursor()
            
            # إنشاء جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT NOT NULL UNIQUE,
                    user_pass TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    permission TEXT DEFAULT 'user',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT (datetime('now', 'localtime'))
                )
            """)
            
            conn.commit()
            conn.close()
            logging.info("تم إنشاء قاعدة بيانات SQLite")
            
        except Exception as e:
            logging.error(f"خطأ في إنشاء قاعدة بيانات SQLite: {str(e)}")
    
    def _determine_connection_mode(self):
        """تحديد نمط الاتصال"""
        # محاولة الاتصال بـ MySQL أولاً
        if MYSQL_AVAILABLE and self._test_mysql_quick():
            self.current_mode = 'mysql'
            self.mysql_available = True
            logging.info("تم تحديد نمط MySQL")
        else:
            self.current_mode = 'sqlite'
            self.mysql_available = False
            logging.info("تم تحديد نمط SQLite")
    
    def _test_mysql_quick(self) -> bool:
        """اختبار سريع للاتصال بـ MySQL"""
        try:
            connection = mysql.connector.connect(
                host=self.config.mysql_host,
                port=self.config.mysql_port,
                user=self.config.mysql_user,
                password=self.config.mysql_password,
                connection_timeout=1,  # timeout قصير جداً
                autocommit=True
            )
            
            if connection.is_connected():
                connection.close()
                return True
                
        except:
            pass  # تجاهل جميع الأخطاء
            
        return False
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات الحالية"""
        if self.current_mode == 'mysql':
            return self._test_mysql_quick()
        else:
            return self._test_sqlite()
    
    def _test_sqlite(self) -> bool:
        """اختبار الاتصال بـ SQLite"""
        try:
            conn = sqlite3.connect(self.config.sqlite_database)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            return True
        except:
            return False
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        connection = None
        try:
            if self.current_mode == 'mysql':
                connection = self._get_mysql_connection()
            else:
                connection = self._get_sqlite_connection()
            
            yield connection
            
        except Exception as e:
            logging.error(f"خطأ في الاتصال: {str(e)}")
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
            raise
            
        finally:
            if connection:
                try:
                    connection.close()
                except:
                    pass
    
    def _get_mysql_connection(self):
        """الحصول على اتصال MySQL"""
        return mysql.connector.connect(
            host=self.config.mysql_host,
            port=self.config.mysql_port,
            database=self.config.mysql_database,
            user=self.config.mysql_user,
            password=self.config.mysql_password,
            charset='utf8mb4',
            autocommit=False
        )
    
    def _get_sqlite_connection(self):
        """الحصول على اتصال SQLite"""
        connection = sqlite3.connect(self.config.sqlite_database)
        connection.row_factory = sqlite3.Row
        return connection
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if self.current_mode == 'mysql':
                    columns = [desc[0] for desc in cursor.description]
                    results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                else:
                    results = [dict(row) for row in cursor.fetchall()]
                
                cursor.close()
                return results
                
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return []
    
    def execute_non_query(self, query: str, params: tuple = None) -> bool:
        """تنفيذ استعلام بدون إرجاع نتائج"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                conn.commit()
                cursor.close()
                return True
                
        except Exception as e:
            logging.error(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return False
    
    def get_last_insert_id(self) -> Optional[int]:
        """الحصول على آخر معرف تم إدراجه"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if self.current_mode == 'mysql':
                    cursor.execute("SELECT LAST_INSERT_ID()")
                else:
                    cursor.execute("SELECT last_insert_rowid()")
                
                result = cursor.fetchone()
                cursor.close()
                return result[0] if result else None
                
        except Exception as e:
            logging.error(f"خطأ في الحصول على آخر معرف: {str(e)}")
            return None
    
    @property
    def is_online(self) -> bool:
        """هل التطبيق في النمط المتصل؟"""
        return self.current_mode == 'mysql' and self.mysql_available
    
    @property
    def is_offline(self) -> bool:
        """هل التطبيق في النمط المحلي؟"""
        return self.current_mode == 'sqlite' or not self.mysql_available
