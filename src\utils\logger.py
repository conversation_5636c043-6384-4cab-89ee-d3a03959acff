# -*- coding: utf-8 -*-
"""
نظام السجلات
Logging System

يدير تسجيل الأحداث والأخطاء في التطبيق
"""

import os
import logging
import logging.handlers
from typing import Optional

def setup_logging(config_manager=None):
    """
    إعداد نظام السجلات
    
    Args:
        config_manager: مدير التكوين
    """
    try:
        # الحصول على إعدادات السجلات
        if config_manager:
            log_level = config_manager.get('LOGGING', 'log_level', 'INFO')
            log_file = config_manager.get('LOGGING', 'log_file', 'logs/application.log')
            max_log_size = config_manager.getint('LOGGING', 'max_log_size', 10485760)  # 10MB
            backup_count = config_manager.getint('LOGGING', 'backup_count', 5)
        else:
            log_level = 'INFO'
            log_file = 'logs/application.log'
            max_log_size = 10485760
            backup_count = 5
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # تحويل مستوى السجل إلى رقم
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # إعداد التنسيق
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # إعداد معالج الملف مع التدوير
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_log_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(numeric_level)
        
        # إعداد معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(numeric_level)
        
        # إعداد السجل الجذر
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # إزالة المعالجات الموجودة
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # إضافة المعالجات الجديدة
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        logging.info("تم إعداد نظام السجلات بنجاح")
        
    except Exception as e:
        print(f"خطأ في إعداد نظام السجلات: {str(e)}")

class Logger:
    """فئة مساعدة للسجلات"""
    
    def __init__(self, name: str):
        """
        تهيئة السجل
        
        Args:
            name: اسم السجل
        """
        self.logger = logging.getLogger(name)
    
    def debug(self, message: str):
        """تسجيل رسالة تصحيح"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """تسجيل رسالة معلومات"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """تسجيل رسالة تحذير"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """تسجيل رسالة خطأ"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """تسجيل رسالة خطأ حرج"""
        self.logger.critical(message)
    
    def exception(self, message: str):
        """تسجيل استثناء مع تفاصيل المكدس"""
        self.logger.exception(message)
