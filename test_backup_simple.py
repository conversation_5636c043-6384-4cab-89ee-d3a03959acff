#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام النسخ الاحتياطي
"""

import sys
import os
import time
from pathlib import Path

# إضافة مسار src
sys.path.insert(0, 'src')

def test_backup_basic():
    """اختبار أساسي للنسخ الاحتياطي"""
    
    print("اختبار أساسي لنظام النسخ الاحتياطي...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.backup_service import BackupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        backup_service = BackupService(conn_manager, config)
        
        print("✅ تم إنشاء خدمة النسخ الاحتياطي")
        
        # اختبار الإعدادات
        settings = backup_service.get_backup_settings()
        print(f"✅ تحميل الإعدادات: {len(settings)} إعداد")
        
        # اختبار إنشاء نسخة احتياطية
        print("--- إنشاء نسخة احتياطية ---")
        backup_name = f"simple_test_{int(time.time())}"
        
        success, message, backup_path = backup_service.create_backup(backup_name, False)
        
        if success:
            print(f"✅ إنشاء النسخة الاحتياطية: {os.path.basename(backup_path)}")
            
            # التحقق من وجود الملف
            if os.path.exists(backup_path):
                file_size = os.path.getsize(backup_path)
                print(f"  - حجم الملف: {file_size} بايت")
                
                # اختبار قائمة النسخ الاحتياطية
                backup_list = backup_service.get_backup_list()
                print(f"✅ قائمة النسخ الاحتياطية: {len(backup_list)} نسخة")
                
                # اختبار الإحصائيات
                stats = backup_service.get_backup_statistics()
                print(f"✅ الإحصائيات: {stats.get('total_backups', 0)} نسخة احتياطية")
                
                # اختبار التحقق من السلامة
                is_valid, verify_message, verification_results = backup_service.verify_backup_integrity(backup_path)
                print(f"✅ التحقق من السلامة: {'سليمة' if is_valid else 'تالفة'}")
                
                # اختبار حذف النسخة الاحتياطية
                delete_success, delete_message = backup_service.delete_backup(backup_path)
                if delete_success:
                    print(f"✅ حذف النسخة الاحتياطية: نجح")
                else:
                    print(f"❌ فشل حذف النسخة الاحتياطية: {delete_message}")
                
                return True
            else:
                print(f"❌ الملف غير موجود: {backup_path}")
                return False
        else:
            print(f"❌ فشل إنشاء النسخة الاحتياطية: {message}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_widget_basic():
    """اختبار أساسي لواجهة النسخ الاحتياطي"""
    
    print("\nاختبار أساسي لواجهة النسخ الاحتياطي...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        from ui.backup_widget import BackupWidget
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار ويدجت النسخ الاحتياطي
        backup_widget = BackupWidget(conn_manager, auth_service)
        print("✅ إنشاء ويدجت النسخ الاحتياطي: نجح")
        
        # اختبار التبويبات
        tab_count = backup_widget.tabs.count()
        print(f"✅ عدد التبويبات: {tab_count}")
        
        # اختبار الخدمة
        if backup_widget.backup_service:
            print("✅ خدمة النسخ الاحتياطي متصلة")
        else:
            print("❌ خدمة النسخ الاحتياطي غير متصلة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("اختبار مبسط لنظام النسخ الاحتياطي")
    print("=" * 50)
    
    tests = [
        ("النسخ الاحتياطي الأساسي", test_backup_basic),
        ("واجهة النسخ الاحتياطي", test_backup_widget_basic),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 50}")
    print(f"نتائج الاختبار: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ إنشاء النسخ الاحتياطية")
        print("- ✅ التحقق من سلامة النسخ")
        print("- ✅ إدارة قائمة النسخ الاحتياطية")
        print("- ✅ إحصائيات النسخ الاحتياطي")
        print("- ✅ واجهة تفاعلية متطورة")
        print("- ✅ حذف وإدارة النسخ")
        
        print(f"\n{'=' * 50}")
        print("🚀 نظام النسخ الاحتياطي جاهز للاستخدام!")
        print("\nلتجربة النظام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول: admin / admin")
        print("3. انتقل إلى 'إدارة النسخ الاحتياطي'")
        print("4. استكشف التبويبات المختلفة")
        print(f"{'=' * 50}")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        sys.exit(1)
