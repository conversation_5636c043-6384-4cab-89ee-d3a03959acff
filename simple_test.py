#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط جداً
"""

import sys
import os

# إضافة مسار src
sys.path.insert(0, 'src')

def test_step_by_step():
    """اختبار خطوة بخطوة"""
    
    print("1. اختبار PyQt6...")
    try:
        from PyQt6.QtWidgets import QApplication
        print("   ✅ PyQt6 متاح")
    except Exception as e:
        print(f"   ❌ خطأ في PyQt6: {e}")
        return False
    
    print("2. اختبار config_manager...")
    try:
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print("   ✅ config_manager متاح")
        print(f"   📝 mysql_host: {config.mysql_host}")
    except Exception as e:
        print(f"   ❌ خطأ في config_manager: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("3. اختبار logger...")
    try:
        from utils.logger import Logger
        logger = Logger("test")
        print("   ✅ logger متاح")
    except Exception as e:
        print(f"   ❌ خطأ في logger: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("4. اختبار connection_manager...")
    try:
        from database.connection_manager import ConnectionManager
        print("   ✅ connection_manager متاح")
        
        conn_manager = ConnectionManager(config)
        print("   ✅ تم إنشاء connection_manager")
        
    except Exception as e:
        print(f"   ❌ خطأ في connection_manager: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("5. اختبار قاعدة البيانات...")
    try:
        if conn_manager.test_connection():
            print("   ✅ الاتصال بقاعدة البيانات ناجح")
        else:
            print("   ⚠️ فشل الاتصال بقاعدة البيانات")
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("6. اختبار auth_service...")
    try:
        from services.auth_service import AuthService
        auth_service = AuthService(conn_manager, config)
        print("   ✅ auth_service متاح")
    except Exception as e:
        print(f"   ❌ خطأ في auth_service: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("7. اختبار login_window...")
    try:
        from ui.login_window import LoginWindow
        print("   ✅ login_window متاح")
    except Exception as e:
        print(f"   ❌ خطأ في login_window: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n🎉 جميع الاختبارات نجحت!")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار بسيط خطوة بخطوة")
    print("=" * 50)
    
    try:
        if test_step_by_step():
            print("\n✅ التطبيق جاهز للتشغيل!")
            sys.exit(0)
        else:
            print("\n❌ هناك مشاكل تحتاج إلى إصلاح")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
