#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مدير الاتصال المبسط خطوة بخطوة
"""

import sys
import os

# إضافة مسار src
sys.path.insert(0, 'src')

def test_step_by_step():
    """اختبار خطوة بخطوة"""
    
    try:
        print("1. تحميل التكوين...")
        from utils.config_manager import ConfigManager
        config = ConfigManager()
        print(f"   ✅ تم تحميل التكوين")
        print(f"   📝 SQLite: {config.sqlite_database}")
        
        print("2. فحص قاعدة البيانات...")
        import sqlite3
        if os.path.exists(config.sqlite_database):
            print(f"   ✅ قاعدة البيانات موجودة")
        else:
            print(f"   ❌ قاعدة البيانات مفقودة")
            return False
        
        print("3. اختبار الاتصال المباشر...")
        conn = sqlite3.connect(config.sqlite_database)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        print(f"   ✅ عدد المستخدمين: {count}")
        cursor.close()
        conn.close()
        
        print("4. إنشاء مدير الاتصال...")
        from database.simple_connection_manager import SimpleConnectionManager
        
        # إنشاء مدير الاتصال خطوة بخطوة
        print("   4.1 إنشاء الكائن...")
        try:
            conn_manager = SimpleConnectionManager.__new__(SimpleConnectionManager)
            print("   ✅ تم إنشاء الكائن")
        except Exception as e:
            print(f"   ❌ فشل إنشاء الكائن: {e}")
            return False
        
        print("   4.2 تهيئة المتغيرات...")
        try:
            conn_manager.config = config
            conn_manager.current_mode = None
            print("   ✅ تم تهيئة المتغيرات")
        except Exception as e:
            print(f"   ❌ فشل تهيئة المتغيرات: {e}")
            return False
        
        print("   4.3 التأكد من قاعدة البيانات...")
        try:
            conn_manager._ensure_database_exists()
            print("   ✅ تم التأكد من قاعدة البيانات")
        except Exception as e:
            print(f"   ❌ فشل التأكد من قاعدة البيانات: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("   4.4 تحديد نمط الاتصال...")
        try:
            conn_manager._determine_connection_mode()
            print(f"   ✅ النمط المحدد: {conn_manager.current_mode}")
        except Exception as e:
            print(f"   ❌ فشل تحديد نمط الاتصال: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("5. اختبار الاتصال...")
        try:
            if conn_manager.test_connection():
                print("   ✅ اختبار الاتصال نجح")
            else:
                print("   ❌ اختبار الاتصال فشل")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الاتصال: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("6. اختبار استعلام...")
        try:
            users = conn_manager.execute_query("SELECT COUNT(*) as count FROM users")
            if users:
                print(f"   ✅ عدد المستخدمين: {users[0]['count']}")
            else:
                print("   ❌ لا توجد نتائج")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في الاستعلام: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        print("✅ جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار مدير الاتصال المبسط خطوة بخطوة")
    print("=" * 50)
    
    if test_step_by_step():
        print("\n🎉 مدير الاتصال يعمل بنجاح!")
    else:
        print("\n❌ هناك مشكلة في مدير الاتصال")
