# -*- coding: utf-8 -*-
"""
خدمة المصادقة والأمان
Authentication and Security Service

تدير عمليات تسجيل الدخول والخروج والصلاحيات
"""

from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import hashlib
import secrets

from database.user_repository import UserRepository
from utils.logger import Logger

class AuthService:
    """خدمة المصادقة والأمان"""
    
    def __init__(self, connection_manager, config_manager):
        """
        تهيئة خدمة المصادقة
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
            config_manager: مدير التكوين
        """
        self.connection_manager = connection_manager
        self.config = config_manager
        self.user_repository = UserRepository(connection_manager)
        self.logger = Logger(__name__)
        
        # الجلسة الحالية
        self.current_user = None
        self.session_token = None
        self.session_start_time = None
        self.session_timeout = self.config.getint('SECURITY', 'session_timeout', 3600)  # ساعة واحدة
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """
        تسجيل الدخول
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            نتيجة تسجيل الدخول
        """
        try:
            self.logger.info(f"محاولة تسجيل دخول للمستخدم: {username}")
            
            # التحقق من صحة البيانات
            if not username or not password:
                return {
                    'success': False,
                    'message': 'يرجى إدخال اسم المستخدم وكلمة المرور',
                    'error_code': 'MISSING_CREDENTIALS'
                }
            
            # البحث عن المستخدم
            user = self.user_repository.find_by_username(username)
            if not user:
                self.logger.warning(f"محاولة تسجيل دخول بمستخدم غير موجود: {username}")
                return {
                    'success': False,
                    'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
                    'error_code': 'INVALID_CREDENTIALS'
                }
            
            # التحقق من حالة المستخدم
            if not user.get('is_active', 0):
                self.logger.warning(f"محاولة تسجيل دخول بمستخدم غير نشط: {username}")
                return {
                    'success': False,
                    'message': 'حساب المستخدم غير نشط',
                    'error_code': 'INACTIVE_USER'
                }
            
            # التحقق من القفل
            if self.user_repository.is_user_locked(user):
                self.logger.warning(f"محاولة تسجيل دخول بمستخدم مقفل: {username}")
                return {
                    'success': False,
                    'message': 'تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة',
                    'error_code': 'ACCOUNT_LOCKED'
                }
            
            # التحقق من كلمة المرور
            if not self.user_repository.verify_password(username, password):
                self.logger.warning(f"كلمة مرور خاطئة للمستخدم: {username}")
                return {
                    'success': False,
                    'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
                    'error_code': 'INVALID_CREDENTIALS'
                }
            
            # إنشاء الجلسة
            self.current_user = user
            self.session_token = self._generate_session_token()
            self.session_start_time = datetime.now()
            
            self.logger.info(f"تم تسجيل الدخول بنجاح للمستخدم: {username}")
            
            return {
                'success': True,
                'message': 'تم تسجيل الدخول بنجاح',
                'user': {
                    'user_id': user['user_id'],
                    'username': user['user_name'],
                    'full_name': user['full_name'],
                    'email': user.get('email'),
                    'permission': user['permission']
                },
                'session_token': self.session_token
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الدخول: {str(e)}")
            return {
                'success': False,
                'message': 'حدث خطأ أثناء تسجيل الدخول',
                'error_code': 'LOGIN_ERROR'
            }
    
    def logout(self) -> Dict[str, Any]:
        """
        تسجيل الخروج
        
        Returns:
            نتيجة تسجيل الخروج
        """
        try:
            if self.current_user:
                username = self.current_user['user_name']
                self.logger.info(f"تسجيل خروج للمستخدم: {username}")
            
            # إنهاء الجلسة
            self.current_user = None
            self.session_token = None
            self.session_start_time = None
            
            return {
                'success': True,
                'message': 'تم تسجيل الخروج بنجاح'
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج: {str(e)}")
            return {
                'success': False,
                'message': 'حدث خطأ أثناء تسجيل الخروج'
            }
    
    def is_authenticated(self) -> bool:
        """التحقق من صحة المصادقة"""
        if not self.current_user or not self.session_token or not self.session_start_time:
            return False
        
        # التحقق من انتهاء الجلسة
        if self.is_session_expired():
            self.logout()
            return False
        
        return True
    
    def is_session_expired(self) -> bool:
        """التحقق من انتهاء الجلسة"""
        if not self.session_start_time:
            return True
        
        elapsed_time = datetime.now() - self.session_start_time
        return elapsed_time.total_seconds() > self.session_timeout
    
    def extend_session(self):
        """تمديد الجلسة"""
        if self.is_authenticated():
            self.session_start_time = datetime.now()
    
    def get_current_user(self) -> Optional[Dict]:
        """الحصول على المستخدم الحالي"""
        if self.is_authenticated():
            return self.current_user
        return None
    
    def get_current_user_id(self) -> Optional[int]:
        """الحصول على معرف المستخدم الحالي"""
        user = self.get_current_user()
        return user['user_id'] if user else None
    
    def has_permission(self, required_permission: str) -> bool:
        """
        التحقق من الصلاحية
        
        Args:
            required_permission: الصلاحية المطلوبة ('admin' أو 'user')
            
        Returns:
            True إذا كان المستخدم يملك الصلاحية
        """
        if not self.is_authenticated():
            return False
        
        user_permission = self.current_user.get('permission', 'user')
        
        # المدير له جميع الصلاحيات
        if user_permission == 'admin':
            return True
        
        # المستخدم العادي له صلاحية 'user' فقط
        return required_permission == 'user'
    
    def is_admin(self) -> bool:
        """التحقق من كون المستخدم مديراً"""
        return self.has_permission('admin')
    
    def is_user(self) -> bool:
        """التحقق من كون المستخدم عادياً"""
        return self.has_permission('user')
    
    def change_password(self, old_password: str, new_password: str) -> Dict[str, Any]:
        """
        تغيير كلمة المرور
        
        Args:
            old_password: كلمة المرور القديمة
            new_password: كلمة المرور الجديدة
            
        Returns:
            نتيجة تغيير كلمة المرور
        """
        try:
            if not self.is_authenticated():
                return {
                    'success': False,
                    'message': 'يجب تسجيل الدخول أولاً',
                    'error_code': 'NOT_AUTHENTICATED'
                }
            
            # التحقق من صحة البيانات
            if not old_password or not new_password:
                return {
                    'success': False,
                    'message': 'يرجى إدخال كلمة المرور القديمة والجديدة',
                    'error_code': 'MISSING_PASSWORDS'
                }
            
            # التحقق من قوة كلمة المرور الجديدة
            if len(new_password) < 6:
                return {
                    'success': False,
                    'message': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
                    'error_code': 'WEAK_PASSWORD'
                }
            
            # تغيير كلمة المرور
            user_id = self.current_user['user_id']
            success = self.user_repository.change_password(user_id, old_password, new_password)
            
            if success:
                self.logger.info(f"تم تغيير كلمة المرور للمستخدم: {self.current_user['user_name']}")
                return {
                    'success': True,
                    'message': 'تم تغيير كلمة المرور بنجاح'
                }
            else:
                return {
                    'success': False,
                    'message': 'كلمة المرور القديمة غير صحيحة',
                    'error_code': 'INVALID_OLD_PASSWORD'
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في تغيير كلمة المرور: {str(e)}")
            return {
                'success': False,
                'message': 'حدث خطأ أثناء تغيير كلمة المرور',
                'error_code': 'CHANGE_PASSWORD_ERROR'
            }
    
    def _generate_session_token(self) -> str:
        """إنشاء رمز الجلسة"""
        # إنشاء رمز عشوائي آمن
        random_bytes = secrets.token_bytes(32)
        
        # إضافة معلومات إضافية للأمان
        user_info = f"{self.current_user['user_id']}:{self.current_user['user_name']}:{datetime.now().isoformat()}"
        combined = random_bytes + user_info.encode('utf-8')
        
        # إنشاء hash
        return hashlib.sha256(combined).hexdigest()
    
    def get_session_info(self) -> Dict[str, Any]:
        """الحصول على معلومات الجلسة"""
        if not self.is_authenticated():
            return {}
        
        elapsed_time = datetime.now() - self.session_start_time
        remaining_time = self.session_timeout - elapsed_time.total_seconds()
        
        return {
            'user': self.current_user,
            'session_token': self.session_token,
            'session_start': self.session_start_time.isoformat(),
            'elapsed_seconds': int(elapsed_time.total_seconds()),
            'remaining_seconds': max(0, int(remaining_time)),
            'is_expired': self.is_session_expired()
        }
