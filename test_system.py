#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار النظام
System Test Script

يختبر جميع مكونات النظام للتأكد من عملها
"""

import os
import sys
import importlib.util
from pathlib import Path

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    
    print("اختبار المكتبات المطلوبة...")
    
    required_modules = [
        ('PyQt6.QtWidgets', 'PyQt6'),
        ('PyQt6.QtCore', 'PyQt6'),
        ('PyQt6.QtGui', 'PyQt6'),
        ('bcrypt', 'bcrypt'),
        ('sqlite3', 'sqlite3 (مدمج)'),
        ('configparser', 'configparser (مدمج)'),
        ('logging', 'logging (مدمج)'),
        ('pathlib', 'pathlib (مدمج)'),
    ]
    
    failed_imports = []
    
    for module_name, display_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {display_name}")
        except ImportError as e:
            print(f"❌ {display_name}: {str(e)}")
            failed_imports.append(display_name)
    
    if failed_imports:
        print(f"\nالمكتبات المفقودة: {', '.join(failed_imports)}")
        print("يرجى تثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    print("جميع المكتبات متوفرة ✅")
    return True

def test_file_structure():
    """اختبار هيكل الملفات"""
    
    print("\nاختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/configuration.ini",
        "src/__init__.py",
        "src/database/__init__.py",
        "src/database/connection_manager.py",
        "src/database/base_repository.py",
        "src/database/user_repository.py",
        "src/database/mysql_schema.sql",
        "src/database/sqlite_schema.sql",
        "src/ui/__init__.py",
        "src/ui/login_window.py",
        "src/ui/main_window.py",
        "src/ui/dashboard_widget.py",
        "src/ui/base_widget.py",
        "src/services/__init__.py",
        "src/services/auth_service.py",
        "src/services/permission_service.py",
        "src/utils/__init__.py",
        "src/utils/config_manager.py",
        "src/utils/logger.py",
        "src/utils/security.py",
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nالملفات المفقودة: {len(missing_files)}")
        return False
    
    print("جميع الملفات موجودة ✅")
    return True

def test_directories():
    """اختبار المجلدات المطلوبة"""
    
    print("\nاختبار المجلدات...")
    
    required_dirs = [
        "src",
        "src/database",
        "src/ui", 
        "src/services",
        "src/utils",
        "src/models",
        "config",
        "assets",
        "assets/css",
        "assets/js",
        "assets/images",
        "data",
        "logs",
        "reports",
        "temp"
    ]
    
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists() and path.is_dir():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/")
            # إنشاء المجلد إذا لم يكن موجوداً
            path.mkdir(parents=True, exist_ok=True)
            print(f"   تم إنشاء المجلد: {dir_path}/")
    
    print("جميع المجلدات جاهزة ✅")
    return True

def test_configuration():
    """اختبار ملف التكوين"""
    
    print("\nاختبار ملف التكوين...")
    
    try:
        # إضافة مسار src إلى Python path
        sys.path.insert(0, 'src')
        
        from utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        # اختبار الإعدادات الأساسية
        app_name = config.app_name
        print(f"✅ اسم التطبيق: {app_name}")
        
        sqlite_db = config.sqlite_database
        print(f"✅ قاعدة البيانات المحلية: {sqlite_db}")
        
        mysql_host = config.mysql_host
        print(f"✅ خادم MySQL: {mysql_host}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ملف التكوين: {str(e)}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    
    print("\nاختبار الاتصال بقاعدة البيانات...")
    
    try:
        sys.path.insert(0, 'src')
        
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        
        # اختبار الاتصال
        if conn_manager.test_connection():
            mode = "MySQL" if conn_manager.is_online else "SQLite"
            print(f"✅ الاتصال بقاعدة البيانات ({mode})")
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def test_authentication():
    """اختبار نظام المصادقة"""
    
    print("\nاختبار نظام المصادقة...")
    
    try:
        sys.path.insert(0, 'src')
        
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار تسجيل دخول خاطئ
        result = auth_service.login("invalid_user", "invalid_pass")
        if not result['success']:
            print("✅ رفض تسجيل الدخول الخاطئ")
        else:
            print("❌ قبل تسجيل دخول خاطئ")
            return False
        
        # اختبار تسجيل دخول صحيح (إذا كان المستخدم موجود)
        result = auth_service.login("admin", "admin")
        if result['success']:
            print("✅ تسجيل الدخول الصحيح")
            
            # اختبار تسجيل الخروج
            logout_result = auth_service.logout()
            if logout_result['success']:
                print("✅ تسجيل الخروج")
            else:
                print("❌ فشل تسجيل الخروج")
                return False
        else:
            print("⚠️ لم يتم العثور على المستخدم الافتراضي (قم بتشغيل setup_database.py)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المصادقة: {str(e)}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    
    print("=" * 60)
    print("اختبار النظام الشامل")
    print("=" * 60)
    
    tests = [
        ("اختبار المكتبات", test_imports),
        ("اختبار هيكل الملفات", test_file_structure),
        ("اختبار المجلدات", test_directories),
        ("اختبار التكوين", test_configuration),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار المصادقة", test_authentication),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج الاختبار: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("\nلتشغيل التطبيق:")
        print("1. قم بتشغيل: python setup_database.py")
        print("2. ثم قم بتشغيل: python main.py")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل التشغيل")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_all_tests()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
