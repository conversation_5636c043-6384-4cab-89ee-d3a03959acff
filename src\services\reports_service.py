# -*- coding: utf-8 -*-
"""
خدمة التقارير والإحصائيات المتقدمة
Advanced Reports Service

تدير إنشاء التقارير المختلفة والتصدير إلى Excel وPDF
"""

from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, date, timedelta
import os
import json
from pathlib import Path

from database.transaction_repository import TransactionRepository
from database.user_repository import UserRepository
from database.lookup_repositories import LookupService
from services.statistics_service import StatisticsService
from utils.logger import Logger

class ReportsService:
    """خدمة التقارير المتقدمة"""
    
    def __init__(self, connection_manager):
        """
        تهيئة خدمة التقارير
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        self.transaction_repository = TransactionRepository(connection_manager)
        self.user_repository = UserRepository(connection_manager)
        self.lookup_service = LookupService(connection_manager)
        self.statistics_service = StatisticsService(connection_manager)
        self.logger = Logger(__name__)
        
        # إعداد مجلد التقارير
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    # ==================== تقارير المعاملات ====================
    
    def generate_transactions_report(self, filters: Dict = None, 
                                   date_range: Tuple[str, str] = None) -> Dict:
        """
        إنشاء تقرير المعاملات
        
        Args:
            filters: معايير التصفية
            date_range: نطاق التاريخ (من، إلى)
            
        Returns:
            بيانات التقرير
        """
        try:
            # إعداد المعايير
            query_filters = filters or {}
            
            if date_range:
                query_filters['date_from'] = date_range[0]
                query_filters['date_to'] = date_range[1]
            
            # الحصول على المعاملات
            transactions = self.transaction_repository.filter_transactions(query_filters)
            
            # إحصائيات التقرير
            report_stats = self._calculate_transactions_stats(transactions)
            
            # تجميع البيانات حسب المعايير المختلفة
            groupings = self._group_transactions_data(transactions)
            
            report_data = {
                'title': 'تقرير المعاملات',
                'generated_at': datetime.now().isoformat(),
                'filters': query_filters,
                'total_count': len(transactions),
                'statistics': report_stats,
                'groupings': groupings,
                'transactions': transactions
            }
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير المعاملات: {str(e)}")
            raise
    
    def generate_performance_report(self, user_id: int = None, 
                                  period: str = 'month') -> Dict:
        """
        إنشاء تقرير الأداء
        
        Args:
            user_id: معرف المستخدم (اختياري)
            period: الفترة الزمنية (day, week, month, year)
            
        Returns:
            بيانات تقرير الأداء
        """
        try:
            # تحديد نطاق التاريخ حسب الفترة
            date_range = self._get_period_date_range(period)
            
            # الحصول على البيانات
            if user_id:
                # تقرير أداء مستخدم محدد
                user_data = self.user_repository.find_by_id(user_id)
                transactions = self.transaction_repository.get_user_transactions(user_id)
                performance_data = self._calculate_user_performance(user_id, transactions, date_range)
                
                report_data = {
                    'title': f'تقرير أداء المستخدم - {user_data.get("full_name", "غير محدد")}',
                    'user': user_data,
                    'period': period,
                    'date_range': date_range,
                    'performance': performance_data,
                    'transactions': transactions
                }
            else:
                # تقرير أداء عام لجميع المستخدمين
                all_users = self.user_repository.get_all_users()
                users_performance = []
                
                for user in all_users:
                    user_transactions = self.transaction_repository.get_user_transactions(user['user_id'])
                    user_perf = self._calculate_user_performance(user['user_id'], user_transactions, date_range)
                    user_perf['user'] = user
                    users_performance.append(user_perf)
                
                # ترتيب حسب الأداء
                users_performance.sort(key=lambda x: x.get('completion_rate', 0), reverse=True)
                
                report_data = {
                    'title': 'تقرير الأداء العام',
                    'period': period,
                    'date_range': date_range,
                    'users_performance': users_performance,
                    'summary': self._calculate_overall_performance_summary(users_performance)
                }
            
            report_data['generated_at'] = datetime.now().isoformat()
            return report_data
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الأداء: {str(e)}")
            raise
    
    def generate_status_report(self, include_charts: bool = True) -> Dict:
        """
        إنشاء تقرير الحالات
        
        Args:
            include_charts: تضمين بيانات الرسوم البيانية
            
        Returns:
            بيانات تقرير الحالات
        """
        try:
            # الحصول على جميع المعاملات
            all_transactions = self.transaction_repository.get_transactions_with_details()
            
            # تجميع حسب الحالة
            status_groups = {}
            for transaction in all_transactions:
                status = transaction.get('request_status', 'غير محدد')
                if status not in status_groups:
                    status_groups[status] = []
                status_groups[status].append(transaction)
            
            # إحصائيات الحالات
            status_stats = []
            total_count = len(all_transactions)
            
            for status, transactions in status_groups.items():
                count = len(transactions)
                percentage = (count / total_count * 100) if total_count > 0 else 0
                
                # حساب متوسط وقت المعالجة
                avg_processing_time = self._calculate_avg_processing_time(transactions)
                
                status_stats.append({
                    'status': status,
                    'count': count,
                    'percentage': round(percentage, 2),
                    'avg_processing_time': avg_processing_time,
                    'transactions': transactions
                })
            
            # ترتيب حسب العدد
            status_stats.sort(key=lambda x: x['count'], reverse=True)
            
            report_data = {
                'title': 'تقرير الحالات',
                'generated_at': datetime.now().isoformat(),
                'total_transactions': total_count,
                'status_statistics': status_stats,
                'summary': {
                    'most_common_status': status_stats[0]['status'] if status_stats else 'لا توجد بيانات',
                    'total_statuses': len(status_stats),
                    'completion_rate': self._calculate_completion_rate(all_transactions)
                }
            }
            
            if include_charts:
                report_data['chart_data'] = self._prepare_status_chart_data(status_stats)
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الحالات: {str(e)}")
            raise
    
    def generate_visa_types_report(self) -> Dict:
        """
        إنشاء تقرير أنواع التأشيرات
        
        Returns:
            بيانات تقرير أنواع التأشيرات
        """
        try:
            # الحصول على جميع المعاملات
            all_transactions = self.transaction_repository.get_transactions_with_details()
            
            # تجميع حسب نوع التأشيرة
            visa_groups = {}
            for transaction in all_transactions:
                visa_type = transaction.get('visa_type', 'غير محدد')
                if visa_type not in visa_groups:
                    visa_groups[visa_type] = []
                visa_groups[visa_type].append(transaction)
            
            # إحصائيات أنواع التأشيرات
            visa_stats = []
            total_count = len(all_transactions)
            
            for visa_type, transactions in visa_groups.items():
                count = len(transactions)
                percentage = (count / total_count * 100) if total_count > 0 else 0
                
                # حساب معدل الإنجاز لهذا النوع
                completed = len([t for t in transactions if t.get('completion_date')])
                completion_rate = (completed / count * 100) if count > 0 else 0
                
                visa_stats.append({
                    'visa_type': visa_type,
                    'count': count,
                    'percentage': round(percentage, 2),
                    'completed': completed,
                    'completion_rate': round(completion_rate, 2),
                    'transactions': transactions
                })
            
            # ترتيب حسب العدد
            visa_stats.sort(key=lambda x: x['count'], reverse=True)
            
            report_data = {
                'title': 'تقرير أنواع التأشيرات',
                'generated_at': datetime.now().isoformat(),
                'total_transactions': total_count,
                'visa_statistics': visa_stats,
                'summary': {
                    'most_requested_type': visa_stats[0]['visa_type'] if visa_stats else 'لا توجد بيانات',
                    'total_types': len(visa_stats),
                    'overall_completion_rate': self._calculate_completion_rate(all_transactions)
                },
                'chart_data': self._prepare_visa_chart_data(visa_stats)
            }
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير أنواع التأشيرات: {str(e)}")
            raise
    
    def generate_monthly_summary_report(self, year: int = None) -> Dict:
        """
        إنشاء تقرير الملخص الشهري
        
        Args:
            year: السنة (افتراضي: السنة الحالية)
            
        Returns:
            بيانات تقرير الملخص الشهري
        """
        try:
            if not year:
                year = datetime.now().year
            
            # الحصول على معاملات السنة
            date_from = f"{year}-01-01"
            date_to = f"{year}-12-31"
            
            transactions = self.transaction_repository.filter_transactions({
                'date_from': date_from,
                'date_to': date_to
            })
            
            # تجميع حسب الشهر
            monthly_data = {}
            for i in range(1, 13):
                month_key = f"{year}-{i:02d}"
                monthly_data[month_key] = {
                    'month': i,
                    'month_name': self._get_month_name(i),
                    'transactions': [],
                    'total': 0,
                    'completed': 0,
                    'pending': 0,
                    'overdue': 0
                }
            
            # توزيع المعاملات على الأشهر
            for transaction in transactions:
                date_str = transaction.get('head_incoming_date', '')
                if date_str:
                    try:
                        trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                        month_key = f"{trans_date.year}-{trans_date.month:02d}"
                        
                        if month_key in monthly_data:
                            monthly_data[month_key]['transactions'].append(transaction)
                            monthly_data[month_key]['total'] += 1
                            
                            if transaction.get('completion_date'):
                                monthly_data[month_key]['completed'] += 1
                            else:
                                monthly_data[month_key]['pending'] += 1
                                
                                # التحقق من التأخير
                                due_date = transaction.get('due_date')
                                if due_date and due_date < datetime.now().strftime('%Y-%m-%d'):
                                    monthly_data[month_key]['overdue'] += 1
                    except:
                        continue
            
            # حساب معدلات الإنجاز الشهرية
            for month_data in monthly_data.values():
                if month_data['total'] > 0:
                    month_data['completion_rate'] = round(
                        (month_data['completed'] / month_data['total']) * 100, 2
                    )
                else:
                    month_data['completion_rate'] = 0
            
            # إعداد بيانات التقرير
            monthly_list = list(monthly_data.values())
            
            report_data = {
                'title': f'تقرير الملخص الشهري - {year}',
                'generated_at': datetime.now().isoformat(),
                'year': year,
                'monthly_data': monthly_list,
                'yearly_summary': {
                    'total_transactions': sum(m['total'] for m in monthly_list),
                    'total_completed': sum(m['completed'] for m in monthly_list),
                    'total_pending': sum(m['pending'] for m in monthly_list),
                    'total_overdue': sum(m['overdue'] for m in monthly_list),
                    'avg_monthly_transactions': round(
                        sum(m['total'] for m in monthly_list) / 12, 2
                    ),
                    'overall_completion_rate': round(
                        (sum(m['completed'] for m in monthly_list) / 
                         max(sum(m['total'] for m in monthly_list), 1)) * 100, 2
                    )
                },
                'chart_data': self._prepare_monthly_chart_data(monthly_list)
            }
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء تقرير الملخص الشهري: {str(e)}")
            raise
    
    # ==================== وظائف مساعدة ====================
    
    def _calculate_transactions_stats(self, transactions: List[Dict]) -> Dict:
        """حساب إحصائيات المعاملات"""
        if not transactions:
            return {
                'total': 0,
                'completed': 0,
                'pending': 0,
                'overdue': 0,
                'completion_rate': 0,
                'avg_processing_days': 0
            }
        
        total = len(transactions)
        completed = len([t for t in transactions if t.get('completion_date')])
        pending = total - completed
        
        # حساب المتأخرة
        current_date = datetime.now().strftime('%Y-%m-%d')
        overdue = len([
            t for t in transactions 
            if not t.get('completion_date') and 
               t.get('due_date') and t.get('due_date') < current_date
        ])
        
        completion_rate = (completed / total * 100) if total > 0 else 0
        
        # حساب متوسط أيام المعالجة
        processing_days = []
        for transaction in transactions:
            if transaction.get('completion_date') and transaction.get('head_incoming_date'):
                try:
                    start_date = datetime.strptime(transaction['head_incoming_date'], '%Y-%m-%d')
                    end_date = datetime.strptime(transaction['completion_date'], '%Y-%m-%d')
                    days = (end_date - start_date).days
                    if days >= 0:
                        processing_days.append(days)
                except:
                    continue
        
        avg_processing_days = sum(processing_days) / len(processing_days) if processing_days else 0
        
        return {
            'total': total,
            'completed': completed,
            'pending': pending,
            'overdue': overdue,
            'completion_rate': round(completion_rate, 2),
            'avg_processing_days': round(avg_processing_days, 2)
        }
    
    def _group_transactions_data(self, transactions: List[Dict]) -> Dict:
        """تجميع بيانات المعاملات حسب معايير مختلفة"""
        groupings = {
            'by_status': {},
            'by_visa_type': {},
            'by_priority': {},
            'by_researcher': {}
        }
        
        for transaction in transactions:
            # تجميع حسب الحالة
            status = transaction.get('request_status', 'غير محدد')
            if status not in groupings['by_status']:
                groupings['by_status'][status] = 0
            groupings['by_status'][status] += 1
            
            # تجميع حسب نوع التأشيرة
            visa_type = transaction.get('visa_type', 'غير محدد')
            if visa_type not in groupings['by_visa_type']:
                groupings['by_visa_type'][visa_type] = 0
            groupings['by_visa_type'][visa_type] += 1
            
            # تجميع حسب الأولوية
            priority = transaction.get('priority', 'غير محدد')
            if priority not in groupings['by_priority']:
                groupings['by_priority'][priority] = 0
            groupings['by_priority'][priority] += 1
            
            # تجميع حسب الباحث
            researcher = transaction.get('researcher_1_name', 'غير مسند')
            if researcher not in groupings['by_researcher']:
                groupings['by_researcher'][researcher] = 0
            groupings['by_researcher'][researcher] += 1
        
        return groupings
    
    def _get_period_date_range(self, period: str) -> Tuple[str, str]:
        """الحصول على نطاق التاريخ حسب الفترة"""
        today = datetime.now()
        
        if period == 'day':
            start_date = today
            end_date = today
        elif period == 'week':
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
        elif period == 'month':
            start_date = today.replace(day=1)
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
            end_date = today.replace(month=12, day=31)
        else:
            # افتراضي: الشهر الحالي
            start_date = today.replace(day=1)
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        return (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
    
    def _calculate_user_performance(self, user_id: int, transactions: List[Dict], 
                                  date_range: Tuple[str, str]) -> Dict:
        """حساب أداء المستخدم"""
        # تصفية المعاملات حسب نطاق التاريخ
        filtered_transactions = []
        for transaction in transactions:
            trans_date = transaction.get('head_incoming_date', '')
            if trans_date and date_range[0] <= trans_date <= date_range[1]:
                filtered_transactions.append(transaction)
        
        return self._calculate_transactions_stats(filtered_transactions)
    
    def _calculate_overall_performance_summary(self, users_performance: List[Dict]) -> Dict:
        """حساب ملخص الأداء العام"""
        if not users_performance:
            return {}
        
        total_users = len(users_performance)
        total_transactions = sum(up.get('total', 0) for up in users_performance)
        total_completed = sum(up.get('completed', 0) for up in users_performance)
        
        avg_completion_rate = sum(up.get('completion_rate', 0) for up in users_performance) / total_users
        avg_processing_days = sum(up.get('avg_processing_days', 0) for up in users_performance) / total_users
        
        return {
            'total_users': total_users,
            'total_transactions': total_transactions,
            'total_completed': total_completed,
            'avg_completion_rate': round(avg_completion_rate, 2),
            'avg_processing_days': round(avg_processing_days, 2),
            'top_performer': users_performance[0]['user']['full_name'] if users_performance else 'لا يوجد'
        }
    
    def _calculate_avg_processing_time(self, transactions: List[Dict]) -> float:
        """حساب متوسط وقت المعالجة"""
        processing_times = []
        
        for transaction in transactions:
            if transaction.get('completion_date') and transaction.get('head_incoming_date'):
                try:
                    start_date = datetime.strptime(transaction['head_incoming_date'], '%Y-%m-%d')
                    end_date = datetime.strptime(transaction['completion_date'], '%Y-%m-%d')
                    days = (end_date - start_date).days
                    if days >= 0:
                        processing_times.append(days)
                except:
                    continue
        
        return sum(processing_times) / len(processing_times) if processing_times else 0
    
    def _calculate_completion_rate(self, transactions: List[Dict]) -> float:
        """حساب معدل الإنجاز"""
        if not transactions:
            return 0
        
        completed = len([t for t in transactions if t.get('completion_date')])
        return round((completed / len(transactions)) * 100, 2)
    
    def _prepare_status_chart_data(self, status_stats: List[Dict]) -> Dict:
        """إعداد بيانات الرسم البياني للحالات"""
        return {
            'labels': [stat['status'] for stat in status_stats],
            'data': [stat['count'] for stat in status_stats],
            'colors': self._get_status_colors([stat['status'] for stat in status_stats])
        }
    
    def _prepare_visa_chart_data(self, visa_stats: List[Dict]) -> Dict:
        """إعداد بيانات الرسم البياني لأنواع التأشيرات"""
        return {
            'labels': [stat['visa_type'] for stat in visa_stats],
            'data': [stat['count'] for stat in visa_stats],
            'completion_rates': [stat['completion_rate'] for stat in visa_stats]
        }
    
    def _prepare_monthly_chart_data(self, monthly_data: List[Dict]) -> Dict:
        """إعداد بيانات الرسم البياني الشهري"""
        return {
            'months': [data['month_name'] for data in monthly_data],
            'total_transactions': [data['total'] for data in monthly_data],
            'completed_transactions': [data['completed'] for data in monthly_data],
            'completion_rates': [data['completion_rate'] for data in monthly_data]
        }
    
    def _get_status_colors(self, statuses: List[str]) -> List[str]:
        """الحصول على ألوان الحالات"""
        color_map = {
            'جديد': '#3498db',
            'قيد المراجعة': '#f39c12',
            'مكتمل': '#27ae60',
            'مرفوض': '#e74c3c',
            'معلق': '#95a5a6'
        }
        
        return [color_map.get(status, '#34495e') for status in statuses]
    
    def _get_month_name(self, month: int) -> str:
        """الحصول على اسم الشهر"""
        months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        return months[month - 1] if 1 <= month <= 12 else f'الشهر {month}'
    
    def get_available_report_types(self) -> List[Dict]:
        """الحصول على أنواع التقارير المتاحة"""
        return [
            {
                'id': 'transactions',
                'name': 'تقرير المعاملات',
                'description': 'تقرير شامل لجميع المعاملات مع إمكانية التصفية',
                'icon': '📄'
            },
            {
                'id': 'performance',
                'name': 'تقرير الأداء',
                'description': 'تقرير أداء المستخدمين ومعدلات الإنجاز',
                'icon': '📊'
            },
            {
                'id': 'status',
                'name': 'تقرير الحالات',
                'description': 'تقرير توزيع المعاملات حسب الحالة',
                'icon': '📈'
            },
            {
                'id': 'visa_types',
                'name': 'تقرير أنواع التأشيرات',
                'description': 'تقرير توزيع المعاملات حسب نوع التأشيرة',
                'icon': '🛂'
            },
            {
                'id': 'monthly_summary',
                'name': 'الملخص الشهري',
                'description': 'ملخص شهري للمعاملات والإحصائيات',
                'icon': '📅'
            }
        ]
