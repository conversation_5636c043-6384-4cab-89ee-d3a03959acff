# -*- coding: utf-8 -*-
"""
مستودع قاعدة البيانات الأساسي
Base Database Repository

فئة أساسية لجميع مستودعات قاعدة البيانات
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any, Union
import logging

from utils.logger import Logger

class BaseRepository(ABC):
    """فئة أساسية لمستودعات قاعدة البيانات"""
    
    def __init__(self, connection_manager):
        """
        تهيئة المستودع
        
        Args:
            connection_manager: مدير الاتصال بقاعدة البيانات
        """
        self.connection_manager = connection_manager
        self.logger = Logger(self.__class__.__name__)
        self.table_name = self.get_table_name()
    
    @abstractmethod
    def get_table_name(self) -> str:
        """إرجاع اسم الجدول"""
        pass
    
    def find_all(self, where_clause: str = "", params: tuple = None, 
                 order_by: str = "", limit: int = None) -> List[Dict]:
        """
        البحث عن جميع السجلات
        
        Args:
            where_clause: شرط WHERE
            params: معاملات الشرط
            order_by: ترتيب النتائج
            limit: حد النتائج
            
        Returns:
            قائمة بالسجلات
        """
        try:
            query = f"SELECT * FROM {self.table_name}"
            
            if where_clause:
                query += f" WHERE {where_clause}"
            
            if order_by:
                query += f" ORDER BY {order_by}"
            
            if limit:
                query += f" LIMIT {limit}"
            
            return self.connection_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن السجلات: {str(e)}")
            raise
    
    def find_by_id(self, record_id: int) -> Optional[Dict]:
        """
        البحث عن سجل بالمعرف
        
        Args:
            record_id: معرف السجل
            
        Returns:
            السجل أو None
        """
        try:
            primary_key = self.get_primary_key()
            results = self.find_all(f"{primary_key} = ?", (record_id,))
            return results[0] if results else None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن السجل بالمعرف {record_id}: {str(e)}")
            raise
    
    def insert(self, data: Dict) -> Optional[int]:
        """
        إدراج سجل جديد
        
        Args:
            data: بيانات السجل
            
        Returns:
            معرف السجل الجديد
        """
        try:
            # إزالة المفتاح الأساسي إذا كان موجوداً
            data = data.copy()
            primary_key = self.get_primary_key()
            if primary_key in data:
                del data[primary_key]
            
            columns = list(data.keys())
            placeholders = ['?' for _ in columns]
            values = list(data.values())
            
            query = f"""
                INSERT INTO {self.table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
            """
            
            self.connection_manager.execute_non_query(query, tuple(values))
            return self.connection_manager.get_last_insert_id()
            
        except Exception as e:
            self.logger.error(f"خطأ في إدراج السجل: {str(e)}")
            raise
    
    def update(self, record_id: int, data: Dict) -> bool:
        """
        تحديث سجل
        
        Args:
            record_id: معرف السجل
            data: البيانات الجديدة
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            # إزالة المفتاح الأساسي من البيانات
            data = data.copy()
            primary_key = self.get_primary_key()
            if primary_key in data:
                del data[primary_key]
            
            if not data:
                return False
            
            columns = list(data.keys())
            set_clause = ', '.join([f"{col} = ?" for col in columns])
            values = list(data.values())
            values.append(record_id)
            
            query = f"""
                UPDATE {self.table_name}
                SET {set_clause}
                WHERE {primary_key} = ?
            """
            
            rows_affected = self.connection_manager.execute_non_query(query, tuple(values))
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديث السجل {record_id}: {str(e)}")
            raise
    
    def delete(self, record_id: int) -> bool:
        """
        حذف سجل
        
        Args:
            record_id: معرف السجل
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            primary_key = self.get_primary_key()
            query = f"DELETE FROM {self.table_name} WHERE {primary_key} = ?"
            
            rows_affected = self.connection_manager.execute_non_query(query, (record_id,))
            return rows_affected > 0
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف السجل {record_id}: {str(e)}")
            raise
    
    def count(self, where_clause: str = "", params: tuple = None) -> int:
        """
        عد السجلات
        
        Args:
            where_clause: شرط WHERE
            params: معاملات الشرط
            
        Returns:
            عدد السجلات
        """
        try:
            query = f"SELECT COUNT(*) as count FROM {self.table_name}"
            
            if where_clause:
                query += f" WHERE {where_clause}"
            
            result = self.connection_manager.execute_query(query, params)
            return result[0]['count'] if result else 0
            
        except Exception as e:
            self.logger.error(f"خطأ في عد السجلات: {str(e)}")
            raise
    
    def exists(self, record_id: int) -> bool:
        """
        التحقق من وجود سجل
        
        Args:
            record_id: معرف السجل
            
        Returns:
            True إذا كان السجل موجوداً
        """
        try:
            primary_key = self.get_primary_key()
            count = self.count(f"{primary_key} = ?", (record_id,))
            return count > 0
            
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من وجود السجل {record_id}: {str(e)}")
            raise
    
    def get_primary_key(self) -> str:
        """إرجاع اسم المفتاح الأساسي (افتراضياً 'id')"""
        return 'id'
    
    def search(self, search_term: str, search_fields: List[str]) -> List[Dict]:
        """
        البحث في حقول متعددة
        
        Args:
            search_term: مصطلح البحث
            search_fields: الحقول المراد البحث فيها
            
        Returns:
            قائمة بالنتائج
        """
        try:
            if not search_fields:
                return []
            
            # إنشاء شروط البحث
            conditions = []
            params = []
            
            for field in search_fields:
                conditions.append(f"{field} LIKE ?")
                params.append(f"%{search_term}%")
            
            where_clause = " OR ".join(conditions)
            return self.find_all(where_clause, tuple(params))
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث: {str(e)}")
            raise
    
    def get_active_records(self) -> List[Dict]:
        """الحصول على السجلات النشطة فقط"""
        return self.find_all("is_active = ?", (1,))
    
    def activate(self, record_id: int) -> bool:
        """تفعيل سجل"""
        return self.update(record_id, {'is_active': 1})
    
    def deactivate(self, record_id: int) -> bool:
        """إلغاء تفعيل سجل"""
        return self.update(record_id, {'is_active': 0})
    
    def bulk_insert(self, records: List[Dict]) -> int:
        """
        إدراج متعدد
        
        Args:
            records: قائمة بالسجلات
            
        Returns:
            عدد السجلات المدرجة
        """
        try:
            if not records:
                return 0
            
            # استخدام أول سجل لتحديد الأعمدة
            first_record = records[0].copy()
            primary_key = self.get_primary_key()
            if primary_key in first_record:
                del first_record[primary_key]
            
            columns = list(first_record.keys())
            placeholders = ['?' for _ in columns]
            
            query = f"""
                INSERT INTO {self.table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
            """
            
            # تحضير البيانات
            params_list = []
            for record in records:
                record_copy = record.copy()
                if primary_key in record_copy:
                    del record_copy[primary_key]
                
                values = [record_copy.get(col) for col in columns]
                params_list.append(tuple(values))
            
            return self.connection_manager.execute_many(query, params_list)
            
        except Exception as e:
            self.logger.error(f"خطأ في الإدراج المتعدد: {str(e)}")
            raise
