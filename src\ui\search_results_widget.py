# -*- coding: utf-8 -*-
"""
ويدجت نتائج البحث
Search Results Widget

يعرض نتائج البحث المتقدم مع إحصائيات وتحليلات
"""

from typing import Dict, List, Optional
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QLabel, QFrame,
                            QPushButton, QProgressBar, QGroupBox, QTextEdit,
                            QSplitter, QTabWidget, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette

from services.search_service import SearchResult, SearchCriteria
from ui.transactions_list_widget import TransactionTableWidget
from utils.logger import Logger

class SearchStatsWidget(QWidget):
    """ويدجت إحصائيات البحث"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_ui()
        self.reset_stats()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # عنوان
        title_label = QLabel("إحصائيات البحث")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        layout.addWidget(title_label)
        
        # الإحصائيات الأساسية
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Shape.Box)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        stats_layout = QVBoxLayout(stats_frame)
        
        # عدد النتائج
        self.results_count_label = QLabel("عدد النتائج: 0")
        self.results_count_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        stats_layout.addWidget(self.results_count_label)
        
        # وقت البحث
        self.search_time_label = QLabel("وقت البحث: 0.00 ثانية")
        stats_layout.addWidget(self.search_time_label)
        
        # العدد الكلي
        self.total_count_label = QLabel("العدد الكلي: 0")
        stats_layout.addWidget(self.total_count_label)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        stats_layout.addWidget(self.progress_bar)
        
        layout.addWidget(stats_frame)
        
        # إحصائيات تفصيلية
        details_group = QGroupBox("تفاصيل النتائج")
        details_layout = QVBoxLayout(details_group)
        
        # توزيع الحالات
        self.status_stats_label = QLabel("توزيع الحالات:")
        self.status_stats_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        details_layout.addWidget(self.status_stats_label)
        
        self.status_details = QTextEdit()
        self.status_details.setMaximumHeight(80)
        self.status_details.setReadOnly(True)
        self.status_details.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-size: 10px;
            }
        """)
        details_layout.addWidget(self.status_details)
        
        # توزيع الأولوية
        self.priority_stats_label = QLabel("توزيع الأولوية:")
        self.priority_stats_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        details_layout.addWidget(self.priority_stats_label)
        
        self.priority_details = QTextEdit()
        self.priority_details.setMaximumHeight(60)
        self.priority_details.setReadOnly(True)
        self.priority_details.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-size: 10px;
            }
        """)
        details_layout.addWidget(self.priority_details)
        
        layout.addWidget(details_group)
        
        layout.addStretch()
    
    def update_stats(self, search_result: SearchResult):
        """تحديث الإحصائيات"""
        # الإحصائيات الأساسية
        self.results_count_label.setText(f"عدد النتائج: {len(search_result.transactions)}")
        self.search_time_label.setText(f"وقت البحث: {search_result.search_time:.3f} ثانية")
        self.total_count_label.setText(f"العدد الكلي: {search_result.total_count}")
        
        # تحليل النتائج
        self.analyze_results(search_result.transactions)
        
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
    
    def analyze_results(self, transactions: List[Dict]):
        """تحليل النتائج"""
        if not transactions:
            self.status_details.setText("لا توجد نتائج للتحليل")
            self.priority_details.setText("لا توجد نتائج للتحليل")
            return
        
        # تحليل الحالات
        status_counts = {}
        priority_counts = {}
        
        for transaction in transactions:
            # الحالات
            status = transaction.get('request_status', 'غير محدد')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # الأولوية
            priority = transaction.get('priority', 'medium')
            priority_map = {
                'low': 'منخفض',
                'medium': 'متوسط', 
                'high': 'عالي',
                'urgent': 'عاجل'
            }
            priority_text = priority_map.get(priority, priority)
            priority_counts[priority_text] = priority_counts.get(priority_text, 0) + 1
        
        # عرض توزيع الحالات
        status_text = ""
        for status, count in sorted(status_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(transactions)) * 100
            status_text += f"{status}: {count} ({percentage:.1f}%)\n"
        self.status_details.setText(status_text.strip())
        
        # عرض توزيع الأولوية
        priority_text = ""
        for priority, count in sorted(priority_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(transactions)) * 100
            priority_text += f"{priority}: {count} ({percentage:.1f}%)\n"
        self.priority_details.setText(priority_text.strip())
    
    def show_loading(self):
        """عرض حالة التحميل"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        
        self.results_count_label.setText("جاري البحث...")
        self.search_time_label.setText("وقت البحث: --")
        self.total_count_label.setText("العدد الكلي: --")
        
        self.status_details.setText("جاري التحليل...")
        self.priority_details.setText("جاري التحليل...")
    
    def reset_stats(self):
        """إعادة تعيين الإحصائيات"""
        self.results_count_label.setText("عدد النتائج: 0")
        self.search_time_label.setText("وقت البحث: 0.00 ثانية")
        self.total_count_label.setText("العدد الكلي: 0")
        
        self.status_details.setText("لا توجد نتائج")
        self.priority_details.setText("لا توجد نتائج")
        
        self.progress_bar.setVisible(False)

class SearchCriteriaDisplayWidget(QWidget):
    """ويدجت عرض معايير البحث"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان
        title_label = QLabel("معايير البحث المستخدمة")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة عرض المعايير
        self.criteria_display = QTextEdit()
        self.criteria_display.setReadOnly(True)
        self.criteria_display.setMaximumHeight(200)
        self.criteria_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.criteria_display)
    
    def display_criteria(self, criteria: SearchCriteria):
        """عرض معايير البحث"""
        criteria_text = "معايير البحث المطبقة:\n"
        criteria_text += "=" * 30 + "\n\n"
        
        # البحث النصي
        if criteria.text_query:
            criteria_text += f"🔍 نص البحث: '{criteria.text_query}'\n"
            criteria_text += f"   نمط البحث: {self.get_search_mode_text(criteria.search_mode)}\n"
            criteria_text += f"   حساس للأحرف: {'نعم' if criteria.case_sensitive else 'لا'}\n"
            
            if criteria.search_fields:
                criteria_text += f"   الحقول المحددة: {', '.join(criteria.search_fields)}\n"
            else:
                criteria_text += "   الحقول: جميع الحقول\n"
            criteria_text += "\n"
        
        # التصفية
        if criteria.filters:
            criteria_text += "🔧 التصفية:\n"
            for filter_key, filter_value in criteria.filters.items():
                filter_name = self.get_filter_name(filter_key)
                criteria_text += f"   {filter_name}: {filter_value}\n"
            criteria_text += "\n"
        
        # نطاق التاريخ
        if criteria.date_from or criteria.date_to:
            criteria_text += "📅 نطاق التاريخ:\n"
            criteria_text += f"   الحقل: {criteria.date_field}\n"
            if criteria.date_from:
                criteria_text += f"   من: {criteria.date_from}\n"
            if criteria.date_to:
                criteria_text += f"   إلى: {criteria.date_to}\n"
            criteria_text += "\n"
        
        # الترتيب
        criteria_text += "📊 الترتيب:\n"
        criteria_text += f"   حسب: {criteria.sort_field}\n"
        criteria_text += f"   الاتجاه: {'تنازلي' if criteria.sort_direction == 'DESC' else 'تصاعدي'}\n"
        
        # الحد
        if criteria.limit:
            criteria_text += f"   عدد النتائج: {criteria.limit}\n"
        
        self.criteria_display.setText(criteria_text)
    
    def get_search_mode_text(self, mode: str) -> str:
        """الحصول على نص نمط البحث"""
        mode_map = {
            'contains': 'يحتوي على',
            'starts_with': 'يبدأ بـ',
            'ends_with': 'ينتهي بـ',
            'exact': 'مطابق تماماً'
        }
        return mode_map.get(mode, mode)
    
    def get_filter_name(self, filter_key: str) -> str:
        """الحصول على اسم التصفية"""
        filter_names = {
            'request_status_id': 'حالة الطلب',
            'visa_type_id': 'نوع التأشيرة',
            'received_from_id': 'وارد من',
            'action_taken_id': 'الإجراء المتخذ',
            'researcher_1_id': 'الباحث الأول',
            'researcher_2_id': 'الباحث الثاني',
            'user_id': 'مدخل البيانات',
            'priority': 'الأولوية',
            'overdue_only': 'المتأخرة فقط',
            'completed_only': 'المكتملة فقط',
            'new_only': 'الجديدة فقط'
        }
        return filter_names.get(filter_key, filter_key)

class SearchResultsWidget(QWidget):
    """ويدجت نتائج البحث المتقدم"""
    
    # إشارات مخصصة
    transaction_selected = pyqtSignal(dict)
    transaction_double_clicked = pyqtSignal(dict)
    export_requested = pyqtSignal(list)
    
    def __init__(self, connection_manager, parent=None):
        super().__init__(parent)
        
        self.connection_manager = connection_manager
        self.logger = Logger(__name__)
        
        # البيانات الحالية
        self.current_result: Optional[SearchResult] = None
        self.current_criteria: Optional[SearchCriteria] = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # عنوان
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                color: white;
                padding: 10px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("نتائج البحث المتقدم")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # زر التصدير
        self.export_button = QPushButton("📤 تصدير النتائج")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        title_layout.addWidget(self.export_button)
        
        layout.addWidget(title_frame)
        
        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الجانب الأيسر - الإحصائيات والمعايير
        left_widget = QWidget()
        left_widget.setMaximumWidth(300)
        left_layout = QVBoxLayout(left_widget)
        
        # إحصائيات البحث
        self.stats_widget = SearchStatsWidget()
        left_layout.addWidget(self.stats_widget)
        
        # معايير البحث
        self.criteria_widget = SearchCriteriaDisplayWidget()
        left_layout.addWidget(self.criteria_widget)
        
        main_splitter.addWidget(left_widget)
        
        # الجانب الأيمن - النتائج
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 10, 10, 10)
        
        # جدول النتائج
        self.results_table = TransactionTableWidget()
        right_layout.addWidget(self.results_table)
        
        # شريط المعلومات
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Shape.Box)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        
        self.info_label = QLabel("لا توجد نتائج")
        self.info_label.setFont(QFont("Arial", 10))
        info_layout.addWidget(self.info_label)
        
        info_layout.addStretch()
        
        # زر تحميل المزيد
        self.load_more_button = QPushButton("تحميل المزيد")
        self.load_more_button.setVisible(False)
        self.load_more_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        info_layout.addWidget(self.load_more_button)
        
        right_layout.addWidget(info_frame)
        
        main_splitter.addWidget(right_widget)
        
        # تعيين النسب
        main_splitter.setSizes([300, 700])
        
        layout.addWidget(main_splitter)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.results_table.transaction_selected.connect(self.transaction_selected.emit)
        self.results_table.transaction_double_clicked.connect(self.transaction_double_clicked.emit)
        
        self.export_button.clicked.connect(self.export_results)
        self.load_more_button.clicked.connect(self.load_more_results)
    
    def display_results(self, search_result: SearchResult, criteria: SearchCriteria):
        """عرض نتائج البحث"""
        self.current_result = search_result
        self.current_criteria = criteria
        
        # تحديث الإحصائيات
        self.stats_widget.update_stats(search_result)
        
        # عرض معايير البحث
        self.criteria_widget.display_criteria(criteria)
        
        # تحميل النتائج في الجدول
        self.results_table.load_transactions(search_result.transactions)
        
        # تحديث شريط المعلومات
        self.update_info_bar()
        
        # إظهار/إخفاء زر تحميل المزيد
        self.load_more_button.setVisible(search_result.has_more)
    
    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        if not self.current_result:
            self.info_label.setText("لا توجد نتائج")
            return
        
        result_count = len(self.current_result.transactions)
        total_count = self.current_result.total_count
        search_time = self.current_result.search_time
        
        info_text = f"عرض {result_count} من أصل {total_count} نتيجة"
        info_text += f" (وقت البحث: {search_time:.3f} ثانية)"
        
        self.info_label.setText(info_text)
    
    def show_loading(self):
        """عرض حالة التحميل"""
        self.stats_widget.show_loading()
        self.results_table.setRowCount(0)
        self.info_label.setText("جاري البحث...")
        self.load_more_button.setVisible(False)
    
    def clear_results(self):
        """مسح النتائج"""
        self.current_result = None
        self.current_criteria = None
        
        self.stats_widget.reset_stats()
        self.criteria_widget.criteria_display.clear()
        self.results_table.setRowCount(0)
        self.info_label.setText("لا توجد نتائج")
        self.load_more_button.setVisible(False)
    
    def export_results(self):
        """تصدير النتائج"""
        if self.current_result and self.current_result.transactions:
            self.export_requested.emit(self.current_result.transactions)
        else:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "تنبيه", "لا توجد نتائج للتصدير")
    
    def load_more_results(self):
        """تحميل المزيد من النتائج"""
        if not self.current_criteria or not self.current_result:
            return
        
        # تحديث الإزاحة
        current_count = len(self.current_result.transactions)
        self.current_criteria.offset = current_count
        
        # إرسال إشارة لتحميل المزيد
        # سيتم التعامل مع هذا في الويدجت الرئيسي
        pass
