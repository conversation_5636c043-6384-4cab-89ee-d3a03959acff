-- =====================================================
-- نظام متابعة المراسلات والمعاملات (الوارد والصادر)
-- IOTS - Incoming/Outgoing Transactions System
-- 
-- سكريبت إنشاء قاعدة البيانات MySQL
-- MySQL Database Schema Script
-- 
-- المطور: Augment Agent
-- التاريخ: 2025-08-05
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS iots_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE iots_system;

-- =====================================================
-- جدول المستخدمين
-- Users Table
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'المعرف الرقمي للمستخدم',
    user_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'اسم المستخدم',
    user_pass VARCHAR(255) NOT NULL COMMENT 'كلمة المرور (مشفرة)',
    full_name VARCHAR(200) NOT NULL COMMENT 'الاسم الكامل',
    email VARCHAR(150) UNIQUE COMMENT 'البريد الإلكتروني',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    permission ENUM('admin', 'user') NOT NULL DEFAULT 'user' COMMENT 'الصلاحية',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    last_login TIMESTAMP NULL COMMENT 'آخر تسجيل دخول',
    login_attempts INT DEFAULT 0 COMMENT 'عدد محاولات تسجيل الدخول الفاشلة',
    locked_until TIMESTAMP NULL COMMENT 'مقفل حتى'
) ENGINE=InnoDB COMMENT='جدول المستخدمين';

-- =====================================================
-- جدول أنواع التأشيرات
-- Visa Types Table
-- =====================================================
CREATE TABLE IF NOT EXISTS visa_types (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف نوع التأشيرة',
    visa_type VARCHAR(100) NOT NULL UNIQUE COMMENT 'نوع التأشيرة',
    description TEXT COMMENT 'وصف نوع التأشيرة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'
) ENGINE=InnoDB COMMENT='جدول أنواع التأشيرات';

-- =====================================================
-- جدول مصادر الورود
-- Received From Sources Table
-- =====================================================
CREATE TABLE IF NOT EXISTS received_from_sources (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف جهة الورود',
    received_from VARCHAR(200) NOT NULL UNIQUE COMMENT 'وارد من',
    contact_info TEXT COMMENT 'معلومات الاتصال',
    address TEXT COMMENT 'العنوان',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'
) ENGINE=InnoDB COMMENT='جدول مصادر الورود';

-- =====================================================
-- جدول الإجراءات المتخذة
-- Actions Taken Table
-- =====================================================
CREATE TABLE IF NOT EXISTS actions_taken (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الإجراء',
    action_taken VARCHAR(200) NOT NULL UNIQUE COMMENT 'الإجراء المتخذ',
    description TEXT COMMENT 'وصف الإجراء',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'
) ENGINE=InnoDB COMMENT='جدول الإجراءات المتخذة';

-- =====================================================
-- جدول حالات الطلبات
-- Request Statuses Table
-- =====================================================
CREATE TABLE IF NOT EXISTS request_statuses (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الحالة',
    request_status VARCHAR(100) NOT NULL UNIQUE COMMENT 'حالة الطلب',
    status_color VARCHAR(7) DEFAULT '#000000' COMMENT 'لون الحالة (hex)',
    description TEXT COMMENT 'وصف الحالة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث'
) ENGINE=InnoDB COMMENT='جدول حالات الطلبات';

-- =====================================================
-- جدول المعاملات الرئيسي
-- Main Transactions Table
-- =====================================================
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف المعاملة',
    head_incoming_no VARCHAR(50) NOT NULL COMMENT 'رقم وارد رئيس المصلحة',
    head_incoming_date DATE NOT NULL COMMENT 'تاريخ وارد رئيس المصلحة',
    subject TEXT NOT NULL COMMENT 'الموضوع',
    researcher_notes TEXT COMMENT 'ملاحظات الباحث',
    
    -- المفاتيح الخارجية
    user_id INT NOT NULL COMMENT 'مدخل البيانات',
    researcher_1_id INT COMMENT 'الباحث الأول',
    researcher_2_id INT COMMENT 'الباحث الثاني',
    visa_type_id INT COMMENT 'نوع التأشيرة',
    received_from_id INT COMMENT 'وارد من',
    action_taken_id INT COMMENT 'الإجراء المتخذ',
    request_status_id INT COMMENT 'حالة الطلب',
    
    -- معلومات إضافية
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT 'الأولوية',
    due_date DATE COMMENT 'تاريخ الاستحقاق',
    completion_date DATE COMMENT 'تاريخ الإنجاز',
    
    -- معلومات التتبع
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT NOT NULL COMMENT 'منشئ السجل',
    updated_by INT COMMENT 'آخر من حدث السجل',
    
    -- القيود الخارجية
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (researcher_1_id) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (researcher_2_id) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (visa_type_id) REFERENCES visa_types(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (received_from_id) REFERENCES received_from_sources(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (action_taken_id) REFERENCES actions_taken(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (request_status_id) REFERENCES request_statuses(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- فهرس فريد لرقم الوارد
    UNIQUE KEY unique_incoming_no (head_incoming_no)
) ENGINE=InnoDB COMMENT='جدول المعاملات الرئيسي';

-- =====================================================
-- جدول سجل التغييرات
-- Transaction History/Audit Log Table
-- =====================================================
CREATE TABLE IF NOT EXISTS transaction_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف السجل',
    transaction_id INT NOT NULL COMMENT 'معرف المعاملة',
    field_name VARCHAR(50) NOT NULL COMMENT 'اسم الحقل المتغير',
    old_value TEXT COMMENT 'القيمة القديمة',
    new_value TEXT COMMENT 'القيمة الجديدة',
    change_type ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL COMMENT 'نوع التغيير',
    changed_by INT NOT NULL COMMENT 'من قام بالتغيير',
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'وقت التغيير',
    notes TEXT COMMENT 'ملاحظات التغيير',
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    
    INDEX idx_transaction_history_transaction_id (transaction_id),
    INDEX idx_transaction_history_changed_at (changed_at)
) ENGINE=InnoDB COMMENT='جدول سجل التغييرات';

-- =====================================================
-- إنشاء الفهارس لتحسين الأداء
-- Create Indexes for Performance Optimization
-- =====================================================

-- فهارس جدول المستخدمين
CREATE INDEX idx_users_permission ON users(permission);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_last_login ON users(last_login);

-- فهارس جدول المعاملات
CREATE INDEX idx_transactions_head_incoming_date ON transactions(head_incoming_date);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_transactions_researcher_1_id ON transactions(researcher_1_id);
CREATE INDEX idx_transactions_researcher_2_id ON transactions(researcher_2_id);
CREATE INDEX idx_transactions_visa_type_id ON transactions(visa_type_id);
CREATE INDEX idx_transactions_received_from_id ON transactions(received_from_id);
CREATE INDEX idx_transactions_action_taken_id ON transactions(action_taken_id);
CREATE INDEX idx_transactions_request_status_id ON transactions(request_status_id);
CREATE INDEX idx_transactions_priority ON transactions(priority);
CREATE INDEX idx_transactions_due_date ON transactions(due_date);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_updated_at ON transactions(updated_at);

-- فهرس مركب للبحث السريع
CREATE INDEX idx_transactions_search ON transactions(head_incoming_date, request_status_id, researcher_1_id);

-- =====================================================
-- إدراج البيانات الأولية
-- Insert Initial Data
-- =====================================================

-- إدراج المستخدم الافتراضي (المدير)
INSERT INTO users (user_name, user_pass, full_name, email, permission, is_active)
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'مدير النظام', '<EMAIL>', 'admin', TRUE)
ON DUPLICATE KEY UPDATE user_name = user_name;

-- إدراج أنواع التأشيرات الأساسية
INSERT INTO visa_types (visa_type, description) VALUES
('تأشيرة سياحة', 'تأشيرة للسياحة والزيارة'),
('تأشيرة عمل', 'تأشيرة للعمل والإقامة'),
('تأشيرة دراسة', 'تأشيرة للدراسة والتعليم'),
('تأشيرة عبور', 'تأشيرة للعبور والترانزيت'),
('تأشيرة علاج', 'تأشيرة للعلاج الطبي'),
('تأشيرة استثمار', 'تأشيرة للاستثمار والأعمال'),
('تأشيرة لم الشمل', 'تأشيرة لم الشمل العائلي')
ON DUPLICATE KEY UPDATE visa_type = VALUES(visa_type);

-- إدراج مصادر الورود الأساسية
INSERT INTO received_from_sources (received_from, contact_info) VALUES
('وزارة الخارجية', 'هاتف: 123456789'),
('السفارة السعودية', 'هاتف: 987654321'),
('القنصلية العامة', 'هاتف: 456789123'),
('مكتب الهجرة', 'هاتف: 789123456'),
('الجوازات', 'هاتف: 321654987'),
('مكتب التأشيرات', 'هاتف: 654987321')
ON DUPLICATE KEY UPDATE received_from = VALUES(received_from);

-- إدراج الإجراءات المتخذة الأساسية
INSERT INTO actions_taken (action_taken, description) VALUES
('تم الاستلام', 'تم استلام المعاملة'),
('قيد المراجعة', 'المعاملة قيد المراجعة والدراسة'),
('تم التوجيه للباحث', 'تم توجيه المعاملة للباحث المختص'),
('طلب معلومات إضافية', 'تم طلب معلومات أو مستندات إضافية'),
('تم الرد', 'تم الرد على المعاملة'),
('تم الإنجاز', 'تم إنجاز المعاملة بالكامل'),
('تم الأرشفة', 'تم أرشفة المعاملة'),
('تم التحويل', 'تم تحويل المعاملة لجهة أخرى')
ON DUPLICATE KEY UPDATE action_taken = VALUES(action_taken);

-- إدراج حالات الطلبات الأساسية
INSERT INTO request_statuses (request_status, status_color, description) VALUES
('جديد', '#007bff', 'معاملة جديدة لم تتم معالجتها بعد'),
('قيد المعالجة', '#ffc107', 'معاملة قيد المعالجة والدراسة'),
('في انتظار الرد', '#17a2b8', 'في انتظار رد من جهة خارجية'),
('مكتمل', '#28a745', 'تم إنجاز المعاملة بنجاح'),
('مؤجل', '#fd7e14', 'تم تأجيل المعاملة لوقت لاحق'),
('ملغي', '#dc3545', 'تم إلغاء المعاملة'),
('مرفوض', '#6c757d', 'تم رفض المعاملة')
ON DUPLICATE KEY UPDATE request_status = VALUES(request_status);

-- =====================================================
-- إنشاء المشاهدات (Views) للاستعلامات المعقدة
-- Create Views for Complex Queries
-- =====================================================

-- مشاهدة تفاصيل المعاملات الكاملة
CREATE OR REPLACE VIEW v_transactions_details AS
SELECT
    t.id,
    t.head_incoming_no,
    t.head_incoming_date,
    t.subject,
    t.researcher_notes,
    t.priority,
    t.due_date,
    t.completion_date,
    t.created_at,
    t.updated_at,

    -- بيانات المستخدمين
    u1.full_name AS data_entry_user,
    u2.full_name AS researcher_1_name,
    u3.full_name AS researcher_2_name,
    u4.full_name AS created_by_name,
    u5.full_name AS updated_by_name,

    -- بيانات الجداول المساعدة
    vt.visa_type,
    rfs.received_from,
    at.action_taken,
    rs.request_status,
    rs.status_color,

    -- حسابات مفيدة
    DATEDIFF(CURDATE(), t.head_incoming_date) AS days_since_received,
    CASE
        WHEN t.due_date IS NULL THEN NULL
        WHEN t.due_date < CURDATE() THEN DATEDIFF(CURDATE(), t.due_date)
        ELSE 0
    END AS days_overdue

FROM transactions t
LEFT JOIN users u1 ON t.user_id = u1.user_id
LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
LEFT JOIN users u4 ON t.created_by = u4.user_id
LEFT JOIN users u5 ON t.updated_by = u5.user_id
LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
LEFT JOIN actions_taken at ON t.action_taken_id = at.id
LEFT JOIN request_statuses rs ON t.request_status_id = rs.id;

-- مشاهدة إحصائيات المعاملات
CREATE OR REPLACE VIEW v_transactions_stats AS
SELECT
    COUNT(*) AS total_transactions,
    COUNT(CASE WHEN rs.request_status = 'جديد' THEN 1 END) AS new_transactions,
    COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) AS processing_transactions,
    COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) AS completed_transactions,
    COUNT(CASE WHEN t.due_date < CURDATE() AND rs.request_status != 'مكتمل' THEN 1 END) AS overdue_transactions,
    AVG(DATEDIFF(t.completion_date, t.head_incoming_date)) AS avg_completion_days
FROM transactions t
LEFT JOIN request_statuses rs ON t.request_status_id = rs.id;

-- =====================================================
-- إنشاء المحفزات (Triggers) لتسجيل التغييرات
-- Create Triggers for Change Logging
-- =====================================================

DELIMITER //

-- محفز لتسجيل التغييرات عند التحديث
CREATE TRIGGER tr_transactions_update_log
AFTER UPDATE ON transactions
FOR EACH ROW
BEGIN
    -- تسجيل تغيير الموضوع
    IF OLD.subject != NEW.subject THEN
        INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
        VALUES (NEW.id, 'subject', OLD.subject, NEW.subject, 'UPDATE', NEW.updated_by);
    END IF;

    -- تسجيل تغيير حالة الطلب
    IF OLD.request_status_id != NEW.request_status_id THEN
        INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
        VALUES (NEW.id, 'request_status_id', OLD.request_status_id, NEW.request_status_id, 'UPDATE', NEW.updated_by);
    END IF;

    -- تسجيل تغيير الباحث الأول
    IF OLD.researcher_1_id != NEW.researcher_1_id THEN
        INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
        VALUES (NEW.id, 'researcher_1_id', OLD.researcher_1_id, NEW.researcher_1_id, 'UPDATE', NEW.updated_by);
    END IF;
END//

-- محفز لتسجيل إنشاء معاملة جديدة
CREATE TRIGGER tr_transactions_insert_log
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
    VALUES (NEW.id, 'created', NULL, 'معاملة جديدة', 'INSERT', NEW.created_by);
END//

DELIMITER ;

-- =====================================================
-- منح الصلاحيات
-- Grant Permissions
-- =====================================================

-- إنشاء مستخدم التطبيق (اختياري)
-- CREATE USER IF NOT EXISTS 'iots_app'@'%' IDENTIFIED BY 'secure_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON iots_system.* TO 'iots_app'@'%';
-- FLUSH PRIVILEGES;

-- =====================================================
-- تحسين إعدادات قاعدة البيانات
-- Database Optimization Settings
-- =====================================================

-- تحسين إعدادات InnoDB
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL innodb_log_file_size = 64M;
SET GLOBAL innodb_flush_log_at_trx_commit = 2;

-- تحسين إعدادات الاستعلامات
SET GLOBAL query_cache_size = 32M;
SET GLOBAL query_cache_type = ON;

-- =====================================================
-- انتهاء السكريبت
-- End of Script
-- =====================================================

SELECT 'تم إنشاء قاعدة البيانات بنجاح - Database created successfully' AS status;
