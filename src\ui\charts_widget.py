# -*- coding: utf-8 -*-
"""
ويدجت الرسوم البيانية
Charts Widget

يعرض الرسوم البيانية والإحصائيات المرئية
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QScrollArea, QGridLayout, QSizePolicy)
from PyQt6.QtCore import Qt, QRect, pyqtSignal
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPainterPath

import math
from typing import List, Dict, Tuple

class PieChartWidget(QWidget):
    """ويدجت الرسم البياني الدائري"""
    
    def __init__(self, data: List[Dict], title: str = "", parent=None):
        super().__init__(parent)
        
        self.data = data
        self.title = title
        self.colors = [
            QColor("#007bff"), QColor("#28a745"), QColor("#ffc107"), 
            QColor("#dc3545"), QColor("#6f42c1"), QColor("#17a2b8"),
            QColor("#fd7e14"), QColor("#20c997"), QColor("#e83e8c")
        ]
        
        self.setMinimumSize(300, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    def paintEvent(self, event):
        """رسم الرسم البياني الدائري"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # حساب المجموع الكلي
        total = sum(item.get('value', 0) for item in self.data)
        if total == 0:
            self.draw_no_data(painter)
            return
        
        # حساب أبعاد الرسم
        rect = self.rect()
        center_x = rect.width() // 2
        center_y = rect.height() // 2
        radius = min(center_x, center_y) - 60
        
        # رسم العنوان
        if self.title:
            painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            painter.setPen(QColor("#2c3e50"))
            title_rect = QRect(0, 10, rect.width(), 30)
            painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, self.title)
        
        # رسم الدائرة
        start_angle = 0
        legend_y = center_y + radius + 20
        
        for i, item in enumerate(self.data):
            value = item.get('value', 0)
            label = item.get('label', f'Item {i+1}')
            
            # حساب الزاوية
            angle = int((value / total) * 360 * 16)  # Qt uses 1/16th degrees
            
            # اختيار اللون
            color = self.colors[i % len(self.colors)]
            
            # رسم القطعة
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(QColor("#ffffff"), 2))
            
            pie_rect = QRect(center_x - radius, center_y - radius, 
                           radius * 2, radius * 2)
            painter.drawPie(pie_rect, start_angle, angle)
            
            # رسم النسبة المئوية
            if angle > 0:
                mid_angle = start_angle + angle // 2
                text_angle = mid_angle / 16.0 * math.pi / 180.0
                text_radius = radius * 0.7
                text_x = center_x + text_radius * math.cos(text_angle)
                text_y = center_y + text_radius * math.sin(text_angle)
                
                percentage = (value / total) * 100
                if percentage >= 5:  # عرض النسبة فقط إذا كانت أكبر من 5%
                    painter.setPen(QColor("#ffffff"))
                    painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))
                    painter.drawText(int(text_x - 15), int(text_y + 5), f"{percentage:.1f}%")
            
            start_angle += angle
        
        # رسم وصف الألوان
        self.draw_legend(painter, rect)
    
    def draw_legend(self, painter, rect):
        """رسم وصف الألوان"""
        legend_x = 10
        legend_y = rect.height() - (len(self.data) * 25) - 10
        
        painter.setFont(QFont("Arial", 10))
        
        for i, item in enumerate(self.data):
            color = self.colors[i % len(self.colors)]
            label = item.get('label', f'Item {i+1}')
            value = item.get('value', 0)
            
            # رسم المربع الملون
            color_rect = QRect(legend_x, legend_y + i * 25, 15, 15)
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color))
            painter.drawRect(color_rect)
            
            # رسم النص
            painter.setPen(QColor("#2c3e50"))
            text_rect = QRect(legend_x + 25, legend_y + i * 25, 200, 20)
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignVCenter, 
                           f"{label}: {value}")
    
    def draw_no_data(self, painter):
        """رسم رسالة عدم وجود بيانات"""
        painter.setPen(QColor("#6c757d"))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "لا توجد بيانات للعرض")

class BarChartWidget(QWidget):
    """ويدجت الرسم البياني العمودي"""
    
    def __init__(self, data: List[Dict], title: str = "", parent=None):
        super().__init__(parent)
        
        self.data = data
        self.title = title
        self.bar_color = QColor("#007bff")
        
        self.setMinimumSize(400, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    def paintEvent(self, event):
        """رسم الرسم البياني العمودي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if not self.data:
            self.draw_no_data(painter)
            return
        
        rect = self.rect()
        
        # رسم العنوان
        title_height = 0
        if self.title:
            painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            painter.setPen(QColor("#2c3e50"))
            title_rect = QRect(0, 10, rect.width(), 30)
            painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, self.title)
            title_height = 40
        
        # حساب أبعاد الرسم
        margin = 50
        chart_rect = QRect(margin, title_height + margin, 
                          rect.width() - 2 * margin, 
                          rect.height() - title_height - 2 * margin - 50)
        
        # العثور على أقصى قيمة
        max_value = max(item.get('value', 0) for item in self.data)
        if max_value == 0:
            max_value = 1
        
        # حساب عرض الأعمدة
        bar_width = chart_rect.width() // len(self.data) - 10
        bar_spacing = 10
        
        # رسم الأعمدة
        for i, item in enumerate(self.data):
            value = item.get('value', 0)
            label = item.get('label', f'Item {i+1}')
            
            # حساب ارتفاع العمود
            bar_height = int((value / max_value) * chart_rect.height())
            
            # حساب موقع العمود
            bar_x = chart_rect.x() + i * (bar_width + bar_spacing)
            bar_y = chart_rect.bottom() - bar_height
            
            # رسم العمود
            bar_rect = QRect(bar_x, bar_y, bar_width, bar_height)
            
            # تدرج لوني للعمود
            gradient_color = QColor(self.bar_color)
            gradient_color.setAlpha(200)
            painter.setBrush(QBrush(gradient_color))
            painter.setPen(QPen(self.bar_color, 2))
            painter.drawRect(bar_rect)
            
            # رسم القيمة فوق العمود
            painter.setPen(QColor("#2c3e50"))
            painter.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            value_rect = QRect(bar_x, bar_y - 25, bar_width, 20)
            painter.drawText(value_rect, Qt.AlignmentFlag.AlignCenter, str(value))
            
            # رسم التسمية تحت العمود
            painter.setFont(QFont("Arial", 9))
            label_rect = QRect(bar_x, chart_rect.bottom() + 5, bar_width, 40)
            painter.drawText(label_rect, Qt.AlignmentFlag.AlignCenter | Qt.TextFlag.TextWordWrap, label)
        
        # رسم المحاور
        self.draw_axes(painter, chart_rect, max_value)
    
    def draw_axes(self, painter, chart_rect, max_value):
        """رسم محاور الرسم البياني"""
        painter.setPen(QPen(QColor("#6c757d"), 1))
        
        # المحور السيني (X)
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.bottomRight())
        
        # المحور الصادي (Y)
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.topLeft())
        
        # خطوط الشبكة الأفقية
        painter.setPen(QPen(QColor("#e9ecef"), 1))
        for i in range(1, 6):
            y = chart_rect.bottom() - (i * chart_rect.height() // 5)
            painter.drawLine(chart_rect.left(), y, chart_rect.right(), y)
            
            # تسميات المحور الصادي
            value = int((i * max_value) // 5)
            painter.setPen(QColor("#6c757d"))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(chart_rect.left() - 40, y + 5, str(value))
            painter.setPen(QPen(QColor("#e9ecef"), 1))
    
    def draw_no_data(self, painter):
        """رسم رسالة عدم وجود بيانات"""
        painter.setPen(QColor("#6c757d"))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "لا توجد بيانات للعرض")

class LineChartWidget(QWidget):
    """ويدجت الرسم البياني الخطي"""
    
    def __init__(self, data: List[Dict], title: str = "", parent=None):
        super().__init__(parent)
        
        self.data = data
        self.title = title
        self.line_color = QColor("#28a745")
        self.point_color = QColor("#007bff")
        
        self.setMinimumSize(400, 300)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    def paintEvent(self, event):
        """رسم الرسم البياني الخطي"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        if not self.data or len(self.data) < 2:
            self.draw_no_data(painter)
            return
        
        rect = self.rect()
        
        # رسم العنوان
        title_height = 0
        if self.title:
            painter.setFont(QFont("Arial", 14, QFont.Weight.Bold))
            painter.setPen(QColor("#2c3e50"))
            title_rect = QRect(0, 10, rect.width(), 30)
            painter.drawText(title_rect, Qt.AlignmentFlag.AlignCenter, self.title)
            title_height = 40
        
        # حساب أبعاد الرسم
        margin = 50
        chart_rect = QRect(margin, title_height + margin, 
                          rect.width() - 2 * margin, 
                          rect.height() - title_height - 2 * margin - 50)
        
        # العثور على أقصى وأدنى قيمة
        values = [item.get('value', 0) for item in self.data]
        max_value = max(values)
        min_value = min(values)
        
        if max_value == min_value:
            max_value += 1
        
        # حساب النقاط
        points = []
        point_spacing = chart_rect.width() // (len(self.data) - 1)
        
        for i, item in enumerate(self.data):
            value = item.get('value', 0)
            
            x = chart_rect.x() + i * point_spacing
            y = chart_rect.bottom() - int(((value - min_value) / (max_value - min_value)) * chart_rect.height())
            
            points.append((x, y))
        
        # رسم الخطوط
        painter.setPen(QPen(self.line_color, 3))
        for i in range(len(points) - 1):
            painter.drawLine(points[i][0], points[i][1], points[i+1][0], points[i+1][1])
        
        # رسم النقاط
        painter.setBrush(QBrush(self.point_color))
        painter.setPen(QPen(self.point_color, 2))
        
        for i, (x, y) in enumerate(points):
            painter.drawEllipse(x - 4, y - 4, 8, 8)
            
            # رسم القيمة
            value = self.data[i].get('value', 0)
            painter.setPen(QColor("#2c3e50"))
            painter.setFont(QFont("Arial", 9))
            painter.drawText(x - 15, y - 10, str(value))
            
            # رسم التسمية
            label = self.data[i].get('label', f'P{i+1}')
            painter.drawText(x - 20, chart_rect.bottom() + 15, label)
            
            painter.setPen(QPen(self.point_color, 2))
        
        # رسم المحاور
        self.draw_axes(painter, chart_rect, max_value, min_value)
    
    def draw_axes(self, painter, chart_rect, max_value, min_value):
        """رسم محاور الرسم البياني"""
        painter.setPen(QPen(QColor("#6c757d"), 1))
        
        # المحور السيني (X)
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.bottomRight())
        
        # المحور الصادي (Y)
        painter.drawLine(chart_rect.bottomLeft(), chart_rect.topLeft())
        
        # خطوط الشبكة الأفقية
        painter.setPen(QPen(QColor("#e9ecef"), 1))
        for i in range(1, 6):
            y = chart_rect.bottom() - (i * chart_rect.height() // 5)
            painter.drawLine(chart_rect.left(), y, chart_rect.right(), y)
            
            # تسميات المحور الصادي
            value = min_value + (i * (max_value - min_value) // 5)
            painter.setPen(QColor("#6c757d"))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(chart_rect.left() - 40, y + 5, f"{value:.0f}")
            painter.setPen(QPen(QColor("#e9ecef"), 1))
    
    def draw_no_data(self, painter):
        """رسم رسالة عدم وجود بيانات"""
        painter.setPen(QColor("#6c757d"))
        painter.setFont(QFont("Arial", 12))
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, "لا توجد بيانات كافية للعرض")

class ChartsContainer(QWidget):
    """حاوي الرسوم البيانية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # الويدجت الرئيسي
        self.charts_widget = QWidget()
        self.charts_layout = QVBoxLayout(self.charts_widget)
        self.charts_layout.setSpacing(30)
        
        scroll_area.setWidget(self.charts_widget)
        layout.addWidget(scroll_area)
    
    def add_chart(self, chart_widget: QWidget):
        """إضافة رسم بياني"""
        # إطار للرسم البياني
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        frame_layout = QVBoxLayout(frame)
        frame_layout.addWidget(chart_widget)
        
        self.charts_layout.addWidget(frame)
    
    def clear_charts(self):
        """مسح جميع الرسوم البيانية"""
        while self.charts_layout.count():
            child = self.charts_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
