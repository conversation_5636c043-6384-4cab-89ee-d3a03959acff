#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام متابعة المراسلات والمعاملات - النسخة العاملة
IOTS - Working Version
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("نظام متابعة المراسلات والمعاملات")
    print("=" * 50)
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'data', 'reports', 'temp']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    try:
        # استيراد المكتبات المطلوبة
        print("تحميل المكتبات...")
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        print("✅ تم تحميل PyQt6")
        
        from utils.config_manager import ConfigManager
        from utils.logger import setup_logging
        from database.simple_connection_manager import SimpleConnectionManager
        from services.auth_service import AuthService
        from ui.login_window import LoginWindow
        print("✅ تم تحميل جميع المكتبات")
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        print("✅ تم إنشاء تطبيق Qt")
        
        # تحميل التكوين
        config = ConfigManager()
        print("✅ تم تحميل التكوين")
        
        # إعداد نظام السجلات
        setup_logging(config)
        logging.info("تم بدء تشغيل التطبيق")
        print("✅ تم إعداد نظام السجلات")
        
        # إعداد الخط والاتجاه للعربية
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        print("✅ تم إعداد دعم العربية")
        
        # تهيئة مدير الاتصال بقاعدة البيانات
        connection_manager = SimpleConnectionManager(config)
        logging.info("تم إنشاء مدير الاتصال بنجاح")
        print("✅ تم إنشاء مدير الاتصال")
        
        # عرض حالة الاتصال
        if connection_manager.is_online:
            print("🌐 النمط: MySQL (متصل)")
            logging.info("التطبيق يعمل في النمط المتصل (MySQL)")
        else:
            print("💾 النمط: SQLite (محلي)")
            logging.info("التطبيق يعمل في النمط المحلي (SQLite)")
        
        # تهيئة خدمة المصادقة
        auth_service = AuthService(connection_manager, config)
        logging.info("تم إنشاء خدمة المصادقة بنجاح")
        print("✅ تم إنشاء خدمة المصادقة")
        
        # اختبار الاتصال بقاعدة البيانات
        if connection_manager.test_connection():
            logging.info("تم الاتصال بقاعدة البيانات بنجاح")
            print("✅ تم الاتصال بقاعدة البيانات")
        else:
            logging.warning("فشل الاتصال بقاعدة البيانات")
            print("⚠️ فشل الاتصال بقاعدة البيانات")
        
        print("✅ تم إكمال التهيئة بنجاح")
        
        # إنشاء وعرض نافذة تسجيل الدخول
        print("عرض نافذة تسجيل الدخول...")
        login_window = LoginWindow(config, connection_manager)
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # عرض النافذة
        result = login_window.exec()
        
        if result == login_window.DialogCode.Accepted:
            print("✅ تم تسجيل الدخول بنجاح")
            
            # إنشاء وعرض النافذة الرئيسية
            try:
                from ui.main_window import MainWindow
                main_window = MainWindow(config, connection_manager, auth_service)
                print("✅ تم إنشاء النافذة الرئيسية")
                
                main_window.show()
                print("✅ تم عرض النافذة الرئيسية")
                
                # تشغيل حلقة الأحداث
                return app.exec()
                
            except Exception as e:
                logging.error(f"خطأ في إنشاء النافذة الرئيسية: {str(e)}")
                print(f"❌ خطأ في إنشاء النافذة الرئيسية: {str(e)}")
                
                # عرض رسالة خطأ
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Icon.Critical)
                msg.setWindowTitle("خطأ")
                msg.setText(f"حدث خطأ أثناء إنشاء النافذة الرئيسية:\n{str(e)}")
                msg.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
                msg.exec()
                
                return 1
        else:
            print("❌ تم إلغاء تسجيل الدخول")
            return 0
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {str(e)}"
        print(f"❌ {error_msg}")
        logging.error(error_msg)
        
        import traceback
        traceback.print_exc()
        
        # محاولة عرض رسالة خطأ إذا كان Qt متاحاً
        try:
            from PyQt6.QtWidgets import QApplication, QMessageBox
            from PyQt6.QtCore import Qt
            
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setWindowTitle("خطأ في التطبيق")
            msg.setText(error_msg)
            msg.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            msg.exec()
        except:
            pass
        
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\nتم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
