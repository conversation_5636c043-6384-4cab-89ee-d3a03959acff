# -*- coding: utf-8 -*-
"""
ويدجت إدارة المستخدمين
User Management Widget

واجهة إدارة المستخدمين للمدير
"""

from typing import Dict, List, Optional
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QPushButton, QLineEdit,
                            QComboBox, QLabel, QFrame, QMessageBox, QDialog,
                            QFormLayout, QCheckBox, QGroupBox, QSplitter,
                            QProgressBar, QTextEdit)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QIcon

from ui.base_widget import BaseWidget
from services.admin_service import AdminService
from utils.logger import Logger

class UserFormDialog(QDialog):
    """نافذة نموذج المستخدم"""
    
    user_saved = pyqtSignal(dict)
    
    def __init__(self, admin_service: AdminService, user_data: Dict = None, parent=None):
        super().__init__(parent)
        
        self.admin_service = admin_service
        self.user_data = user_data
        self.is_edit_mode = user_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل المستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(title_label)
        
        # النموذج
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        
        # اسم المستخدم
        self.username_edit = QLineEdit()
        self.username_edit.setStyleSheet(self.get_input_style())
        self.username_edit.setMaxLength(50)
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        # كلمة المرور
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setStyleSheet(self.get_input_style())
        password_label = "كلمة المرور الجديدة:" if self.is_edit_mode else "كلمة المرور:"
        form_layout.addRow(password_label, self.password_edit)
        
        # تأكيد كلمة المرور
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_edit.setStyleSheet(self.get_input_style())
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        # الاسم الكامل
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setStyleSheet(self.get_input_style())
        self.full_name_edit.setMaxLength(100)
        form_layout.addRow("الاسم الكامل:", self.full_name_edit)
        
        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setStyleSheet(self.get_input_style())
        self.email_edit.setMaxLength(100)
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setStyleSheet(self.get_input_style())
        self.phone_edit.setMaxLength(20)
        form_layout.addRow("رقم الهاتف:", self.phone_edit)
        
        # الصلاحية
        self.permission_combo = QComboBox()
        self.permission_combo.setStyleSheet(self.get_input_style())
        permissions = self.admin_service.get_available_permissions()
        for perm in permissions:
            self.permission_combo.addItem(perm['text'], perm['value'])
        form_layout.addRow("الصلاحية:", self.permission_combo)
        
        # حالة النشاط
        self.active_checkbox = QCheckBox("المستخدم نشط")
        self.active_checkbox.setChecked(True)
        self.active_checkbox.setStyleSheet("font-weight: bold; color: #27ae60;")
        form_layout.addRow("", self.active_checkbox)
        
        layout.addWidget(form_frame)
        
        # ملاحظة لكلمة المرور في وضع التعديل
        if self.is_edit_mode:
            note_label = QLabel("💡 اتركي كلمة المرور فارغة إذا كنت لا تريدين تغييرها")
            note_label.setStyleSheet("""
                QLabel {
                    color: #f39c12;
                    font-style: italic;
                    padding: 10px;
                    background-color: #fef9e7;
                    border: 1px solid #f1c40f;
                    border-radius: 4px;
                }
            """)
            layout.addWidget(note_label)
        
        # الأزرار
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet(self.get_button_style("#27ae60"))
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style("#95a5a6"))
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        layout.addWidget(buttons_frame)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_button.clicked.connect(self.save_user)
        self.cancel_button.clicked.connect(self.reject)
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if not self.user_data:
            return
        
        self.username_edit.setText(self.user_data.get('user_name', ''))
        self.full_name_edit.setText(self.user_data.get('full_name', ''))
        self.email_edit.setText(self.user_data.get('email', ''))
        self.phone_edit.setText(self.user_data.get('phone', ''))
        
        # تعيين الصلاحية
        permission = self.user_data.get('permission', 'user')
        for i in range(self.permission_combo.count()):
            if self.permission_combo.itemData(i) == permission:
                self.permission_combo.setCurrentIndex(i)
                break
        
        # حالة النشاط
        self.active_checkbox.setChecked(bool(self.user_data.get('is_active', 1)))
    
    def save_user(self):
        """حفظ المستخدم"""
        try:
            # جمع البيانات
            user_data = {
                'user_name': self.username_edit.text().strip(),
                'full_name': self.full_name_edit.text().strip(),
                'email': self.email_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'permission': self.permission_combo.currentData(),
                'is_active': 1 if self.active_checkbox.isChecked() else 0
            }
            
            # التحقق من البيانات الأساسية
            if not user_data['user_name']:
                self.show_error("اسم المستخدم مطلوب")
                return
            
            if not user_data['full_name']:
                self.show_error("الاسم الكامل مطلوب")
                return
            
            if not user_data['email']:
                self.show_error("البريد الإلكتروني مطلوب")
                return
            
            # التحقق من كلمة المرور
            password = self.password_edit.text()
            confirm_password = self.confirm_password_edit.text()
            
            if not self.is_edit_mode:
                # في وضع الإضافة، كلمة المرور مطلوبة
                if not password:
                    self.show_error("كلمة المرور مطلوبة")
                    return
                
                if len(password) < 6:
                    self.show_error("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                    return
                
                if password != confirm_password:
                    self.show_error("كلمة المرور وتأكيدها غير متطابقتين")
                    return
                
                user_data['user_pass'] = password
            
            else:
                # في وضع التعديل، كلمة المرور اختيارية
                if password:
                    if len(password) < 6:
                        self.show_error("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                        return
                    
                    if password != confirm_password:
                        self.show_error("كلمة المرور وتأكيدها غير متطابقتين")
                        return
                    
                    user_data['user_pass'] = password
            
            # حفظ المستخدم
            if self.is_edit_mode:
                success, message = self.admin_service.update_user(self.user_data['user_id'], user_data)
            else:
                success, message = self.admin_service.create_user(user_data)
            
            if success:
                self.show_success(message)
                self.user_saved.emit(user_data)
                self.accept()
            else:
                self.show_error(message)
                
        except Exception as e:
            self.show_error(f"خطأ في حفظ المستخدم: {str(e)}")
    
    def get_input_style(self) -> str:
        """نمط حقول الإدخال"""
        return """
            QLineEdit, QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 10px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """
    
    def get_button_style(self, color: str) -> str:
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#95a5a6": "#7f8c8d"
        }
        return color_map.get(color, color)
    
    def show_error(self, message: str):
        """عرض رسالة خطأ"""
        QMessageBox.critical(self, "خطأ", message)
    
    def show_success(self, message: str):
        """عرض رسالة نجاح"""
        QMessageBox.information(self, "نجح", message)

class UserManagementWidget(BaseWidget):
    """ويدجت إدارة المستخدمين"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.admin_service = AdminService(connection_manager)

        # البيانات الحالية
        self.current_users = []

        # مؤقت التحديث
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_users)

        super().__init__("إدارة المستخدمين", parent)
    
    def setup_content(self):
        """إعداد المحتوى"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.setup_toolbar(layout)
        
        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الجانب الأيسر - جدول المستخدمين
        self.setup_users_table(main_splitter)
        
        # الجانب الأيمن - تفاصيل المستخدم
        self.setup_user_details(main_splitter)
        
        # تعيين النسب
        main_splitter.setSizes([700, 300])
        
        layout.addWidget(main_splitter)
        
        # شريط المعلومات
        self.setup_info_bar(layout)
        
        self.content_layout.addLayout(layout)
        
        # تحميل البيانات الأولية
        self.load_users()
        
        # إعداد الاتصالات
        self.setup_connections()
    
    def setup_toolbar(self, layout):
        """إعداد شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                color: white;
                padding: 15px;
                border-radius: 8px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # العنوان
        title_label = QLabel("👥 إدارة المستخدمين")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار الإجراءات
        self.add_user_btn = QPushButton("➕ إضافة مستخدم")
        self.add_user_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        toolbar_layout.addWidget(self.add_user_btn)
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        toolbar_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_users_table(self, parent):
        """إعداد جدول المستخدمين"""
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)
        
        # عنوان الجدول
        table_title = QLabel("قائمة المستخدمين")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_title.setStyleSheet("color: #2c3e50; padding: 10px;")
        table_layout.addWidget(table_title)
        
        # الجدول
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(8)
        self.users_table.setHorizontalHeaderLabels([
            "المعرف", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", 
            "الصلاحية", "الحالة", "المعاملات", "الإجراءات"
        ])
        
        # إعداد الجدول
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.users_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.users_table.verticalHeader().setVisible(False)
        
        # تعيين عرض الأعمدة
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # المعرف
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # اسم المستخدم
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)           # الاسم الكامل
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # البريد
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # الصلاحية
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # المعاملات
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # الإجراءات
        
        table_layout.addWidget(self.users_table)
        parent.addWidget(table_widget)
    
    def setup_user_details(self, parent):
        """إعداد تفاصيل المستخدم"""
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(10, 0, 0, 0)
        
        # عنوان التفاصيل
        details_title = QLabel("تفاصيل المستخدم")
        details_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        details_title.setStyleSheet("color: #2c3e50; padding: 10px;")
        details_layout.addWidget(details_title)
        
        # منطقة التفاصيل
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
        details_layout.addWidget(self.details_text)
        
        # إحصائيات المستخدم
        stats_group = QGroupBox("إحصائيات المستخدم")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(150)
        self.stats_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        stats_layout.addWidget(self.stats_text)
        
        details_layout.addWidget(stats_group)
        
        details_layout.addStretch()
        
        parent.addWidget(details_widget)
    
    def setup_info_bar(self, layout):
        """إعداد شريط المعلومات"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Shape.Box)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        
        self.info_label = QLabel("عدد المستخدمين: 0")
        self.info_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        info_layout.addWidget(self.info_label)
        
        info_layout.addStretch()
        
        # شريط التقدم للتحديث
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        info_layout.addWidget(self.progress_bar)
        
        layout.addWidget(info_frame)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_user_btn.clicked.connect(self.add_user)
        self.refresh_btn.clicked.connect(self.refresh_users)
        
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)
    
    def load_users(self):
        """تحميل المستخدمين"""
        try:
            self.show_loading("جاري تحميل المستخدمين...")
            
            # الحصول على المستخدمين مع الإحصائيات
            users = self.admin_service.get_all_users_with_stats()
            
            self.current_users = users
            self.populate_users_table()
            self.update_info_bar()
            
            self.hide_loading()
            
        except Exception as e:
            self.hide_loading()
            self.logger.error(f"خطأ في تحميل المستخدمين: {str(e)}")
            self.show_error(f"فشل في تحميل المستخدمين: {str(e)}")
    
    def populate_users_table(self):
        """ملء جدول المستخدمين"""
        self.users_table.setRowCount(len(self.current_users))
        
        for row, user in enumerate(self.current_users):
            # المعرف
            self.users_table.setItem(row, 0, QTableWidgetItem(str(user.get('user_id', ''))))
            
            # اسم المستخدم
            username_item = QTableWidgetItem(str(user.get('user_name', '')))
            username_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            self.users_table.setItem(row, 1, username_item)
            
            # الاسم الكامل
            self.users_table.setItem(row, 2, QTableWidgetItem(str(user.get('full_name', ''))))
            
            # البريد الإلكتروني
            self.users_table.setItem(row, 3, QTableWidgetItem(str(user.get('email', ''))))
            
            # الصلاحية
            permission_item = QTableWidgetItem(user.get('permission_text', ''))
            permission_color = self.get_permission_color(user.get('permission', ''))
            permission_item.setBackground(QColor(permission_color + "30"))
            permission_item.setForeground(QColor(permission_color))
            permission_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.users_table.setItem(row, 4, permission_item)
            
            # الحالة
            status_item = QTableWidgetItem(user.get('status_text', ''))
            status_color = "#27ae60" if user.get('is_active') else "#e74c3c"
            status_item.setBackground(QColor(status_color + "30"))
            status_item.setForeground(QColor(status_color))
            status_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.users_table.setItem(row, 5, status_item)
            
            # عدد المعاملات
            transactions_text = f"{user.get('total_transactions', 0)} ({user.get('completion_rate', 0):.1f}%)"
            self.users_table.setItem(row, 6, QTableWidgetItem(transactions_text))
            
            # الإجراءات
            actions_widget = self.create_actions_widget(user)
            self.users_table.setCellWidget(row, 7, actions_widget)
    
    def create_actions_widget(self, user: Dict) -> QWidget:
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل المستخدم")
        edit_btn.setMaximumSize(25, 25)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        edit_btn.clicked.connect(lambda: self.edit_user(user))
        layout.addWidget(edit_btn)
        
        # زر تبديل الحالة
        toggle_icon = "🔒" if user.get('is_active') else "🔓"
        toggle_btn = QPushButton(toggle_icon)
        toggle_btn.setToolTip("تبديل حالة المستخدم")
        toggle_btn.setMaximumSize(25, 25)
        toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        toggle_btn.clicked.connect(lambda: self.toggle_user_status(user))
        layout.addWidget(toggle_btn)
        
        # زر إعادة تعيين كلمة المرور
        reset_btn = QPushButton("🔑")
        reset_btn.setToolTip("إعادة تعيين كلمة المرور")
        reset_btn.setMaximumSize(25, 25)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        reset_btn.clicked.connect(lambda: self.reset_user_password(user))
        layout.addWidget(reset_btn)
        
        return widget
    
    def on_user_selected(self):
        """معالجة تحديد مستخدم"""
        current_row = self.users_table.currentRow()
        if 0 <= current_row < len(self.current_users):
            user = self.current_users[current_row]
            self.display_user_details(user)
    
    def display_user_details(self, user: Dict):
        """عرض تفاصيل المستخدم"""
        details_text = f"""
معلومات المستخدم:
{'=' * 30}

المعرف: {user.get('user_id', 'غير محدد')}
اسم المستخدم: {user.get('user_name', 'غير محدد')}
الاسم الكامل: {user.get('full_name', 'غير محدد')}
البريد الإلكتروني: {user.get('email', 'غير محدد')}
رقم الهاتف: {user.get('phone', 'غير محدد')}
الصلاحية: {user.get('permission_text', 'غير محدد')}
الحالة: {user.get('status_text', 'غير محدد')}

تواريخ مهمة:
آخر تسجيل دخول: {user.get('last_login_formatted', 'لم يسجل دخول')}
تاريخ الإنشاء: {user.get('created_at_formatted', 'غير محدد')}
        """
        
        self.details_text.setText(details_text.strip())
        
        # الإحصائيات
        stats_text = f"""
إحصائيات الأداء:
{'=' * 20}

إجمالي المعاملات: {user.get('total_transactions', 0)}
المعاملات المكتملة: {user.get('completed_transactions', 0)}
المعاملات المعلقة: {user.get('pending_transactions', 0)}
معدل الإنجاز: {user.get('completion_rate', 0):.1f}%
متوسط أيام الإنجاز: {user.get('avg_completion_days', 0):.1f} يوم
        """
        
        self.stats_text.setText(stats_text.strip())
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            dialog = UserFormDialog(self.admin_service, parent=self)
            dialog.user_saved.connect(self.on_user_saved)
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة إضافة المستخدم: {str(e)}")
            self.show_error(f"فشل في فتح نافذة الإضافة: {str(e)}")
    
    def edit_user(self, user: Dict):
        """تعديل مستخدم"""
        try:
            dialog = UserFormDialog(self.admin_service, user, parent=self)
            dialog.user_saved.connect(self.on_user_saved)
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة تعديل المستخدم: {str(e)}")
            self.show_error(f"فشل في فتح نافذة التعديل: {str(e)}")
    
    def toggle_user_status(self, user: Dict):
        """تبديل حالة المستخدم"""
        try:
            current_status = "نشط" if user.get('is_active') else "غير نشط"
            new_status = "غير نشط" if user.get('is_active') else "نشط"
            
            reply = QMessageBox.question(
                self, 
                "تأكيد تغيير الحالة",
                f"هل أنت متأكد من تغيير حالة المستخدم '{user.get('full_name')}' من '{current_status}' إلى '{new_status}'؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                success, message = self.admin_service.toggle_user_status(user['user_id'])
                
                if success:
                    self.show_success(message)
                    self.refresh_users()
                else:
                    self.show_error(message)
                    
        except Exception as e:
            self.logger.error(f"خطأ في تبديل حالة المستخدم: {str(e)}")
            self.show_error(f"فشل في تغيير حالة المستخدم: {str(e)}")
    
    def reset_user_password(self, user: Dict):
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            from PyQt6.QtWidgets import QInputDialog
            
            new_password, ok = QInputDialog.getText(
                self,
                "إعادة تعيين كلمة المرور",
                f"أدخل كلمة المرور الجديدة للمستخدم '{user.get('full_name')}':",
                QLineEdit.EchoMode.Password
            )
            
            if ok and new_password:
                success, message = self.admin_service.reset_user_password(user['user_id'], new_password)
                
                if success:
                    self.show_success(message)
                else:
                    self.show_error(message)
                    
        except Exception as e:
            self.logger.error(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")
            self.show_error(f"فشل في إعادة تعيين كلمة المرور: {str(e)}")
    
    def on_user_saved(self, user_data: Dict):
        """معالجة حفظ المستخدم"""
        self.refresh_users()
    
    def refresh_users(self):
        """تحديث قائمة المستخدمين"""
        self.load_users()
    
    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        total_users = len(self.current_users)
        active_users = len([u for u in self.current_users if u.get('is_active')])
        
        self.info_label.setText(f"عدد المستخدمين: {total_users} (نشط: {active_users})")
    
    def show_loading(self, message: str):
        """عرض حالة التحميل"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.info_label.setText(message)
    
    def hide_loading(self):
        """إخفاء حالة التحميل"""
        self.progress_bar.setVisible(False)
        self.update_info_bar()
    
    def get_permission_color(self, permission: str) -> str:
        """الحصول على لون الصلاحية"""
        color_map = {
            'admin': '#e74c3c',
            'user': '#3498db',
            'viewer': '#95a5a6'
        }
        return color_map.get(permission, '#95a5a6')
    
    def get_toolbar_button_style(self, color: str) -> str:
        """نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)
    
    def show_success(self, message: str):
        """عرض رسالة نجاح"""
        QMessageBox.information(self, "نجح", message)
    
    def show_error(self, message: str):
        """عرض رسالة خطأ"""
        QMessageBox.critical(self, "خطأ", message)
    
    def refresh(self):
        """تحديث الويدجت"""
        self.refresh_users()
