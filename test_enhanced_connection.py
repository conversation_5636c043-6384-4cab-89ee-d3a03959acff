#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مدير الاتصال المحسن
Enhanced Connection Manager Test

يختبر الاتصال بـ MySQL و SQLite مع التبديل التلقائي
"""

import sys
import os

# إضافة مسار src
sys.path.insert(0, 'src')

def test_mysql_availability():
    """اختبار توفر مكتبة MySQL"""
    try:
        import mysql.connector
        print("✅ مكتبة mysql.connector متوفرة")
        return True
    except ImportError:
        print("❌ مكتبة mysql.connector غير متوفرة")
        return False

def test_enhanced_connection_manager():
    """اختبار مدير الاتصال المحسن"""
    
    print("اختبار مدير الاتصال المحسن...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.simple_connection_manager import SimpleConnectionManager
        
        # تحميل التكوين
        config = ConfigManager()
        print(f"✅ تم تحميل التكوين")
        print(f"   📝 MySQL Host: {config.mysql_host}")
        print(f"   📝 MySQL Database: {config.mysql_database}")
        print(f"   📝 SQLite Database: {config.sqlite_database}")
        
        # إنشاء مدير الاتصال
        conn_manager = SimpleConnectionManager(config)
        print(f"✅ تم إنشاء مدير الاتصال المحسن")
        
        # عرض النمط الحالي
        if conn_manager.is_online:
            print(f"🌐 النمط الحالي: MySQL (متصل)")
        else:
            print(f"💾 النمط الحالي: SQLite (محلي)")
        
        # اختبار الاتصال
        if conn_manager.test_connection():
            print(f"✅ اختبار الاتصال: نجح")
        else:
            print(f"❌ اختبار الاتصال: فشل")
            return False
        
        # اختبار استعلام بسيط
        try:
            users = conn_manager.execute_query("SELECT COUNT(*) as count FROM users")
            if users:
                print(f"✅ عدد المستخدمين: {users[0]['count']}")
            else:
                print(f"⚠️ لا توجد بيانات مستخدمين")
        except Exception as e:
            print(f"❌ خطأ في استعلام المستخدمين: {e}")
        
        # اختبار التبديل بين الأنماط
        print(f"\n--- اختبار التبديل بين الأنماط ---")
        
        if conn_manager.current_mode == 'mysql':
            print(f"محاولة التبديل إلى SQLite...")
            if conn_manager.switch_to_sqlite():
                print(f"✅ تم التبديل إلى SQLite")
                
                # اختبار استعلام في SQLite
                users = conn_manager.execute_query("SELECT COUNT(*) as count FROM users")
                if users:
                    print(f"✅ عدد المستخدمين في SQLite: {users[0]['count']}")
                
                # العودة إلى MySQL
                print(f"محاولة العودة إلى MySQL...")
                if conn_manager.switch_to_mysql():
                    print(f"✅ تم العودة إلى MySQL")
                else:
                    print(f"⚠️ لم يتم العودة إلى MySQL")
            else:
                print(f"❌ فشل التبديل إلى SQLite")
        else:
            print(f"محاولة التبديل إلى MySQL...")
            if conn_manager.switch_to_mysql():
                print(f"✅ تم التبديل إلى MySQL")
                
                # اختبار استعلام في MySQL
                users = conn_manager.execute_query("SELECT COUNT(*) as count FROM users")
                if users:
                    print(f"✅ عدد المستخدمين في MySQL: {users[0]['count']}")
                
                # العودة إلى SQLite
                print(f"العودة إلى SQLite...")
                conn_manager.switch_to_sqlite()
                print(f"✅ تم العودة إلى SQLite")
            else:
                print(f"⚠️ MySQL غير متاح، البقاء في SQLite")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الاتصال: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentication():
    """اختبار نظام المصادقة"""
    
    print("\nاختبار نظام المصادقة...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.simple_connection_manager import SimpleConnectionManager
        from services.auth_service import AuthService
        
        config = ConfigManager()
        conn_manager = SimpleConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        print(f"✅ تم إنشاء خدمة المصادقة")
        
        # اختبار تسجيل الدخول
        login_result = auth_service.login("admin", "admin")

        if login_result.get('success', False):
            print(f"✅ تسجيل الدخول نجح")
            user = login_result.get('user', {})
            print(f"   👤 المستخدم: {user.get('full_name', 'غير محدد')}")
            print(f"   🔑 الصلاحية: {user.get('permission', 'غير محدد')}")

            if conn_manager.is_online:
                print(f"   🌐 قاعدة البيانات: MySQL")
            else:
                print(f"   💾 قاعدة البيانات: SQLite")

            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {login_result.get('message', 'خطأ غير معروف')}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المصادقة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("اختبار مدير الاتصال المحسن")
    print("=" * 60)
    
    # اختبار توفر MySQL
    mysql_available = test_mysql_availability()
    
    tests = [
        ("مدير الاتصال المحسن", test_enhanced_connection_manager),
        ("نظام المصادقة", test_authentication),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"اختبار: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج الاختبار: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        
        if mysql_available:
            print("\n💡 نصائح:")
            print("- إذا كان لديك خادم MySQL، شغل: python setup_mysql.py")
            print("- ثم شغل التطبيق: python main.py")
            print("- سيتصل التطبيق تلقائياً بـ MySQL إذا كان متاحاً")
        else:
            print("\n💡 نصائح:")
            print("- لتثبيت دعم MySQL: pip install mysql-connector-python")
            print("- ثم شغل: python setup_mysql.py")
            print("- التطبيق يعمل حالياً في النمط المحلي (SQLite)")
        
        print("\nيمكنك الآن تشغيل التطبيق:")
        print("python main.py")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
