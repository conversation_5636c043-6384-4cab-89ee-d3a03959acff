# -*- coding: utf-8 -*-
"""
الويدجت الأساسي
Base Widget

فئة أساسية لجميع الويدجتات في التطبيق
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from utils.logger import Logger

class BaseWidget(QWidget):
    """فئة أساسية لجميع الويدجتات"""
    
    # إشارات مشتركة
    loading_started = pyqtSignal()
    loading_finished = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.logger = Logger(self.__class__.__name__)
        self.is_loading = False
        
        self.setup_base_ui()
    
    def setup_base_ui(self):
        """إعداد الواجهة الأساسية"""
        # التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # شريط العنوان (اختياري)
        if self.title:
            self.setup_title_bar()
        
        # منطقة المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.main_layout.addWidget(self.content_widget)
        
        # إعداد المحتوى المخصص
        self.setup_content()
    
    def setup_title_bar(self):
        """إعداد شريط العنوان"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.Shape.Box)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        # عنوان الصفحة
        self.title_label = QLabel(self.title)
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: none;
                background: transparent;
            }
        """)
        title_layout.addWidget(self.title_label)
        
        title_layout.addStretch()
        
        # أزرار إضافية (يمكن للفئات المشتقة إضافتها)
        self.title_buttons_layout = QHBoxLayout()
        title_layout.addLayout(self.title_buttons_layout)
        
        self.main_layout.addWidget(title_frame)
    
    def setup_content(self):
        """إعداد المحتوى - يجب تنفيذها في الفئات المشتقة"""
        pass
    
    def add_title_button(self, button: QPushButton):
        """إضافة زر لشريط العنوان"""
        if hasattr(self, 'title_buttons_layout'):
            self.title_buttons_layout.addWidget(button)
    
    def set_title(self, title: str):
        """تعيين عنوان جديد"""
        self.title = title
        if hasattr(self, 'title_label'):
            self.title_label.setText(title)
    
    def show_loading(self, message: str = "جاري التحميل..."):
        """عرض حالة التحميل"""
        self.is_loading = True
        self.loading_started.emit()
        
        # يمكن للفئات المشتقة تنفيذ منطق التحميل المخصص
        self.on_loading_started(message)
    
    def hide_loading(self):
        """إخفاء حالة التحميل"""
        self.is_loading = False
        self.loading_finished.emit()
        
        # يمكن للفئات المشتقة تنفيذ منطق إخفاء التحميل المخصص
        self.on_loading_finished()
    
    def on_loading_started(self, message: str):
        """معالجة بداية التحميل - يمكن تخصيصها في الفئات المشتقة"""
        pass
    
    def on_loading_finished(self):
        """معالجة انتهاء التحميل - يمكن تخصيصها في الفئات المشتقة"""
        pass
    
    def show_error(self, error_message: str):
        """عرض رسالة خطأ"""
        self.logger.error(error_message)
        self.error_occurred.emit(error_message)
    
    def refresh(self):
        """تحديث المحتوى - يجب تنفيذها في الفئات المشتقة"""
        pass
    
    def get_common_styles(self):
        """الحصول على الأنماط المشتركة"""
        return {
            'card': """
                QFrame {
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """,
            'button_primary': """
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:pressed {
                    background-color: #004085;
                }
                QPushButton:disabled {
                    background-color: #6c757d;
                }
            """,
            'button_secondary': """
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #545b62;
                }
                QPushButton:pressed {
                    background-color: #3d4142;
                }
            """,
            'button_success': """
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1e7e34;
                }
            """,
            'button_danger': """
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """,
            'input': """
                QLineEdit, QTextEdit, QComboBox {
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                    padding: 8px;
                    font-size: 14px;
                }
                QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                    border-color: #007bff;
                    outline: none;
                }
            """,
            'table': """
                QTableWidget {
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                    gridline-color: #dee2e6;
                }
                QTableWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #f8f9fa;
                }
                QTableWidget::item:selected {
                    background-color: #007bff;
                    color: white;
                }
                QHeaderView::section {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    padding: 8px;
                    font-weight: bold;
                }
            """
        }
    
    def apply_style(self, widget, style_name: str):
        """تطبيق نمط على ويدجت"""
        styles = self.get_common_styles()
        if style_name in styles:
            widget.setStyleSheet(styles[style_name])
    
    def create_separator(self):
        """إنشاء خط فاصل"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("color: #dee2e6;")
        return separator
