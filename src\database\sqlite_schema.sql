-- =====================================================
-- نظام متابعة المراسلات والمعاملات (الوارد والصادر)
-- IOTS - Incoming/Outgoing Transactions System
-- 
-- سكريبت إنشاء قاعدة البيانات SQLite (للنمط المحلي)
-- SQLite Database Schema Script (for Offline Mode)
-- 
-- المطور: Augment Agent
-- التاريخ: 2025-08-05
-- =====================================================

-- تفعيل المفاتيح الخارجية في SQLite
PRAGMA foreign_keys = ON;

-- =====================================================
-- جدول المستخدمين
-- Users Table
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_name TEXT NOT NULL UNIQUE,
    user_pass TEXT NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    permission TEXT NOT NULL DEFAULT 'user' CHECK (permission IN ('admin', 'user')),
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime')),
    last_login TEXT,
    login_attempts INTEGER DEFAULT 0,
    locked_until TEXT
);

-- =====================================================
-- جدول أنواع التأشيرات
-- Visa Types Table
-- =====================================================
CREATE TABLE IF NOT EXISTS visa_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    visa_type TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime'))
);

-- =====================================================
-- جدول مصادر الورود
-- Received From Sources Table
-- =====================================================
CREATE TABLE IF NOT EXISTS received_from_sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    received_from TEXT NOT NULL UNIQUE,
    contact_info TEXT,
    address TEXT,
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime'))
);

-- =====================================================
-- جدول الإجراءات المتخذة
-- Actions Taken Table
-- =====================================================
CREATE TABLE IF NOT EXISTS actions_taken (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    action_taken TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime'))
);

-- =====================================================
-- جدول حالات الطلبات
-- Request Statuses Table
-- =====================================================
CREATE TABLE IF NOT EXISTS request_statuses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_status TEXT NOT NULL UNIQUE,
    status_color TEXT DEFAULT '#000000',
    description TEXT,
    is_active INTEGER DEFAULT 1 CHECK (is_active IN (0, 1)),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime'))
);

-- =====================================================
-- جدول المعاملات الرئيسي
-- Main Transactions Table
-- =====================================================
CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    head_incoming_no TEXT NOT NULL UNIQUE,
    head_incoming_date TEXT NOT NULL,
    subject TEXT NOT NULL,
    researcher_notes TEXT,
    
    -- المفاتيح الخارجية
    user_id INTEGER NOT NULL,
    researcher_1_id INTEGER,
    researcher_2_id INTEGER,
    visa_type_id INTEGER,
    received_from_id INTEGER,
    action_taken_id INTEGER,
    request_status_id INTEGER,
    
    -- معلومات إضافية
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    due_date TEXT,
    completion_date TEXT,
    
    -- معلومات التتبع
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    updated_at TEXT DEFAULT (datetime('now', 'localtime')),
    created_by INTEGER NOT NULL,
    updated_by INTEGER,
    
    -- القيود الخارجية
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (researcher_1_id) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (researcher_2_id) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (visa_type_id) REFERENCES visa_types(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (received_from_id) REFERENCES received_from_sources(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (action_taken_id) REFERENCES actions_taken(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (request_status_id) REFERENCES request_statuses(id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (updated_by) REFERENCES users(user_id) ON DELETE SET NULL ON UPDATE CASCADE
);

-- =====================================================
-- جدول سجل التغييرات
-- Transaction History/Audit Log Table
-- =====================================================
CREATE TABLE IF NOT EXISTS transaction_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,
    field_name TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_type TEXT NOT NULL CHECK (change_type IN ('INSERT', 'UPDATE', 'DELETE')),
    changed_by INTEGER NOT NULL,
    changed_at TEXT DEFAULT (datetime('now', 'localtime')),
    notes TEXT,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(user_id) ON DELETE RESTRICT ON UPDATE CASCADE
);

-- =====================================================
-- جدول المزامنة (خاص بـ SQLite)
-- Synchronization Table (SQLite specific)
-- =====================================================
CREATE TABLE IF NOT EXISTS sync_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    operation TEXT NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    sync_status TEXT DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
    created_at TEXT DEFAULT (datetime('now', 'localtime')),
    synced_at TEXT,
    error_message TEXT
);

-- =====================================================
-- إنشاء الفهارس لتحسين الأداء
-- Create Indexes for Performance Optimization
-- =====================================================

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_permission ON users(permission);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_user_name ON users(user_name);

-- فهارس جدول المعاملات
CREATE INDEX IF NOT EXISTS idx_transactions_head_incoming_date ON transactions(head_incoming_date);
CREATE INDEX IF NOT EXISTS idx_transactions_head_incoming_no ON transactions(head_incoming_no);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_researcher_1_id ON transactions(researcher_1_id);
CREATE INDEX IF NOT EXISTS idx_transactions_researcher_2_id ON transactions(researcher_2_id);
CREATE INDEX IF NOT EXISTS idx_transactions_visa_type_id ON transactions(visa_type_id);
CREATE INDEX IF NOT EXISTS idx_transactions_received_from_id ON transactions(received_from_id);
CREATE INDEX IF NOT EXISTS idx_transactions_action_taken_id ON transactions(action_taken_id);
CREATE INDEX IF NOT EXISTS idx_transactions_request_status_id ON transactions(request_status_id);
CREATE INDEX IF NOT EXISTS idx_transactions_priority ON transactions(priority);
CREATE INDEX IF NOT EXISTS idx_transactions_due_date ON transactions(due_date);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- فهارس جدول سجل التغييرات
CREATE INDEX IF NOT EXISTS idx_transaction_history_transaction_id ON transaction_history(transaction_id);
CREATE INDEX IF NOT EXISTS idx_transaction_history_changed_at ON transaction_history(changed_at);

-- فهارس جدول المزامنة
CREATE INDEX IF NOT EXISTS idx_sync_log_table_record ON sync_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_sync_log_status ON sync_log(sync_status);

-- =====================================================
-- إنشاء المحفزات (Triggers) لتحديث updated_at
-- Create Triggers for updating updated_at
-- =====================================================

-- محفز تحديث updated_at للمستخدمين
CREATE TRIGGER IF NOT EXISTS tr_users_updated_at
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = datetime('now', 'localtime') WHERE user_id = NEW.user_id;
END;

-- محفز تحديث updated_at للمعاملات
CREATE TRIGGER IF NOT EXISTS tr_transactions_updated_at
AFTER UPDATE ON transactions
FOR EACH ROW
BEGIN
    UPDATE transactions SET updated_at = datetime('now', 'localtime') WHERE id = NEW.id;
END;

-- محفز لتسجيل التغييرات في المعاملات
CREATE TRIGGER IF NOT EXISTS tr_transactions_history_log
AFTER UPDATE ON transactions
FOR EACH ROW
BEGIN
    -- تسجيل تغيير الموضوع
    INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
    SELECT NEW.id, 'subject', OLD.subject, NEW.subject, 'UPDATE', NEW.updated_by
    WHERE OLD.subject != NEW.subject;
    
    -- تسجيل تغيير حالة الطلب
    INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
    SELECT NEW.id, 'request_status_id', OLD.request_status_id, NEW.request_status_id, 'UPDATE', NEW.updated_by
    WHERE OLD.request_status_id != NEW.request_status_id;
    
    -- تسجيل تغيير الباحث الأول
    INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
    SELECT NEW.id, 'researcher_1_id', OLD.researcher_1_id, NEW.researcher_1_id, 'UPDATE', NEW.updated_by
    WHERE OLD.researcher_1_id != NEW.researcher_1_id;
END;

-- محفز لتسجيل إنشاء معاملة جديدة
CREATE TRIGGER IF NOT EXISTS tr_transactions_insert_log
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO transaction_history (transaction_id, field_name, old_value, new_value, change_type, changed_by)
    VALUES (NEW.id, 'created', NULL, 'معاملة جديدة', 'INSERT', NEW.created_by);
END;

-- محفز لتسجيل عمليات المزامنة
CREATE TRIGGER IF NOT EXISTS tr_sync_log_insert
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO sync_log (table_name, record_id, operation)
    VALUES ('transactions', NEW.id, 'INSERT');
END;

CREATE TRIGGER IF NOT EXISTS tr_sync_log_update
AFTER UPDATE ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO sync_log (table_name, record_id, operation)
    VALUES ('transactions', NEW.id, 'UPDATE');
END;

CREATE TRIGGER IF NOT EXISTS tr_sync_log_delete
AFTER DELETE ON transactions
FOR EACH ROW
BEGIN
    INSERT INTO sync_log (table_name, record_id, operation)
    VALUES ('transactions', OLD.id, 'DELETE');
END;

-- =====================================================
-- إدراج البيانات الأولية
-- Insert Initial Data
-- =====================================================

-- إدراج المستخدم الافتراضي (المدير)
INSERT OR IGNORE INTO users (user_name, user_pass, full_name, email, permission, is_active)
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9S2', 'مدير النظام', '<EMAIL>', 'admin', 1);

-- إدراج أنواع التأشيرات الأساسية
INSERT OR IGNORE INTO visa_types (visa_type, description) VALUES
('تأشيرة سياحة', 'تأشيرة للسياحة والزيارة'),
('تأشيرة عمل', 'تأشيرة للعمل والإقامة'),
('تأشيرة دراسة', 'تأشيرة للدراسة والتعليم'),
('تأشيرة عبور', 'تأشيرة للعبور والترانزيت'),
('تأشيرة علاج', 'تأشيرة للعلاج الطبي'),
('تأشيرة استثمار', 'تأشيرة للاستثمار والأعمال'),
('تأشيرة لم الشمل', 'تأشيرة لم الشمل العائلي');

-- إدراج مصادر الورود الأساسية
INSERT OR IGNORE INTO received_from_sources (received_from, contact_info) VALUES
('وزارة الخارجية', 'هاتف: 123456789'),
('السفارة السعودية', 'هاتف: 987654321'),
('القنصلية العامة', 'هاتف: 456789123'),
('مكتب الهجرة', 'هاتف: 789123456'),
('الجوازات', 'هاتف: 321654987'),
('مكتب التأشيرات', 'هاتف: 654987321');

-- إدراج الإجراءات المتخذة الأساسية
INSERT OR IGNORE INTO actions_taken (action_taken, description) VALUES
('تم الاستلام', 'تم استلام المعاملة'),
('قيد المراجعة', 'المعاملة قيد المراجعة والدراسة'),
('تم التوجيه للباحث', 'تم توجيه المعاملة للباحث المختص'),
('طلب معلومات إضافية', 'تم طلب معلومات أو مستندات إضافية'),
('تم الرد', 'تم الرد على المعاملة'),
('تم الإنجاز', 'تم إنجاز المعاملة بالكامل'),
('تم الأرشفة', 'تم أرشفة المعاملة'),
('تم التحويل', 'تم تحويل المعاملة لجهة أخرى');

-- إدراج حالات الطلبات الأساسية
INSERT OR IGNORE INTO request_statuses (request_status, status_color, description) VALUES
('جديد', '#007bff', 'معاملة جديدة لم تتم معالجتها بعد'),
('قيد المعالجة', '#ffc107', 'معاملة قيد المعالجة والدراسة'),
('في انتظار الرد', '#17a2b8', 'في انتظار رد من جهة خارجية'),
('مكتمل', '#28a745', 'تم إنجاز المعاملة بنجاح'),
('مؤجل', '#fd7e14', 'تم تأجيل المعاملة لوقت لاحق'),
('ملغي', '#dc3545', 'تم إلغاء المعاملة'),
('مرفوض', '#6c757d', 'تم رفض المعاملة');

-- =====================================================
-- إنشاء المشاهدات (Views) للاستعلامات المعقدة
-- Create Views for Complex Queries
-- =====================================================

-- مشاهدة تفاصيل المعاملات الكاملة
CREATE VIEW IF NOT EXISTS v_transactions_details AS
SELECT
    t.id,
    t.head_incoming_no,
    t.head_incoming_date,
    t.subject,
    t.researcher_notes,
    t.priority,
    t.due_date,
    t.completion_date,
    t.created_at,
    t.updated_at,

    -- بيانات المستخدمين
    u1.full_name AS data_entry_user,
    u2.full_name AS researcher_1_name,
    u3.full_name AS researcher_2_name,
    u4.full_name AS created_by_name,
    u5.full_name AS updated_by_name,

    -- بيانات الجداول المساعدة
    vt.visa_type,
    rfs.received_from,
    at.action_taken,
    rs.request_status,
    rs.status_color,

    -- حسابات مفيدة
    julianday('now') - julianday(t.head_incoming_date) AS days_since_received,
    CASE
        WHEN t.due_date IS NULL THEN NULL
        WHEN julianday(t.due_date) < julianday('now') THEN julianday('now') - julianday(t.due_date)
        ELSE 0
    END AS days_overdue

FROM transactions t
LEFT JOIN users u1 ON t.user_id = u1.user_id
LEFT JOIN users u2 ON t.researcher_1_id = u2.user_id
LEFT JOIN users u3 ON t.researcher_2_id = u3.user_id
LEFT JOIN users u4 ON t.created_by = u4.user_id
LEFT JOIN users u5 ON t.updated_by = u5.user_id
LEFT JOIN visa_types vt ON t.visa_type_id = vt.id
LEFT JOIN received_from_sources rfs ON t.received_from_id = rfs.id
LEFT JOIN actions_taken at ON t.action_taken_id = at.id
LEFT JOIN request_statuses rs ON t.request_status_id = rs.id;

-- مشاهدة إحصائيات المعاملات
CREATE VIEW IF NOT EXISTS v_transactions_stats AS
SELECT
    COUNT(*) AS total_transactions,
    COUNT(CASE WHEN rs.request_status = 'جديد' THEN 1 END) AS new_transactions,
    COUNT(CASE WHEN rs.request_status = 'قيد المعالجة' THEN 1 END) AS processing_transactions,
    COUNT(CASE WHEN rs.request_status = 'مكتمل' THEN 1 END) AS completed_transactions,
    COUNT(CASE WHEN julianday(t.due_date) < julianday('now') AND rs.request_status != 'مكتمل' THEN 1 END) AS overdue_transactions,
    AVG(julianday(t.completion_date) - julianday(t.head_incoming_date)) AS avg_completion_days
FROM transactions t
LEFT JOIN request_statuses rs ON t.request_status_id = rs.id;

-- =====================================================
-- انتهاء السكريبت
-- End of Script
-- =====================================================

SELECT 'تم إنشاء قاعدة البيانات المحلية بنجاح - Local database created successfully' AS status;
