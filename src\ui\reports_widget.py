# -*- coding: utf-8 -*-
"""
ويدجت التقارير
Reports Widget

واجهة إنشاء وتصدير التقارير المختلفة
"""

from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QLabel, QFrame, QPushButton, QComboBox, QDateEdit,
                            QGroupBox, QFormLayout, QCheckBox, QSpinBox,
                            QTextEdit, QProgressBar, QMessageBox, QFileDialog,
                            QSplitter, QScrollArea, QGridLayout)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPixmap

from ui.base_widget import BaseWidget
from services.reports_service import ReportsService
from services.export_service import ExportService
from utils.logger import Logger

class ReportGenerationThread(QThread):
    """خيط إنشاء التقرير"""
    
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, reports_service, report_type, parameters):
        super().__init__()
        self.reports_service = reports_service
        self.report_type = report_type
        self.parameters = parameters
    
    def run(self):
        """تشغيل إنشاء التقرير"""
        try:
            self.progress_updated.emit(10)
            
            if self.report_type == 'transactions':
                filters = self.parameters.get('filters', {})
                date_range = self.parameters.get('date_range')
                report_data = self.reports_service.generate_transactions_report(filters, date_range)
                
            elif self.report_type == 'performance':
                user_id = self.parameters.get('user_id')
                period = self.parameters.get('period', 'month')
                report_data = self.reports_service.generate_performance_report(user_id, period)
                
            elif self.report_type == 'status':
                include_charts = self.parameters.get('include_charts', True)
                report_data = self.reports_service.generate_status_report(include_charts)
                
            elif self.report_type == 'visa_types':
                report_data = self.reports_service.generate_visa_types_report()
                
            elif self.report_type == 'monthly_summary':
                year = self.parameters.get('year')
                report_data = self.reports_service.generate_monthly_summary_report(year)
                
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")
            
            self.progress_updated.emit(100)
            self.report_generated.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class ExportThread(QThread):
    """خيط التصدير"""
    
    export_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, export_service, report_data, format_type, filename):
        super().__init__()
        self.export_service = export_service
        self.report_data = report_data
        self.format_type = format_type
        self.filename = filename
    
    def run(self):
        """تشغيل التصدير"""
        try:
            self.progress_updated.emit(20)
            
            if self.format_type == 'excel':
                filepath = self.export_service.export_to_excel(self.report_data, self.filename)
            elif self.format_type == 'pdf':
                filepath = self.export_service.export_to_pdf(self.report_data, self.filename)
            elif self.format_type == 'json':
                filepath = self.export_service.export_to_json(self.report_data, self.filename)
            else:
                raise ValueError(f"صيغة تصدير غير مدعومة: {self.format_type}")
            
            self.progress_updated.emit(100)
            self.export_completed.emit(filepath)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class ReportParametersWidget(QWidget):
    """ويدجت معايير التقرير"""
    
    def __init__(self, report_type: str, connection_manager, parent=None):
        super().__init__(parent)
        
        self.report_type = report_type
        self.connection_manager = connection_manager
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # إعداد المعايير حسب نوع التقرير
        if self.report_type == 'transactions':
            self.setup_transactions_parameters(layout)
        elif self.report_type == 'performance':
            self.setup_performance_parameters(layout)
        elif self.report_type == 'status':
            self.setup_status_parameters(layout)
        elif self.report_type == 'visa_types':
            self.setup_visa_parameters(layout)
        elif self.report_type == 'monthly_summary':
            self.setup_monthly_parameters(layout)
        
        layout.addStretch()
    
    def setup_transactions_parameters(self, layout):
        """إعداد معايير تقرير المعاملات"""
        # مجموعة التصفية
        filter_group = QGroupBox("معايير التصفية")
        filter_layout = QFormLayout(filter_group)
        
        # نطاق التاريخ
        self.date_from_check = QCheckBox("من تاريخ:")
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setEnabled(False)
        self.date_from_check.toggled.connect(self.date_from.setEnabled)
        
        date_from_layout = QHBoxLayout()
        date_from_layout.addWidget(self.date_from_check)
        date_from_layout.addWidget(self.date_from)
        filter_layout.addRow("", date_from_layout)
        
        self.date_to_check = QCheckBox("إلى تاريخ:")
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setEnabled(False)
        self.date_to_check.toggled.connect(self.date_to.setEnabled)
        
        date_to_layout = QHBoxLayout()
        date_to_layout.addWidget(self.date_to_check)
        date_to_layout.addWidget(self.date_to)
        filter_layout.addRow("", date_to_layout)
        
        # حالة المعاملة
        self.status_check = QCheckBox("حالة المعاملة:")
        self.status_combo = QComboBox()
        self.status_combo.addItem("جميع الحالات", "")
        # يمكن إضافة الحالات من قاعدة البيانات
        self.status_combo.setEnabled(False)
        self.status_check.toggled.connect(self.status_combo.setEnabled)
        
        status_layout = QHBoxLayout()
        status_layout.addWidget(self.status_check)
        status_layout.addWidget(self.status_combo)
        filter_layout.addRow("", status_layout)
        
        # الأولوية
        self.priority_check = QCheckBox("الأولوية:")
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["جميع الأولويات", "عاجل", "مهم", "عادي"])
        self.priority_combo.setEnabled(False)
        self.priority_check.toggled.connect(self.priority_combo.setEnabled)
        
        priority_layout = QHBoxLayout()
        priority_layout.addWidget(self.priority_check)
        priority_layout.addWidget(self.priority_combo)
        filter_layout.addRow("", priority_layout)
        
        layout.addWidget(filter_group)
    
    def setup_performance_parameters(self, layout):
        """إعداد معايير تقرير الأداء"""
        # مجموعة المعايير
        params_group = QGroupBox("معايير التقرير")
        params_layout = QFormLayout(params_group)
        
        # المستخدم
        self.user_check = QCheckBox("مستخدم محدد:")
        self.user_combo = QComboBox()
        self.user_combo.addItem("جميع المستخدمين", None)
        # يمكن إضافة المستخدمين من قاعدة البيانات
        self.user_combo.setEnabled(False)
        self.user_check.toggled.connect(self.user_combo.setEnabled)
        
        user_layout = QHBoxLayout()
        user_layout.addWidget(self.user_check)
        user_layout.addWidget(self.user_combo)
        params_layout.addRow("", user_layout)
        
        # الفترة الزمنية
        self.period_combo = QComboBox()
        periods = [
            ("اليوم الحالي", "day"),
            ("الأسبوع الحالي", "week"),
            ("الشهر الحالي", "month"),
            ("السنة الحالية", "year")
        ]
        for text, value in periods:
            self.period_combo.addItem(text, value)
        
        params_layout.addRow("الفترة الزمنية:", self.period_combo)
        
        layout.addWidget(params_group)
    
    def setup_status_parameters(self, layout):
        """إعداد معايير تقرير الحالات"""
        # مجموعة الخيارات
        options_group = QGroupBox("خيارات التقرير")
        options_layout = QFormLayout(options_group)
        
        # تضمين الرسوم البيانية
        self.include_charts_check = QCheckBox("تضمين بيانات الرسوم البيانية")
        self.include_charts_check.setChecked(True)
        options_layout.addRow("", self.include_charts_check)
        
        layout.addWidget(options_group)
    
    def setup_visa_parameters(self, layout):
        """إعداد معايير تقرير أنواع التأشيرات"""
        # لا توجد معايير خاصة لهذا التقرير
        info_label = QLabel("هذا التقرير لا يحتاج معايير إضافية")
        info_label.setStyleSheet("color: #6c757d; font-style: italic; padding: 20px;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
    
    def setup_monthly_parameters(self, layout):
        """إعداد معايير تقرير الملخص الشهري"""
        # مجموعة المعايير
        params_group = QGroupBox("معايير التقرير")
        params_layout = QFormLayout(params_group)
        
        # السنة
        self.year_spinbox = QSpinBox()
        self.year_spinbox.setRange(2020, 2030)
        self.year_spinbox.setValue(QDate.currentDate().year())
        params_layout.addRow("السنة:", self.year_spinbox)
        
        layout.addWidget(params_group)
    
    def get_parameters(self) -> Dict:
        """الحصول على المعايير المحددة"""
        parameters = {}
        
        if self.report_type == 'transactions':
            filters = {}
            
            # نطاق التاريخ
            if self.date_from_check.isChecked() and self.date_to_check.isChecked():
                parameters['date_range'] = (
                    self.date_from.date().toString('yyyy-MM-dd'),
                    self.date_to.date().toString('yyyy-MM-dd')
                )
            
            # الحالة
            if self.status_check.isChecked():
                status_value = self.status_combo.currentData()
                if status_value:
                    filters['status_id'] = status_value
            
            # الأولوية
            if self.priority_check.isChecked():
                priority_text = self.priority_combo.currentText()
                if priority_text != "جميع الأولويات":
                    filters['priority'] = priority_text
            
            if filters:
                parameters['filters'] = filters
        
        elif self.report_type == 'performance':
            if self.user_check.isChecked():
                user_id = self.user_combo.currentData()
                if user_id:
                    parameters['user_id'] = user_id
            
            parameters['period'] = self.period_combo.currentData()
        
        elif self.report_type == 'status':
            parameters['include_charts'] = self.include_charts_check.isChecked()
        
        elif self.report_type == 'monthly_summary':
            parameters['year'] = self.year_spinbox.value()
        
        return parameters

class ReportPreviewWidget(QWidget):
    """ويدجت معاينة التقرير"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_report_data = None
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # عنوان المعاينة
        title_label = QLabel("معاينة التقرير")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة المعاينة
        self.preview_area = QScrollArea()
        self.preview_area.setWidgetResizable(True)
        self.preview_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
            }
        """)
        
        # ويدجت المحتوى
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(15, 15, 15, 15)
        self.content_layout.setSpacing(15)
        
        # رسالة افتراضية
        self.default_message = QLabel("قم بإنشاء تقرير لعرض المعاينة هنا")
        self.default_message.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.default_message.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 16px;
                font-style: italic;
                padding: 50px;
            }
        """)
        self.content_layout.addWidget(self.default_message)
        
        self.preview_area.setWidget(self.content_widget)
        layout.addWidget(self.preview_area)
    
    def display_report(self, report_data: Dict):
        """عرض التقرير"""
        self.current_report_data = report_data
        
        # مسح المحتوى السابق
        for i in reversed(range(self.content_layout.count())):
            self.content_layout.itemAt(i).widget().setParent(None)
        
        # عنوان التقرير
        title = report_data.get('title', 'تقرير')
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        self.content_layout.addWidget(title_label)
        
        # تاريخ الإنشاء
        generated_at = report_data.get('generated_at', '')
        if generated_at:
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(generated_at.replace('Z', '+00:00'))
                formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                date_label = QLabel(f"تاريخ الإنشاء: {formatted_date}")
                date_label.setStyleSheet("color: #6c757d; font-size: 12px; padding: 5px;")
                date_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.content_layout.addWidget(date_label)
            except:
                pass
        
        # الإحصائيات الرئيسية
        if 'statistics' in report_data:
            self.add_statistics_section(report_data['statistics'])
        
        # الملخص
        if 'summary' in report_data:
            self.add_summary_section(report_data['summary'])
        
        # البيانات التفصيلية
        if 'monthly_data' in report_data:
            self.add_monthly_data_section(report_data['monthly_data'])
        
        if 'users_performance' in report_data:
            self.add_performance_section(report_data['users_performance'][:5])  # أول 5
        
        if 'status_statistics' in report_data:
            self.add_status_section(report_data['status_statistics'])
        
        if 'visa_statistics' in report_data:
            self.add_visa_section(report_data['visa_statistics'])
        
        self.content_layout.addStretch()
    
    def add_statistics_section(self, statistics: Dict):
        """إضافة قسم الإحصائيات"""
        stats_group = QGroupBox("الإحصائيات العامة")
        stats_layout = QGridLayout(stats_group)
        
        row = 0
        col = 0
        for key, value in statistics.items():
            # تسمية الإحصائية
            label_text = self.translate_stat_key(key)
            label = QLabel(f"{label_text}:")
            label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            stats_layout.addWidget(label, row, col * 2)
            
            # قيمة الإحصائية
            value_label = QLabel(str(value))
            value_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            stats_layout.addWidget(value_label, row, col * 2 + 1)
            
            col += 1
            if col >= 2:  # عمودين
                col = 0
                row += 1
        
        self.content_layout.addWidget(stats_group)
    
    def add_summary_section(self, summary: Dict):
        """إضافة قسم الملخص"""
        summary_group = QGroupBox("الملخص")
        summary_layout = QVBoxLayout(summary_group)
        
        for key, value in summary.items():
            label_text = self.translate_stat_key(key)
            summary_label = QLabel(f"• {label_text}: {value}")
            summary_label.setStyleSheet("padding: 3px; color: #2c3e50;")
            summary_layout.addWidget(summary_label)
        
        self.content_layout.addWidget(summary_group)
    
    def add_monthly_data_section(self, monthly_data: List[Dict]):
        """إضافة قسم البيانات الشهرية"""
        monthly_group = QGroupBox("البيانات الشهرية")
        monthly_layout = QVBoxLayout(monthly_group)
        
        # عرض أول 6 أشهر فقط في المعاينة
        for month_data in monthly_data[:6]:
            month_name = month_data.get('month_name', '')
            total = month_data.get('total', 0)
            completed = month_data.get('completed', 0)
            completion_rate = month_data.get('completion_rate', 0)
            
            month_label = QLabel(f"{month_name}: {total} معاملة ({completed} مكتملة - {completion_rate}%)")
            month_label.setStyleSheet("padding: 2px; color: #34495e;")
            monthly_layout.addWidget(month_label)
        
        if len(monthly_data) > 6:
            more_label = QLabel(f"... و {len(monthly_data) - 6} أشهر أخرى")
            more_label.setStyleSheet("padding: 2px; color: #95a5a6; font-style: italic;")
            monthly_layout.addWidget(more_label)
        
        self.content_layout.addWidget(monthly_group)
    
    def add_performance_section(self, users_performance: List[Dict]):
        """إضافة قسم الأداء"""
        performance_group = QGroupBox("أداء المستخدمين (أفضل 5)")
        performance_layout = QVBoxLayout(performance_group)
        
        for user_perf in users_performance:
            user = user_perf.get('user', {})
            user_name = user.get('full_name', 'غير محدد')
            total = user_perf.get('total', 0)
            completion_rate = user_perf.get('completion_rate', 0)
            
            user_label = QLabel(f"• {user_name}: {total} معاملة - معدل الإنجاز {completion_rate}%")
            user_label.setStyleSheet("padding: 2px; color: #2c3e50;")
            performance_layout.addWidget(user_label)
        
        self.content_layout.addWidget(performance_group)
    
    def add_status_section(self, status_statistics: List[Dict]):
        """إضافة قسم الحالات"""
        status_group = QGroupBox("إحصائيات الحالات")
        status_layout = QVBoxLayout(status_group)
        
        for status_stat in status_statistics:
            status = status_stat.get('status', '')
            count = status_stat.get('count', 0)
            percentage = status_stat.get('percentage', 0)
            
            status_label = QLabel(f"• {status}: {count} معاملة ({percentage}%)")
            status_label.setStyleSheet("padding: 2px; color: #34495e;")
            status_layout.addWidget(status_label)
        
        self.content_layout.addWidget(status_group)
    
    def add_visa_section(self, visa_statistics: List[Dict]):
        """إضافة قسم أنواع التأشيرات"""
        visa_group = QGroupBox("إحصائيات أنواع التأشيرات")
        visa_layout = QVBoxLayout(visa_group)
        
        for visa_stat in visa_statistics:
            visa_type = visa_stat.get('visa_type', '')
            count = visa_stat.get('count', 0)
            percentage = visa_stat.get('percentage', 0)
            completion_rate = visa_stat.get('completion_rate', 0)
            
            visa_label = QLabel(f"• {visa_type}: {count} معاملة ({percentage}%) - إنجاز {completion_rate}%")
            visa_label.setStyleSheet("padding: 2px; color: #2c3e50;")
            visa_layout.addWidget(visa_label)
        
        self.content_layout.addWidget(visa_group)
    
    def translate_stat_key(self, key: str) -> str:
        """ترجمة مفاتيح الإحصائيات"""
        translations = {
            'total': 'الإجمالي',
            'completed': 'المكتملة',
            'pending': 'المعلقة',
            'overdue': 'المتأخرة',
            'completion_rate': 'معدل الإنجاز %',
            'avg_processing_days': 'متوسط أيام المعالجة',
            'total_users': 'إجمالي المستخدمين',
            'total_transactions': 'إجمالي المعاملات',
            'most_common_status': 'الحالة الأكثر شيوعاً',
            'most_requested_type': 'النوع الأكثر طلباً',
            'top_performer': 'أفضل أداء',
            'overall_completion_rate': 'معدل الإنجاز العام'
        }
        return translations.get(key, key)

class ReportsWidget(BaseWidget):
    """ويدجت التقارير الرئيسي"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.reports_service = ReportsService(connection_manager)
        self.export_service = ExportService()

        # البيانات الحالية
        self.current_report_data = None
        self.current_report_type = None

        # خيوط العمل
        self.report_thread = None
        self.export_thread = None

        super().__init__("التقارير والإحصائيات", parent)
    
    def setup_content(self):
        """إعداد المحتوى"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.setup_toolbar(layout)
        
        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الجانب الأيسر - إعدادات التقرير
        self.setup_report_settings(main_splitter)
        
        # الجانب الأيمن - معاينة التقرير
        self.setup_report_preview(main_splitter)
        
        # تعيين النسب
        main_splitter.setSizes([400, 600])
        
        layout.addWidget(main_splitter)
        
        # شريط الحالة
        self.setup_status_bar(layout)
        
        self.content_layout.addLayout(layout)
    
    def setup_toolbar(self, layout):
        """إعداد شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                color: white;
                padding: 15px;
                border-radius: 8px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # العنوان
        title_label = QLabel("📊 التقارير والإحصائيات المتقدمة")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار الإجراءات
        self.generate_btn = QPushButton("🔄 إنشاء التقرير")
        self.generate_btn.setStyleSheet(self.get_toolbar_button_style("#27ae60"))
        self.generate_btn.clicked.connect(self.generate_report)
        toolbar_layout.addWidget(self.generate_btn)
        
        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.setStyleSheet(self.get_toolbar_button_style("#3498db"))
        self.export_btn.setEnabled(False)
        self.export_btn.clicked.connect(self.export_report)
        toolbar_layout.addWidget(self.export_btn)
        
        layout.addWidget(toolbar_frame)
    
    def setup_report_settings(self, parent):
        """إعداد إعدادات التقرير"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        
        # عنوان الإعدادات
        settings_title = QLabel("إعدادات التقرير")
        settings_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        settings_title.setStyleSheet("color: #2c3e50; padding: 10px;")
        settings_layout.addWidget(settings_title)
        
        # اختيار نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QVBoxLayout(report_type_group)
        
        self.report_type_combo = QComboBox()
        report_types = self.reports_service.get_available_report_types()
        for report_type in report_types:
            self.report_type_combo.addItem(
                f"{report_type['icon']} {report_type['name']}", 
                report_type['id']
            )
        
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        report_type_layout.addWidget(self.report_type_combo)
        
        # وصف نوع التقرير
        self.report_description = QLabel()
        self.report_description.setWordWrap(True)
        self.report_description.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-style: italic;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }
        """)
        report_type_layout.addWidget(self.report_description)
        
        settings_layout.addWidget(report_type_group)
        
        # معايير التقرير
        self.parameters_group = QGroupBox("معايير التقرير")
        self.parameters_layout = QVBoxLayout(self.parameters_group)
        
        # ويدجت المعايير (سيتم تغييره حسب نوع التقرير)
        self.parameters_widget = None
        
        settings_layout.addWidget(self.parameters_group)
        
        # تحديث المعايير للنوع الافتراضي
        self.on_report_type_changed()
        
        settings_layout.addStretch()
        
        parent.addWidget(settings_widget)
    
    def setup_report_preview(self, parent):
        """إعداد معاينة التقرير"""
        self.preview_widget = ReportPreviewWidget()
        parent.addWidget(self.preview_widget)
    
    def setup_status_bar(self, layout):
        """إعداد شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز لإنشاء التقرير")
        self.status_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_frame)
    
    def on_report_type_changed(self):
        """معالجة تغيير نوع التقرير"""
        report_id = self.report_type_combo.currentData()
        if not report_id:
            return
        
        # تحديث الوصف
        report_types = self.reports_service.get_available_report_types()
        for report_type in report_types:
            if report_type['id'] == report_id:
                self.report_description.setText(report_type['description'])
                break
        
        # إزالة ويدجت المعايير السابق
        if self.parameters_widget:
            self.parameters_widget.setParent(None)
        
        # إنشاء ويدجت معايير جديد
        self.parameters_widget = ReportParametersWidget(
            report_id, self.connection_manager
        )
        self.parameters_layout.addWidget(self.parameters_widget)
        
        self.current_report_type = report_id
    
    def generate_report(self):
        """إنشاء التقرير"""
        if not self.current_report_type or not self.parameters_widget:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع التقرير أولاً")
            return
        
        try:
            # الحصول على المعايير
            parameters = self.parameters_widget.get_parameters()
            
            # بدء إنشاء التقرير
            self.start_report_generation(parameters)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
    
    def start_report_generation(self, parameters):
        """بدء إنشاء التقرير في خيط منفصل"""
        # تعطيل الأزرار
        self.generate_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label.setText("جاري إنشاء التقرير...")
        
        # إنشاء وتشغيل خيط التقرير
        self.report_thread = ReportGenerationThread(
            self.reports_service, self.current_report_type, parameters
        )
        self.report_thread.report_generated.connect(self.on_report_generated)
        self.report_thread.error_occurred.connect(self.on_report_error)
        self.report_thread.progress_updated.connect(self.progress_bar.setValue)
        self.report_thread.start()
    
    def on_report_generated(self, report_data):
        """معالجة إنشاء التقرير بنجاح"""
        self.current_report_data = report_data
        
        # عرض التقرير في المعاينة
        self.preview_widget.display_report(report_data)
        
        # تفعيل زر التصدير
        self.export_btn.setEnabled(True)
        
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.status_label.setText("تم إنشاء التقرير بنجاح")
        
        # تفعيل زر الإنشاء
        self.generate_btn.setEnabled(True)
        
        # تنظيف الخيط
        if self.report_thread:
            self.report_thread.deleteLater()
            self.report_thread = None
    
    def on_report_error(self, error_message):
        """معالجة خطأ في إنشاء التقرير"""
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل في إنشاء التقرير")
        
        # تفعيل زر الإنشاء
        self.generate_btn.setEnabled(True)
        
        # عرض رسالة الخطأ
        QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{error_message}")
        
        # تنظيف الخيط
        if self.report_thread:
            self.report_thread.deleteLater()
            self.report_thread = None
    
    def export_report(self):
        """تصدير التقرير"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للتصدير")
            return
        
        # عرض نافذة اختيار صيغة التصدير
        self.show_export_dialog()
    
    def show_export_dialog(self):
        """عرض نافذة التصدير"""
        from PyQt6.QtWidgets import QDialog, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("تصدير التقرير")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # اختيار صيغة التصدير
        format_group = QGroupBox("صيغة التصدير")
        format_layout = QVBoxLayout(format_group)
        
        format_combo = QComboBox()
        export_formats = self.export_service.get_export_formats()
        for fmt in export_formats:
            format_combo.addItem(f"{fmt['icon']} {fmt['name']}", fmt['id'])
        
        format_layout.addWidget(format_combo)
        
        # وصف الصيغة
        format_description = QLabel()
        format_description.setWordWrap(True)
        format_description.setStyleSheet("color: #6c757d; font-style: italic; padding: 10px;")
        
        def update_description():
            current_format = format_combo.currentData()
            for fmt in export_formats:
                if fmt['id'] == current_format:
                    format_description.setText(fmt['description'])
                    break
        
        format_combo.currentTextChanged.connect(update_description)
        update_description()  # تحديث أولي
        
        format_layout.addWidget(format_description)
        layout.addWidget(format_group)
        
        # اسم الملف
        filename_group = QGroupBox("اسم الملف")
        filename_layout = QFormLayout(filename_group)
        
        filename_edit = QLineEdit()
        filename_edit.setText(f"report_{self.current_report_type}")
        filename_layout.addRow("اسم الملف:", filename_edit)
        
        layout.addWidget(filename_group)
        
        # الأزرار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # تشغيل النافذة
        if dialog.exec() == QDialog.DialogCode.Accepted:
            format_type = format_combo.currentData()
            filename = filename_edit.text().strip()
            
            if filename:
                self.start_export(format_type, filename)
            else:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الملف")
    
    def start_export(self, format_type, filename):
        """بدء التصدير في خيط منفصل"""
        # تعطيل الأزرار
        self.generate_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label.setText("جاري تصدير التقرير...")
        
        # إنشاء وتشغيل خيط التصدير
        self.export_thread = ExportThread(
            self.export_service, self.current_report_data, format_type, filename
        )
        self.export_thread.export_completed.connect(self.on_export_completed)
        self.export_thread.error_occurred.connect(self.on_export_error)
        self.export_thread.progress_updated.connect(self.progress_bar.setValue)
        self.export_thread.start()
    
    def on_export_completed(self, filepath):
        """معالجة اكتمال التصدير"""
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"تم تصدير التقرير إلى: {filepath}")
        
        # تفعيل الأزرار
        self.generate_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        
        # عرض رسالة النجاح
        reply = QMessageBox.information(
            self, 
            "نجح التصدير", 
            f"تم تصدير التقرير بنجاح إلى:\n{filepath}\n\nهل تريد فتح مجلد الملف؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            import os
            import subprocess
            import platform
            
            folder_path = os.path.dirname(filepath)
            
            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        
        # تنظيف الخيط
        if self.export_thread:
            self.export_thread.deleteLater()
            self.export_thread = None
    
    def on_export_error(self, error_message):
        """معالجة خطأ في التصدير"""
        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل في تصدير التقرير")
        
        # تفعيل الأزرار
        self.generate_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        
        # عرض رسالة الخطأ
        QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{error_message}")
        
        # تنظيف الخيط
        if self.export_thread:
            self.export_thread.deleteLater()
            self.export_thread = None
    
    def get_toolbar_button_style(self, color: str) -> str:
        """نمط أزرار شريط الأدوات"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:disabled {{
                background-color: #95a5a6;
                color: #ecf0f1;
            }}
        """
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)
    
    def refresh(self):
        """تحديث الويدجت"""
        # إعادة تحميل أنواع التقارير
        self.report_type_combo.clear()
        report_types = self.reports_service.get_available_report_types()
        for report_type in report_types:
            self.report_type_combo.addItem(
                f"{report_type['icon']} {report_type['name']}", 
                report_type['id']
            )
