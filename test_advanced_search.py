#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام البحث المتقدم
Advanced Search System Test

يختبر جميع وظائف البحث المتقدم والتصفية
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة مسار src
sys.path.insert(0, 'src')

def test_search_service():
    """اختبار خدمة البحث المتقدم"""
    
    print("اختبار خدمة البحث المتقدم...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.search_service import AdvancedSearchService, SearchCriteria
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        search_service = AdvancedSearchService(conn_manager)
        
        # اختبار البحث النصي البسيط
        criteria = SearchCriteria()
        criteria.text_query = "تأشيرة"
        criteria.search_mode = "contains"
        
        result = search_service.search(criteria)
        print(f"✅ البحث النصي: {len(result.transactions)} نتيجة في {result.search_time:.3f} ثانية")
        
        # اختبار البحث مع التصفية
        criteria = SearchCriteria()
        criteria.filters = {"priority": "medium"}
        
        result = search_service.search(criteria)
        print(f"✅ البحث مع التصفية: {len(result.transactions)} نتيجة")
        
        # اختبار البحث مع نطاق تاريخي
        criteria = SearchCriteria()
        criteria.date_from = "2024-01-01"
        criteria.date_to = "2024-12-31"
        criteria.date_field = "head_incoming_date"
        
        result = search_service.search(criteria)
        print(f"✅ البحث مع نطاق تاريخي: {len(result.transactions)} نتيجة")
        
        # اختبار البحث المعقد
        criteria = SearchCriteria()
        criteria.text_query = "دراسة"
        criteria.search_fields = ["subject", "researcher_notes"]
        criteria.filters = {"priority": "high"}
        criteria.sort_field = "created_at"
        criteria.sort_direction = "DESC"
        criteria.limit = 10
        
        result = search_service.search(criteria)
        print(f"✅ البحث المعقد: {len(result.transactions)} نتيجة")
        
        # اختبار الاقتراحات
        suggestions = search_service.get_search_suggestions("subject", "تأشيرة", 5)
        print(f"✅ اقتراحات البحث: {len(suggestions)} اقتراح")
        
        # اختبار خيارات التصفية
        filter_options = search_service.get_filter_options("request_status_id")
        print(f"✅ خيارات التصفية: {len(filter_options)} خيار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة البحث: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_search_criteria():
    """اختبار معايير البحث"""
    
    print("\nاختبار معايير البحث...")
    
    try:
        from services.search_service import SearchCriteria
        
        # إنشاء معايير بحث
        criteria = SearchCriteria()
        
        # تعيين البحث النصي
        criteria.text_query = "طلب تأشيرة"
        criteria.search_mode = "contains"
        criteria.case_sensitive = False
        criteria.search_fields = ["subject", "researcher_notes"]
        
        print("✅ تم إنشاء معايير البحث النصي")
        
        # تعيين التصفية
        criteria.filters = {
            "priority": "high",
            "request_status_id": 1,
            "overdue_only": True
        }
        
        print("✅ تم تعيين معايير التصفية")
        
        # تعيين نطاق التاريخ
        criteria.date_from = "2024-01-01"
        criteria.date_to = "2024-12-31"
        criteria.date_field = "head_incoming_date"
        
        print("✅ تم تعيين نطاق التاريخ")
        
        # تعيين الترتيب
        criteria.sort_field = "created_at"
        criteria.sort_direction = "DESC"
        criteria.limit = 50
        criteria.offset = 0
        
        print("✅ تم تعيين خيارات الترتيب")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معايير البحث: {str(e)}")
        return False

def test_search_performance():
    """اختبار أداء البحث"""
    
    print("\nاختبار أداء البحث...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.search_service import AdvancedSearchService, SearchCriteria
        import time
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        search_service = AdvancedSearchService(conn_manager)
        
        # اختبارات أداء مختلفة
        test_cases = [
            {
                "name": "بحث نصي بسيط",
                "criteria": lambda: SearchCriteria()
            },
            {
                "name": "بحث مع تصفية",
                "criteria": lambda: SearchCriteria()
            },
            {
                "name": "بحث معقد",
                "criteria": lambda: SearchCriteria()
            }
        ]
        
        # تعيين معايير الاختبار
        test_cases[0]["criteria"]().text_query = "تأشيرة"
        
        test_cases[1]["criteria"]().filters = {"priority": "medium"}
        
        complex_criteria = test_cases[2]["criteria"]()
        complex_criteria.text_query = "دراسة"
        complex_criteria.filters = {"priority": "high"}
        complex_criteria.date_from = "2024-01-01"
        
        total_time = 0
        successful_tests = 0
        
        for test_case in test_cases:
            try:
                start_time = time.time()
                criteria = test_case["criteria"]()
                result = search_service.search(criteria)
                end_time = time.time()
                
                search_time = end_time - start_time
                total_time += search_time
                successful_tests += 1
                
                print(f"✅ {test_case['name']}: {len(result.transactions)} نتيجة في {search_time:.3f} ثانية")
                
            except Exception as e:
                print(f"❌ فشل {test_case['name']}: {str(e)}")
        
        if successful_tests > 0:
            avg_time = total_time / successful_tests
            print(f"✅ متوسط وقت البحث: {avg_time:.3f} ثانية")
            
            # تقييم الأداء
            if avg_time < 0.1:
                print("🚀 أداء ممتاز (< 0.1 ثانية)")
            elif avg_time < 0.5:
                print("✅ أداء جيد (< 0.5 ثانية)")
            elif avg_time < 1.0:
                print("⚠️ أداء مقبول (< 1.0 ثانية)")
            else:
                print("🐌 أداء بطيء (> 1.0 ثانية)")
        
        return successful_tests == len(test_cases)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {str(e)}")
        return False

def test_search_accuracy():
    """اختبار دقة البحث"""
    
    print("\nاختبار دقة البحث...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.search_service import AdvancedSearchService, SearchCriteria
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        search_service = AdvancedSearchService(conn_manager)
        
        # اختبار دقة البحث النصي
        test_queries = [
            ("تأشيرة", "contains"),
            ("2024", "starts_with"),
            ("دراسة", "ends_with"),
            ("طلب تأشيرة دراسة", "exact")
        ]
        
        accurate_results = 0
        total_tests = len(test_queries)
        
        for query, mode in test_queries:
            try:
                criteria = SearchCriteria()
                criteria.text_query = query
                criteria.search_mode = mode
                
                result = search_service.search(criteria)
                
                # التحقق من دقة النتائج
                if len(result.transactions) >= 0:  # أي نتيجة صالحة
                    accurate_results += 1
                    print(f"✅ البحث عن '{query}' بنمط '{mode}': {len(result.transactions)} نتيجة")
                else:
                    print(f"❌ البحث عن '{query}' بنمط '{mode}': نتائج غير صحيحة")
                
            except Exception as e:
                print(f"❌ خطأ في البحث عن '{query}': {str(e)}")
        
        accuracy_rate = (accurate_results / total_tests) * 100
        print(f"✅ معدل الدقة: {accuracy_rate:.1f}%")
        
        return accuracy_rate >= 80  # 80% دقة مقبولة
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدقة: {str(e)}")
        return False

def test_filter_combinations():
    """اختبار تركيبات التصفية"""
    
    print("\nاختبار تركيبات التصفية...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.search_service import AdvancedSearchService, SearchCriteria
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        search_service = AdvancedSearchService(conn_manager)
        
        # تركيبات تصفية مختلفة
        filter_combinations = [
            {"priority": "medium"},
            {"overdue_only": True},
            {"completed_only": True},
            {"priority": "high", "overdue_only": True},
            {"new_only": True}
        ]
        
        successful_filters = 0
        
        for filters in filter_combinations:
            try:
                criteria = SearchCriteria()
                criteria.filters = filters
                
                result = search_service.search(criteria)
                
                filter_desc = ", ".join([f"{k}={v}" for k, v in filters.items()])
                print(f"✅ تصفية [{filter_desc}]: {len(result.transactions)} نتيجة")
                
                successful_filters += 1
                
            except Exception as e:
                print(f"❌ خطأ في التصفية {filters}: {str(e)}")
        
        success_rate = (successful_filters / len(filter_combinations)) * 100
        print(f"✅ معدل نجاح التصفية: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التصفية: {str(e)}")
        return False

def run_advanced_search_tests():
    """تشغيل جميع اختبارات البحث المتقدم"""
    
    print("=" * 60)
    print("اختبار نظام البحث المتقدم والتصفية")
    print("=" * 60)
    
    tests = [
        ("خدمة البحث المتقدم", test_search_service),
        ("معايير البحث", test_search_criteria),
        ("أداء البحث", test_search_performance),
        ("دقة البحث", test_search_accuracy),
        ("تركيبات التصفية", test_filter_combinations),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج اختبار البحث المتقدم: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات البحث المتقدم نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ البحث النصي المتقدم")
        print("- ✅ التصفية الديناميكية")
        print("- ✅ البحث في نطاقات تاريخية")
        print("- ✅ الترتيب والتصفح")
        print("- ✅ اقتراحات البحث")
        print("- ✅ البحث السريع")
        return True
    else:
        print("⚠️ بعض اختبارات البحث المتقدم فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_advanced_search_tests()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 نظام البحث المتقدم جاهز للاستخدام!")
            print("\nلتجربة النظام:")
            print("1. شغل التطبيق: python main.py")
            print("2. سجل الدخول: admin / admin")
            print("3. انتقل إلى 'البحث المتقدم'")
            print("4. جرب البحث النصي والتصفية")
            print("5. استكشف البحث السريع")
            print(f"{'=' * 60}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
