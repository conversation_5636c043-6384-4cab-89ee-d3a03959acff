#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة معاملات تجريبية
Add Sample Transactions

يضيف معاملات تجريبية لاختبار النظام
"""

import sys
import os
from datetime import datetime, date, timedelta
import random

# إضافة مسار src
sys.path.insert(0, 'src')

def add_sample_transactions():
    """إضافة معاملات تجريبية"""
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from database.transaction_repository import TransactionRepository
        from database.lookup_repositories import LookupService
        from database.user_repository import UserRepository
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        transaction_repo = TransactionRepository(conn_manager)
        lookup_service = LookupService(conn_manager)
        user_repo = UserRepository(conn_manager)
        
        # الحصول على البيانات المساعدة
        lookup_data = lookup_service.get_all_lookup_data()
        users = user_repo.get_all_users()
        
        # البيانات التجريبية
        sample_subjects = [
            "طلب تأشيرة دراسة للطالب أحمد محمد",
            "تجديد تأشيرة عمل للموظف سارة أحمد",
            "طلب تأشيرة زيارة عائلية",
            "تأشيرة استثمار لمشروع جديد",
            "طلب تأشيرة علاج طبي",
            "تأشيرة مؤتمر علمي دولي",
            "طلب تأشيرة سياحة",
            "تأشيرة عمل مؤقت",
            "طلب تأشيرة دراسة عليا",
            "تجديد تأشيرة إقامة"
        ]
        
        sample_notes = [
            "تم استلام الطلب وجاري المراجعة",
            "ينتظر مستندات إضافية من المتقدم",
            "تم إرسال الطلب للجهة المختصة",
            "قيد المراجعة النهائية",
            "تم الموافقة وجاري إصدار التأشيرة",
            "يحتاج مراجعة إضافية",
            "تم رفض الطلب لنقص المستندات",
            "معاملة عاجلة تحتاج متابعة فورية",
            "تم تحويل الطلب للقسم المختص",
            "جاري التنسيق مع الجهات الخارجية"
        ]
        
        priorities = ['low', 'medium', 'high', 'urgent']
        
        print("إضافة معاملات تجريبية...")
        
        added_count = 0
        
        for i in range(15):  # إضافة 15 معاملة تجريبية
            try:
                # إنشاء رقم وارد فريد
                incoming_no = f"2024/{str(i+100).zfill(3)}"
                
                # التحقق من عدم وجود الرقم
                existing = transaction_repo.find_by_incoming_number(incoming_no)
                if existing:
                    print(f"⚠️ رقم الوارد {incoming_no} موجود بالفعل، تخطي...")
                    continue
                
                # تاريخ عشوائي في آخر 3 أشهر
                days_ago = random.randint(1, 90)
                incoming_date = (date.today() - timedelta(days=days_ago)).strftime('%Y-%m-%d')
                
                # تاريخ استحقاق عشوائي
                due_days = random.randint(7, 30)
                due_date = (date.today() + timedelta(days=due_days)).strftime('%Y-%m-%d')
                
                # بيانات المعاملة
                transaction_data = {
                    'head_incoming_no': incoming_no,
                    'head_incoming_date': incoming_date,
                    'subject': random.choice(sample_subjects),
                    'researcher_notes': random.choice(sample_notes),
                    'priority': random.choice(priorities),
                    'due_date': due_date,
                    'user_id': 1,  # مدخل البيانات الافتراضي
                    'created_by': 1
                }
                
                # إضافة معرفات عشوائية للجداول المساعدة
                if lookup_data.get('visa_types'):
                    transaction_data['visa_type_id'] = random.choice(lookup_data['visa_types'])['id']
                
                if lookup_data.get('received_from_sources'):
                    transaction_data['received_from_id'] = random.choice(lookup_data['received_from_sources'])['id']
                
                if lookup_data.get('request_statuses'):
                    transaction_data['request_status_id'] = random.choice(lookup_data['request_statuses'])['id']
                
                if lookup_data.get('actions_taken'):
                    transaction_data['action_taken_id'] = random.choice(lookup_data['actions_taken'])['id']
                
                # إسناد باحث عشوائي
                researchers = [u for u in users if u.get('role') == 'researcher']
                if researchers:
                    transaction_data['researcher_1_id'] = random.choice(researchers)['user_id']
                
                # إنشاء المعاملة
                transaction_id = transaction_repo.create_transaction(transaction_data)

                if transaction_id:
                    added_count += 1
                    print(f"✅ تم إضافة المعاملة {incoming_no}")
                else:
                    print(f"❌ فشل في إضافة المعاملة {incoming_no}")
                
            except Exception as e:
                print(f"❌ خطأ في إضافة المعاملة {i+1}: {str(e)}")
                import traceback
                traceback.print_exc()
                continue
        
        print(f"\n🎉 تم إضافة {added_count} معاملة تجريبية بنجاح!")
        
        if added_count > 0:
            print("\nيمكنك الآن:")
            print("1. تشغيل التطبيق ومشاهدة المعاملات الجديدة")
            print("2. اختبار البحث والتصفية")
            print("3. مشاهدة الإحصائيات المحدثة")
            print("4. استكشاف الرسوم البيانية")
        
        return added_count > 0
        
    except Exception as e:
        print(f"❌ خطأ عام في إضافة المعاملات التجريبية: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("إضافة معاملات تجريبية لنظام إدارة المعاملات")
    print("=" * 60)
    
    try:
        success = add_sample_transactions()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 تم إضافة المعاملات التجريبية بنجاح!")
            print("يمكنك الآن تشغيل التطبيق واختبار جميع الميزات")
            print(f"{'=' * 60}")
        else:
            print("\n⚠️ لم يتم إضافة أي معاملات تجريبية")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف العملية بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
