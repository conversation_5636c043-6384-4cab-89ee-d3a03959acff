# -*- coding: utf-8 -*-
"""
نافذة نموذج المعاملة
Transaction Form Dialog

نافذة إضافة وتعديل المعاملات
"""

from typing import Dict
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QTextEdit, QComboBox, QDateEdit,
                            QPushButton, QFrame, QMessageBox, QTabWidget,
                            QWidget, QScrollArea, QGroupBox, QCheckBox)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from models.transaction_model import Transaction
from database.lookup_repositories import LookupService
from database.user_repository import UserRepository
from utils.logger import Logger

class TransactionFormDialog(QDialog):
    """نافذة نموذج المعاملة"""
    
    # إشارات مخصصة
    transaction_saved = pyqtSignal(dict)
    
    def __init__(self, connection_manager, auth_service, transaction=None, parent=None):
        super().__init__(parent)
        
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.transaction = transaction
        self.is_edit_mode = transaction is not None
        self.logger = Logger(__name__)
        
        # الخدمات
        self.lookup_service = LookupService(connection_manager)
        self.user_repository = UserRepository(connection_manager)
        
        # بيانات الجداول المساعدة
        self.lookup_data = {}
        
        self.setup_ui()
        self.load_lookup_data()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_transaction_data()
        else:
            self.set_default_values()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل المعاملة" if self.is_edit_mode else "إضافة معاملة جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(800, 700)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # الويدجت الرئيسي
        form_widget = QWidget()
        scroll_area.setWidget(form_widget)
        
        # التخطيط النموذجي
        form_layout = QVBoxLayout(form_widget)
        form_layout.setSpacing(20)
        
        # إعداد الأقسام
        self.setup_basic_info_section(form_layout)
        self.setup_assignment_section(form_layout)
        self.setup_status_section(form_layout)
        self.setup_dates_section(form_layout)
        self.setup_notes_section(form_layout)
        
        main_layout.addWidget(scroll_area)
        
        # أزرار التحكم
        self.setup_buttons(main_layout)
    
    def setup_basic_info_section(self, layout):
        """إعداد قسم المعلومات الأساسية"""
        group_box = QGroupBox("المعلومات الأساسية")
        group_box.setStyleSheet(self.get_group_box_style())
        
        form_layout = QFormLayout(group_box)
        form_layout.setSpacing(10)
        
        # رقم الوارد
        self.incoming_no_edit = QLineEdit()
        self.incoming_no_edit.setStyleSheet(self.get_input_style())
        self.incoming_no_edit.setMaxLength(50)
        form_layout.addRow("رقم الوارد:", self.incoming_no_edit)
        
        # تاريخ الوارد
        self.incoming_date_edit = QDateEdit()
        self.incoming_date_edit.setStyleSheet(self.get_input_style())
        self.incoming_date_edit.setDate(QDate.currentDate())
        self.incoming_date_edit.setCalendarPopup(True)
        form_layout.addRow("تاريخ الوارد:", self.incoming_date_edit)
        
        # الموضوع
        self.subject_edit = QTextEdit()
        self.subject_edit.setStyleSheet(self.get_input_style())
        self.subject_edit.setMaximumHeight(100)
        form_layout.addRow("الموضوع:", self.subject_edit)
        
        # نوع التأشيرة
        self.visa_type_combo = QComboBox()
        self.visa_type_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("نوع التأشيرة:", self.visa_type_combo)
        
        # وارد من
        self.received_from_combo = QComboBox()
        self.received_from_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("وارد من:", self.received_from_combo)
        
        layout.addWidget(group_box)
    
    def setup_assignment_section(self, layout):
        """إعداد قسم الإسناد"""
        group_box = QGroupBox("الإسناد والمسؤولية")
        group_box.setStyleSheet(self.get_group_box_style())
        
        form_layout = QFormLayout(group_box)
        form_layout.setSpacing(10)
        
        # الباحث الأول
        self.researcher_1_combo = QComboBox()
        self.researcher_1_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("الباحث الأول:", self.researcher_1_combo)
        
        # الباحث الثاني
        self.researcher_2_combo = QComboBox()
        self.researcher_2_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("الباحث الثاني:", self.researcher_2_combo)
        
        # الأولوية
        self.priority_combo = QComboBox()
        self.priority_combo.setStyleSheet(self.get_input_style())
        priorities = [
            ("منخفض", "low"),
            ("متوسط", "medium"),
            ("عالي", "high"),
            ("عاجل", "urgent")
        ]
        for text, data in priorities:
            self.priority_combo.addItem(text, data)
        form_layout.addRow("الأولوية:", self.priority_combo)
        
        layout.addWidget(group_box)
    
    def setup_status_section(self, layout):
        """إعداد قسم الحالة والإجراءات"""
        group_box = QGroupBox("الحالة والإجراءات")
        group_box.setStyleSheet(self.get_group_box_style())
        
        form_layout = QFormLayout(group_box)
        form_layout.setSpacing(10)
        
        # حالة الطلب
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("حالة الطلب:", self.status_combo)
        
        # الإجراء المتخذ
        self.action_combo = QComboBox()
        self.action_combo.setStyleSheet(self.get_input_style())
        form_layout.addRow("الإجراء المتخذ:", self.action_combo)
        
        layout.addWidget(group_box)
    
    def setup_dates_section(self, layout):
        """إعداد قسم التواريخ"""
        group_box = QGroupBox("التواريخ المهمة")
        group_box.setStyleSheet(self.get_group_box_style())
        
        form_layout = QFormLayout(group_box)
        form_layout.setSpacing(10)
        
        # تاريخ الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setStyleSheet(self.get_input_style())
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setSpecialValueText("غير محدد")
        self.due_date_edit.setDate(QDate.currentDate().addDays(7))
        
        # خانة اختيار لتفعيل تاريخ الاستحقاق
        self.due_date_enabled = QCheckBox("تحديد تاريخ استحقاق")
        self.due_date_enabled.setChecked(True)
        
        due_date_layout = QHBoxLayout()
        due_date_layout.addWidget(self.due_date_enabled)
        due_date_layout.addWidget(self.due_date_edit)
        
        form_layout.addRow("تاريخ الاستحقاق:", due_date_layout)
        
        # تاريخ الإنجاز (للتعديل فقط)
        if self.is_edit_mode:
            self.completion_date_edit = QDateEdit()
            self.completion_date_edit.setStyleSheet(self.get_input_style())
            self.completion_date_edit.setCalendarPopup(True)
            self.completion_date_edit.setSpecialValueText("غير مكتمل")
            
            self.completion_date_enabled = QCheckBox("تحديد تاريخ الإنجاز")
            
            completion_date_layout = QHBoxLayout()
            completion_date_layout.addWidget(self.completion_date_enabled)
            completion_date_layout.addWidget(self.completion_date_edit)
            
            form_layout.addRow("تاريخ الإنجاز:", completion_date_layout)
        
        layout.addWidget(group_box)
    
    def setup_notes_section(self, layout):
        """إعداد قسم الملاحظات"""
        group_box = QGroupBox("الملاحظات")
        group_box.setStyleSheet(self.get_group_box_style())
        
        form_layout = QFormLayout(group_box)
        
        # ملاحظات الباحث
        self.notes_edit = QTextEdit()
        self.notes_edit.setStyleSheet(self.get_input_style())
        self.notes_edit.setMaximumHeight(120)
        self.notes_edit.setPlaceholderText("أدخل ملاحظات الباحث هنا...")
        form_layout.addRow("ملاحظات الباحث:", self.notes_edit)
        
        layout.addWidget(group_box)
    
    def setup_buttons(self, layout):
        """إعداد أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet(self.get_button_style("#28a745"))
        self.save_button.setMinimumHeight(40)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style("#6c757d"))
        self.cancel_button.setMinimumHeight(40)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.save_button)
        
        layout.addWidget(buttons_frame)
    
    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        self.save_button.clicked.connect(self.save_transaction)
        self.cancel_button.clicked.connect(self.reject)
        
        # ربط تفعيل/إلغاء تفعيل التواريخ
        self.due_date_enabled.toggled.connect(self.due_date_edit.setEnabled)
        
        if self.is_edit_mode and hasattr(self, 'completion_date_enabled'):
            self.completion_date_enabled.toggled.connect(self.completion_date_edit.setEnabled)
    
    def load_lookup_data(self):
        """تحميل بيانات الجداول المساعدة"""
        try:
            self.lookup_data = self.lookup_service.get_all_lookup_data()
            
            # تحميل أنواع التأشيرات
            self.visa_type_combo.addItem("-- اختر نوع التأشيرة --", None)
            for item in self.lookup_data['visa_types']:
                self.visa_type_combo.addItem(item['visa_type'], item['id'])
            
            # تحميل مصادر الورود
            self.received_from_combo.addItem("-- اختر جهة الورود --", None)
            for item in self.lookup_data['received_from_sources']:
                self.received_from_combo.addItem(item['received_from'], item['id'])
            
            # تحميل الإجراءات
            self.action_combo.addItem("-- اختر الإجراء --", None)
            for item in self.lookup_data['actions_taken']:
                self.action_combo.addItem(item['action_taken'], item['id'])
            
            # تحميل الحالات
            self.status_combo.addItem("-- اختر الحالة --", None)
            for item in self.lookup_data['request_statuses']:
                self.status_combo.addItem(item['request_status'], item['id'])
            
            # تحميل الباحثين
            researchers = self.user_repository.get_researchers()
            
            self.researcher_1_combo.addItem("-- اختر الباحث الأول --", None)
            self.researcher_2_combo.addItem("-- اختر الباحث الثاني --", None)
            
            for researcher in researchers:
                self.researcher_1_combo.addItem(researcher['full_name'], researcher['user_id'])
                self.researcher_2_combo.addItem(researcher['full_name'], researcher['user_id'])
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات الجداول المساعدة: {str(e)}")
            self.show_error("خطأ", "فشل في تحميل البيانات المساعدة")
    
    def set_default_values(self):
        """تعيين القيم الافتراضية"""
        try:
            # تعيين المستخدم الحالي كمدخل البيانات
            current_user = self.auth_service.get_current_user()
            if current_user:
                self.current_user_id = current_user['user_id']
            
            # تعيين الأولوية الافتراضية
            self.priority_combo.setCurrentText("متوسط")
            
            # تعيين الحالة الافتراضية
            for i in range(self.status_combo.count()):
                if self.status_combo.itemText(i) == "جديد":
                    self.status_combo.setCurrentIndex(i)
                    break
            
        except Exception as e:
            self.logger.error(f"خطأ في تعيين القيم الافتراضية: {str(e)}")
    
    def load_transaction_data(self):
        """تحميل بيانات المعاملة للتعديل"""
        try:
            if not self.transaction:
                return
            
            # تحميل البيانات الأساسية
            self.incoming_no_edit.setText(self.transaction.get('head_incoming_no', ''))
            
            # تحميل التاريخ
            if self.transaction.get('head_incoming_date'):
                date_obj = QDate.fromString(self.transaction['head_incoming_date'], 'yyyy-MM-dd')
                self.incoming_date_edit.setDate(date_obj)
            
            self.subject_edit.setPlainText(self.transaction.get('subject', ''))
            self.notes_edit.setPlainText(self.transaction.get('researcher_notes', ''))
            
            # تحميل القوائم المنسدلة
            self.set_combo_value(self.visa_type_combo, self.transaction.get('visa_type_id'))
            self.set_combo_value(self.received_from_combo, self.transaction.get('received_from_id'))
            self.set_combo_value(self.researcher_1_combo, self.transaction.get('researcher_1_id'))
            self.set_combo_value(self.researcher_2_combo, self.transaction.get('researcher_2_id'))
            self.set_combo_value(self.action_combo, self.transaction.get('action_taken_id'))
            self.set_combo_value(self.status_combo, self.transaction.get('request_status_id'))
            
            # تحميل الأولوية
            priority_map = {'low': 'منخفض', 'medium': 'متوسط', 'high': 'عالي', 'urgent': 'عاجل'}
            priority_text = priority_map.get(self.transaction.get('priority', 'medium'), 'متوسط')
            self.priority_combo.setCurrentText(priority_text)
            
            # تحميل التواريخ
            if self.transaction.get('due_date'):
                due_date = QDate.fromString(self.transaction['due_date'], 'yyyy-MM-dd')
                self.due_date_edit.setDate(due_date)
                self.due_date_enabled.setChecked(True)
            else:
                self.due_date_enabled.setChecked(False)
                self.due_date_edit.setEnabled(False)
            
            if self.is_edit_mode and hasattr(self, 'completion_date_edit'):
                if self.transaction.get('completion_date'):
                    completion_date = QDate.fromString(self.transaction['completion_date'], 'yyyy-MM-dd')
                    self.completion_date_edit.setDate(completion_date)
                    self.completion_date_enabled.setChecked(True)
                else:
                    self.completion_date_enabled.setChecked(False)
                    self.completion_date_edit.setEnabled(False)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل بيانات المعاملة: {str(e)}")
            self.show_error("خطأ", "فشل في تحميل بيانات المعاملة")
    
    def set_combo_value(self, combo: QComboBox, value):
        """تعيين قيمة القائمة المنسدلة"""
        if value is not None:
            for i in range(combo.count()):
                if combo.itemData(i) == value:
                    combo.setCurrentIndex(i)
                    break
    
    def save_transaction(self):
        """حفظ المعاملة"""
        try:
            # جمع البيانات
            transaction_data = self.collect_form_data()
            
            # التحقق من صحة البيانات
            transaction_model = Transaction.from_dict(transaction_data)
            validation_errors = transaction_model.validate()
            
            if validation_errors:
                error_message = "\n".join(validation_errors)
                self.show_error("خطأ في البيانات", error_message)
                return
            
            # إرسال إشارة الحفظ
            self.transaction_saved.emit(transaction_data)
            self.accept()
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ المعاملة: {str(e)}")
            self.show_error("خطأ", f"فشل في حفظ المعاملة:\n{str(e)}")
    
    def collect_form_data(self) -> Dict:
        """جمع بيانات النموذج"""
        data = {}
        
        # البيانات الأساسية
        data['head_incoming_no'] = self.incoming_no_edit.text().strip()
        data['head_incoming_date'] = self.incoming_date_edit.date().toString('yyyy-MM-dd')
        data['subject'] = self.subject_edit.toPlainText().strip()
        data['researcher_notes'] = self.notes_edit.toPlainText().strip()
        
        # المعرفات
        data['visa_type_id'] = self.visa_type_combo.currentData()
        data['received_from_id'] = self.received_from_combo.currentData()
        data['researcher_1_id'] = self.researcher_1_combo.currentData()
        data['researcher_2_id'] = self.researcher_2_combo.currentData()
        data['action_taken_id'] = self.action_combo.currentData()
        data['request_status_id'] = self.status_combo.currentData()
        
        # الأولوية
        priority_map = {'منخفض': 'low', 'متوسط': 'medium', 'عالي': 'high', 'عاجل': 'urgent'}
        data['priority'] = priority_map.get(self.priority_combo.currentText(), 'medium')
        
        # التواريخ
        if self.due_date_enabled.isChecked():
            data['due_date'] = self.due_date_edit.date().toString('yyyy-MM-dd')
        else:
            data['due_date'] = None
        
        if self.is_edit_mode and hasattr(self, 'completion_date_enabled'):
            if self.completion_date_enabled.isChecked():
                data['completion_date'] = self.completion_date_edit.date().toString('yyyy-MM-dd')
            else:
                data['completion_date'] = None
        
        # معلومات المستخدم
        current_user = self.auth_service.get_current_user()
        if current_user:
            if self.is_edit_mode:
                data['updated_by'] = current_user['user_id']
                data['id'] = self.transaction['id']
            else:
                data['user_id'] = current_user['user_id']
                data['created_by'] = current_user['user_id']
        
        return data
    
    def get_group_box_style(self) -> str:
        """نمط صندوق المجموعة"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """
    
    def get_input_style(self) -> str:
        """نمط حقول الإدخال"""
        return """
            QLineEdit, QTextEdit, QComboBox, QDateEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """
    
    def get_button_style(self, color: str) -> str:
        """نمط الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """
    
    def darken_color(self, color: str, factor: float = 0.9) -> str:
        """تغميق اللون"""
        color_map = {
            "#28a745": "#1e7e34" if factor == 0.9 else "#155724",
            "#6c757d": "#545b62" if factor == 0.9 else "#3d4142"
        }
        return color_map.get(color, color)
    
    def show_error(self, title: str, message: str):
        """عرض رسالة خطأ"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        msg_box.exec()
