# -*- coding: utf-8 -*-
"""
ويدجت البحث المتقدم
Advanced Search Widget

واجهة البحث المتقدم مع خيارات متعددة
"""

from typing import Dict, List, Optional
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QComboBox, QCheckBox, QDateEdit, QPushButton,
                            QGroupBox, QFrame, QLabel, QListWidget, QListWidgetItem,
                            QTabWidget, QScrollArea, QButtonGroup, QRadioButton,
                            QSpinBox, QSlider, QTextEdit, QSplitter)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from services.search_service import AdvancedSearchService, SearchCriteria, SearchResult
from database.lookup_repositories import LookupService
from database.user_repository import UserRepository
from utils.logger import Logger

class SearchFieldSelector(QWidget):
    """محدد حقول البحث"""
    
    fields_changed = pyqtSignal(list)
    
    def __init__(self, available_fields: Dict[str, str], parent=None):
        super().__init__(parent)
        
        self.available_fields = available_fields
        self.selected_fields = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # عنوان
        title_label = QLabel("حقول البحث:")
        title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(title_label)
        
        # قائمة الحقول
        self.fields_list = QListWidget()
        self.fields_list.setMaximumHeight(150)
        
        # إضافة الحقول
        for field_key, field_name in self.available_fields.items():
            item = QListWidgetItem(field_name)
            item.setData(Qt.ItemDataRole.UserRole, field_key)
            item.setCheckState(Qt.CheckState.Unchecked)
            self.fields_list.addItem(item)
        
        # ربط الأحداث
        self.fields_list.itemChanged.connect(self.on_field_changed)
        
        layout.addWidget(self.fields_list)
        
        # أزرار سريعة
        buttons_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("تحديد الكل")
        select_all_btn.clicked.connect(self.select_all)
        buttons_layout.addWidget(select_all_btn)
        
        clear_all_btn = QPushButton("إلغاء الكل")
        clear_all_btn.clicked.connect(self.clear_all)
        buttons_layout.addWidget(clear_all_btn)
        
        layout.addLayout(buttons_layout)
    
    def on_field_changed(self, item):
        """معالجة تغيير حقل"""
        field_key = item.data(Qt.ItemDataRole.UserRole)
        
        if item.checkState() == Qt.CheckState.Checked:
            if field_key not in self.selected_fields:
                self.selected_fields.append(field_key)
        else:
            if field_key in self.selected_fields:
                self.selected_fields.remove(field_key)
        
        self.fields_changed.emit(self.selected_fields)
    
    def select_all(self):
        """تحديد جميع الحقول"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            item.setCheckState(Qt.CheckState.Checked)
    
    def clear_all(self):
        """إلغاء تحديد جميع الحقول"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            item.setCheckState(Qt.CheckState.Unchecked)
    
    def get_selected_fields(self) -> List[str]:
        """الحصول على الحقول المحددة"""
        return self.selected_fields.copy()
    
    def set_selected_fields(self, fields: List[str]):
        """تعيين الحقول المحددة"""
        self.selected_fields = fields.copy()
        
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            field_key = item.data(Qt.ItemDataRole.UserRole)
            
            if field_key in fields:
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)

class FilterWidget(QWidget):
    """ويدجت التصفية"""
    
    filters_changed = pyqtSignal(dict)
    
    def __init__(self, connection_manager, parent=None):
        super().__init__(parent)
        
        self.connection_manager = connection_manager
        self.lookup_service = LookupService(connection_manager)
        self.user_repository = UserRepository(connection_manager)
        
        self.filters = {}
        self.filter_widgets = {}
        
        self.setup_ui()
        self.load_filter_options()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # مجموعة التصفية الأساسية
        basic_group = QGroupBox("التصفية الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        # حالة الطلب
        self.status_combo = QComboBox()
        self.status_combo.addItem("جميع الحالات", None)
        basic_layout.addRow("حالة الطلب:", self.status_combo)
        self.filter_widgets['request_status_id'] = self.status_combo
        
        # نوع التأشيرة
        self.visa_type_combo = QComboBox()
        self.visa_type_combo.addItem("جميع الأنواع", None)
        basic_layout.addRow("نوع التأشيرة:", self.visa_type_combo)
        self.filter_widgets['visa_type_id'] = self.visa_type_combo
        
        # الباحث
        self.researcher_combo = QComboBox()
        self.researcher_combo.addItem("جميع الباحثين", None)
        basic_layout.addRow("الباحث:", self.researcher_combo)
        self.filter_widgets['researcher_1_id'] = self.researcher_combo
        
        # الأولوية
        self.priority_combo = QComboBox()
        self.priority_combo.addItem("جميع الأولويات", None)
        priorities = [("منخفض", "low"), ("متوسط", "medium"), ("عالي", "high"), ("عاجل", "urgent")]
        for text, value in priorities:
            self.priority_combo.addItem(text, value)
        basic_layout.addRow("الأولوية:", self.priority_combo)
        self.filter_widgets['priority'] = self.priority_combo
        
        scroll_layout.addWidget(basic_group)
        
        # مجموعة التصفية المتقدمة
        advanced_group = QGroupBox("التصفية المتقدمة")
        advanced_layout = QFormLayout(advanced_group)
        
        # وارد من
        self.received_from_combo = QComboBox()
        self.received_from_combo.addItem("جميع المصادر", None)
        advanced_layout.addRow("وارد من:", self.received_from_combo)
        self.filter_widgets['received_from_id'] = self.received_from_combo
        
        # الإجراء المتخذ
        self.action_combo = QComboBox()
        self.action_combo.addItem("جميع الإجراءات", None)
        advanced_layout.addRow("الإجراء المتخذ:", self.action_combo)
        self.filter_widgets['action_taken_id'] = self.action_combo
        
        # مدخل البيانات
        self.data_entry_combo = QComboBox()
        self.data_entry_combo.addItem("جميع المدخلين", None)
        advanced_layout.addRow("مدخل البيانات:", self.data_entry_combo)
        self.filter_widgets['user_id'] = self.data_entry_combo
        
        scroll_layout.addWidget(advanced_group)
        
        # مجموعة الخيارات الخاصة
        special_group = QGroupBox("خيارات خاصة")
        special_layout = QVBoxLayout(special_group)
        
        # المعاملات المتأخرة فقط
        self.overdue_checkbox = QCheckBox("المعاملات المتأخرة فقط")
        special_layout.addWidget(self.overdue_checkbox)
        self.filter_widgets['overdue_only'] = self.overdue_checkbox
        
        # المعاملات المكتملة فقط
        self.completed_checkbox = QCheckBox("المعاملات المكتملة فقط")
        special_layout.addWidget(self.completed_checkbox)
        self.filter_widgets['completed_only'] = self.completed_checkbox
        
        # المعاملات الجديدة فقط
        self.new_checkbox = QCheckBox("المعاملات الجديدة فقط")
        special_layout.addWidget(self.new_checkbox)
        self.filter_widgets['new_only'] = self.new_checkbox
        
        scroll_layout.addWidget(special_group)
        
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        apply_btn = QPushButton("تطبيق التصفية")
        apply_btn.clicked.connect(self.apply_filters)
        buttons_layout.addWidget(apply_btn)
        
        clear_btn = QPushButton("مسح التصفية")
        clear_btn.clicked.connect(self.clear_filters)
        buttons_layout.addWidget(clear_btn)
        
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        self.setup_connections()
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        for widget in self.filter_widgets.values():
            if isinstance(widget, QComboBox):
                widget.currentTextChanged.connect(self.on_filter_changed)
            elif isinstance(widget, QCheckBox):
                widget.toggled.connect(self.on_filter_changed)
    
    def load_filter_options(self):
        """تحميل خيارات التصفية"""
        try:
            # تحميل بيانات الجداول المساعدة
            lookup_data = self.lookup_service.get_all_lookup_data()
            
            # حالات الطلبات
            for status in lookup_data.get('request_statuses', []):
                self.status_combo.addItem(status['request_status'], status['id'])
            
            # أنواع التأشيرات
            for visa_type in lookup_data.get('visa_types', []):
                self.visa_type_combo.addItem(visa_type['visa_type'], visa_type['id'])
            
            # مصادر الورود
            for source in lookup_data.get('received_from_sources', []):
                self.received_from_combo.addItem(source['received_from'], source['id'])
            
            # الإجراءات المتخذة
            for action in lookup_data.get('actions_taken', []):
                self.action_combo.addItem(action['action_taken'], action['id'])
            
            # الباحثين
            researchers = self.user_repository.get_researchers()
            for researcher in researchers:
                self.researcher_combo.addItem(researcher['full_name'], researcher['user_id'])
                self.data_entry_combo.addItem(researcher['full_name'], researcher['user_id'])
            
        except Exception as e:
            print(f"خطأ في تحميل خيارات التصفية: {str(e)}")
    
    def on_filter_changed(self):
        """معالجة تغيير التصفية"""
        self.collect_filters()
        self.filters_changed.emit(self.filters)
    
    def collect_filters(self):
        """جمع قيم التصفية"""
        self.filters = {}
        
        for filter_key, widget in self.filter_widgets.items():
            if isinstance(widget, QComboBox):
                value = widget.currentData()
                if value is not None:
                    self.filters[filter_key] = value
            elif isinstance(widget, QCheckBox):
                if widget.isChecked():
                    self.filters[filter_key] = True
    
    def apply_filters(self):
        """تطبيق التصفية"""
        self.collect_filters()
        self.filters_changed.emit(self.filters)
    
    def clear_filters(self):
        """مسح التصفية"""
        for widget in self.filter_widgets.values():
            if isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QCheckBox):
                widget.setChecked(False)
        
        self.filters = {}
        self.filters_changed.emit(self.filters)
    
    def get_filters(self) -> Dict:
        """الحصول على التصفية الحالية"""
        self.collect_filters()
        return self.filters.copy()
    
    def set_filters(self, filters: Dict):
        """تعيين التصفية"""
        self.filters = filters.copy()
        
        for filter_key, value in filters.items():
            if filter_key in self.filter_widgets:
                widget = self.filter_widgets[filter_key]
                
                if isinstance(widget, QComboBox):
                    for i in range(widget.count()):
                        if widget.itemData(i) == value:
                            widget.setCurrentIndex(i)
                            break
                elif isinstance(widget, QCheckBox):
                    widget.setChecked(bool(value))

class DateRangeWidget(QWidget):
    """ويدجت نطاق التاريخ"""
    
    date_range_changed = pyqtSignal(str, str, str)  # from, to, field
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # حقل التاريخ
        field_layout = QHBoxLayout()
        field_layout.addWidget(QLabel("حقل التاريخ:"))
        
        self.date_field_combo = QComboBox()
        self.date_field_combo.addItem("تاريخ الوارد", "head_incoming_date")
        self.date_field_combo.addItem("تاريخ الاستحقاق", "due_date")
        self.date_field_combo.addItem("تاريخ الإنجاز", "completion_date")
        self.date_field_combo.addItem("تاريخ الإنشاء", "created_at")
        self.date_field_combo.addItem("تاريخ التحديث", "updated_at")
        field_layout.addWidget(self.date_field_combo)
        
        layout.addLayout(field_layout)
        
        # نطاق التاريخ
        range_layout = QFormLayout()
        
        # من تاريخ
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        self.from_date.setCalendarPopup(True)
        self.from_date.setSpecialValueText("غير محدد")
        range_layout.addRow("من تاريخ:", self.from_date)
        
        # إلى تاريخ
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        self.to_date.setSpecialValueText("غير محدد")
        range_layout.addRow("إلى تاريخ:", self.to_date)
        
        layout.addLayout(range_layout)
        
        # خانات اختيار التفعيل
        enable_layout = QHBoxLayout()
        
        self.enable_from = QCheckBox("تفعيل من")
        self.enable_to = QCheckBox("تفعيل إلى")
        
        enable_layout.addWidget(self.enable_from)
        enable_layout.addWidget(self.enable_to)
        
        layout.addLayout(enable_layout)
        
        # ربط الأحداث
        self.enable_from.toggled.connect(self.from_date.setEnabled)
        self.enable_to.toggled.connect(self.to_date.setEnabled)
        
        self.from_date.dateChanged.connect(self.on_date_changed)
        self.to_date.dateChanged.connect(self.on_date_changed)
        self.date_field_combo.currentTextChanged.connect(self.on_date_changed)
        
        # تعطيل التواريخ افتراضياً
        self.from_date.setEnabled(False)
        self.to_date.setEnabled(False)
    
    def on_date_changed(self):
        """معالجة تغيير التاريخ"""
        from_date = self.from_date.date().toString('yyyy-MM-dd') if self.enable_from.isChecked() else ""
        to_date = self.to_date.date().toString('yyyy-MM-dd') if self.enable_to.isChecked() else ""
        field = self.date_field_combo.currentData()
        
        self.date_range_changed.emit(from_date, to_date, field)
    
    def get_date_range(self) -> tuple:
        """الحصول على نطاق التاريخ"""
        from_date = self.from_date.date().toString('yyyy-MM-dd') if self.enable_from.isChecked() else None
        to_date = self.to_date.date().toString('yyyy-MM-dd') if self.enable_to.isChecked() else None
        field = self.date_field_combo.currentData()
        
        return from_date, to_date, field
    
    def set_date_range(self, from_date: str, to_date: str, field: str):
        """تعيين نطاق التاريخ"""
        if from_date:
            self.from_date.setDate(QDate.fromString(from_date, 'yyyy-MM-dd'))
            self.enable_from.setChecked(True)
        else:
            self.enable_from.setChecked(False)
        
        if to_date:
            self.to_date.setDate(QDate.fromString(to_date, 'yyyy-MM-dd'))
            self.enable_to.setChecked(True)
        else:
            self.enable_to.setChecked(False)
        
        # تعيين حقل التاريخ
        for i in range(self.date_field_combo.count()):
            if self.date_field_combo.itemData(i) == field:
                self.date_field_combo.setCurrentIndex(i)
                break

class AdvancedSearchWidget(QWidget):
    """ويدجت البحث المتقدم"""
    
    search_requested = pyqtSignal(object)  # SearchCriteria
    
    def __init__(self, connection_manager, parent=None):
        super().__init__(parent)
        
        self.connection_manager = connection_manager
        self.search_service = AdvancedSearchService(connection_manager)
        self.logger = Logger(__name__)
        
        # معايير البحث الحالية
        self.current_criteria = SearchCriteria()
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # عنوان
        title_label = QLabel("البحث المتقدم")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tab_widget = QTabWidget()
        
        # تبويب البحث النصي
        self.setup_text_search_tab()
        
        # تبويب التصفية
        self.setup_filter_tab()
        
        # تبويب نطاق التاريخ
        self.setup_date_range_tab()
        
        # تبويب الترتيب والخيارات
        self.setup_options_tab()
        
        layout.addWidget(self.tab_widget)
        
        # أزرار التحكم
        self.setup_control_buttons(layout)
    
    def setup_text_search_tab(self):
        """إعداد تبويب البحث النصي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # حقل البحث
        search_group = QGroupBox("نص البحث")
        search_layout = QVBoxLayout(search_group)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("أدخل نص البحث...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        search_layout.addWidget(self.search_edit)
        
        # نمط البحث
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("نمط البحث:"))
        
        self.search_mode_combo = QComboBox()
        self.search_mode_combo.addItem("يحتوي على", "contains")
        self.search_mode_combo.addItem("يبدأ بـ", "starts_with")
        self.search_mode_combo.addItem("ينتهي بـ", "ends_with")
        self.search_mode_combo.addItem("مطابق تماماً", "exact")
        mode_layout.addWidget(self.search_mode_combo)
        
        # حساسية الأحرف
        self.case_sensitive_checkbox = QCheckBox("حساس للأحرف الكبيرة/الصغيرة")
        mode_layout.addWidget(self.case_sensitive_checkbox)
        
        search_layout.addLayout(mode_layout)
        layout.addWidget(search_group)
        
        # حقول البحث
        fields_group = QGroupBox("حقول البحث")
        fields_layout = QVBoxLayout(fields_group)
        
        self.field_selector = SearchFieldSelector(self.search_service.searchable_fields)
        fields_layout.addWidget(self.field_selector)
        
        layout.addWidget(fields_group)
        
        self.tab_widget.addTab(tab, "البحث النصي")
    
    def setup_filter_tab(self):
        """إعداد تبويب التصفية"""
        self.filter_widget = FilterWidget(self.connection_manager)
        self.tab_widget.addTab(self.filter_widget, "التصفية")
    
    def setup_date_range_tab(self):
        """إعداد تبويب نطاق التاريخ"""
        self.date_range_widget = DateRangeWidget()
        self.tab_widget.addTab(self.date_range_widget, "نطاق التاريخ")
    
    def setup_options_tab(self):
        """إعداد تبويب الخيارات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # الترتيب
        sort_group = QGroupBox("الترتيب")
        sort_layout = QFormLayout(sort_group)
        
        self.sort_field_combo = QComboBox()
        sort_fields = [
            ("تاريخ الإنشاء", "created_at"),
            ("تاريخ الوارد", "head_incoming_date"),
            ("رقم الوارد", "head_incoming_no"),
            ("الموضوع", "subject"),
            ("تاريخ الاستحقاق", "due_date"),
            ("تاريخ الإنجاز", "completion_date")
        ]
        for text, value in sort_fields:
            self.sort_field_combo.addItem(text, value)
        sort_layout.addRow("ترتيب حسب:", self.sort_field_combo)
        
        self.sort_direction_combo = QComboBox()
        self.sort_direction_combo.addItem("تنازلي (الأحدث أولاً)", "DESC")
        self.sort_direction_combo.addItem("تصاعدي (الأقدم أولاً)", "ASC")
        sort_layout.addRow("اتجاه الترتيب:", self.sort_direction_combo)
        
        layout.addWidget(sort_group)
        
        # خيارات العرض
        display_group = QGroupBox("خيارات العرض")
        display_layout = QFormLayout(display_group)
        
        self.limit_spinbox = QSpinBox()
        self.limit_spinbox.setRange(10, 1000)
        self.limit_spinbox.setValue(100)
        self.limit_spinbox.setSuffix(" نتيجة")
        display_layout.addRow("عدد النتائج:", self.limit_spinbox)
        
        layout.addWidget(display_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "الخيارات")
    
    def setup_control_buttons(self, layout):
        """إعداد أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # زر البحث
        self.search_button = QPushButton("🔍 بحث")
        self.search_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # زر مسح
        self.clear_button = QPushButton("🗑️ مسح")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        # زر حفظ الإعداد
        self.save_preset_button = QPushButton("💾 حفظ إعداد")
        self.save_preset_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addWidget(self.save_preset_button)
        buttons_layout.addWidget(self.search_button)
        
        layout.addWidget(buttons_frame)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_button.clicked.connect(self.perform_search)
        self.clear_button.clicked.connect(self.clear_search)
        self.save_preset_button.clicked.connect(self.save_search_preset)
        
        # ربط تغييرات المعايير
        self.field_selector.fields_changed.connect(self.on_criteria_changed)
        self.filter_widget.filters_changed.connect(self.on_criteria_changed)
        self.date_range_widget.date_range_changed.connect(self.on_criteria_changed)
    
    def on_criteria_changed(self):
        """معالجة تغيير معايير البحث"""
        self.collect_criteria()
    
    def collect_criteria(self):
        """جمع معايير البحث"""
        self.current_criteria = SearchCriteria()
        
        # البحث النصي
        self.current_criteria.text_query = self.search_edit.text().strip()
        self.current_criteria.search_fields = self.field_selector.get_selected_fields()
        self.current_criteria.search_mode = self.search_mode_combo.currentData()
        self.current_criteria.case_sensitive = self.case_sensitive_checkbox.isChecked()
        
        # التصفية
        self.current_criteria.filters = self.filter_widget.get_filters()
        
        # نطاق التاريخ
        date_from, date_to, date_field = self.date_range_widget.get_date_range()
        self.current_criteria.date_from = date_from
        self.current_criteria.date_to = date_to
        self.current_criteria.date_field = date_field
        
        # الترتيب والخيارات
        self.current_criteria.sort_field = self.sort_field_combo.currentData()
        self.current_criteria.sort_direction = self.sort_direction_combo.currentData()
        self.current_criteria.limit = self.limit_spinbox.value()
    
    def perform_search(self):
        """تنفيذ البحث"""
        self.collect_criteria()
        self.search_requested.emit(self.current_criteria)
    
    def clear_search(self):
        """مسح البحث"""
        # مسح البحث النصي
        self.search_edit.clear()
        self.search_mode_combo.setCurrentIndex(0)
        self.case_sensitive_checkbox.setChecked(False)
        self.field_selector.clear_all()
        
        # مسح التصفية
        self.filter_widget.clear_filters()
        
        # مسح نطاق التاريخ
        self.date_range_widget.enable_from.setChecked(False)
        self.date_range_widget.enable_to.setChecked(False)
        
        # إعادة تعيين الخيارات
        self.sort_field_combo.setCurrentIndex(0)
        self.sort_direction_combo.setCurrentIndex(0)
        self.limit_spinbox.setValue(100)
        
        # مسح المعايير
        self.current_criteria = SearchCriteria()
    
    def save_search_preset(self):
        """حفظ إعداد البحث"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(self, "معلومات", "ميزة حفظ الإعدادات قيد التطوير")
    
    def get_current_criteria(self) -> SearchCriteria:
        """الحصول على معايير البحث الحالية"""
        self.collect_criteria()
        return self.current_criteria

    def set_criteria(self, criteria: SearchCriteria):
        """تعيين معايير البحث"""
        # البحث النصي
        self.search_edit.setText(criteria.text_query)

        # نمط البحث
        for i in range(self.search_mode_combo.count()):
            if self.search_mode_combo.itemData(i) == criteria.search_mode:
                self.search_mode_combo.setCurrentIndex(i)
                break

        self.case_sensitive_checkbox.setChecked(criteria.case_sensitive)

        # حقول البحث
        self.field_selector.set_selected_fields(criteria.search_fields)

        # التصفية
        self.filter_widget.set_filters(criteria.filters)

        # نطاق التاريخ
        self.date_range_widget.set_date_range(
            criteria.date_from, criteria.date_to, criteria.date_field
        )

        # الترتيب
        for i in range(self.sort_field_combo.count()):
            if self.sort_field_combo.itemData(i) == criteria.sort_field:
                self.sort_field_combo.setCurrentIndex(i)
                break

        for i in range(self.sort_direction_combo.count()):
            if self.sort_direction_combo.itemData(i) == criteria.sort_direction:
                self.sort_direction_combo.setCurrentIndex(i)
                break

        # الخيارات
        if criteria.limit:
            self.limit_spinbox.setValue(criteria.limit)

        self.current_criteria = criteria
