# -*- coding: utf-8 -*-
"""
ويدجت قائمة المعاملات
Transactions List Widget

يعرض قائمة المعاملات مع إمكانيات البحث والتصفية والإدارة
"""

from typing import Dict
from datetime import datetime
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QHeaderView, QPushButton, QLineEdit,
                            QComboBox, QDateEdit, QLabel, QFrame, QMessageBox,
                            QMenu, QCheckBox, QGroupBox, QSplitter, QTextEdit)
from PyQt6.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QColor, QFont

from ui.base_widget import BaseWidget
from ui.transaction_form_dialog import TransactionFormDialog
from database.transaction_repository import TransactionRepository
from database.lookup_repositories import LookupService
from database.user_repository import UserRepository
from models.transaction_model import Transaction
from utils.logger import Logger

class TransactionTableWidget(QTableWidget):
    """جدول المعاملات المخصص"""
    
    # إشارات مخصصة
    transaction_selected = pyqtSignal(dict)
    transaction_double_clicked = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
        self.transactions_data = []
    
    def setup_table(self):
        """إعداد الجدول"""
        # تعيين الأعمدة
        headers = [
            "م", "رقم الوارد", "تاريخ الوارد", "الموضوع", "الحالة", 
            "الباحث الأول", "نوع التأشيرة", "الأولوية", "تاريخ الاستحقاق", "الإجراءات"
        ]
        
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # إعداد الجدول
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.setSortingEnabled(True)
        self.verticalHeader().setVisible(False)
        
        # تعيين عرض الأعمدة
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # م
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # رقم الوارد
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # تاريخ الوارد
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # الموضوع
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # الحالة
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الباحث
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # نوع التأشيرة
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # الأولوية
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)  # تاريخ الاستحقاق
        header.setSectionResizeMode(9, QHeaderView.ResizeMode.ResizeToContents)  # الإجراءات
        
        # ربط الأحداث
        self.itemSelectionChanged.connect(self.on_selection_changed)
        self.itemDoubleClicked.connect(self.on_double_click)
    
    def load_transactions(self, transactions: list):
        """تحميل المعاملات في الجدول"""
        self.transactions_data = transactions
        self.setRowCount(len(transactions))
        
        for row, transaction in enumerate(transactions):
            self.populate_row(row, transaction)
    
    def populate_row(self, row: int, transaction: dict):
        """ملء صف في الجدول"""
        # الرقم التسلسلي
        self.setItem(row, 0, QTableWidgetItem(str(row + 1)))
        
        # رقم الوارد
        incoming_no_item = QTableWidgetItem(str(transaction.get('head_incoming_no', '')))
        incoming_no_item.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.setItem(row, 1, incoming_no_item)
        
        # تاريخ الوارد
        self.setItem(row, 2, QTableWidgetItem(str(transaction.get('head_incoming_date', ''))))
        
        # الموضوع (مقصور)
        subject = str(transaction.get('subject', ''))
        if len(subject) > 50:
            subject = subject[:47] + "..."
        self.setItem(row, 3, QTableWidgetItem(subject))
        
        # الحالة مع اللون
        status_item = QTableWidgetItem(str(transaction.get('request_status', '')))
        status_color = transaction.get('status_color', '#000000')
        try:
            status_item.setBackground(QColor(status_color + "30"))  # شفافية 30%
            status_item.setForeground(QColor(status_color))
        except:
            pass
        status_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        self.setItem(row, 4, status_item)
        
        # الباحث الأول
        self.setItem(row, 5, QTableWidgetItem(str(transaction.get('researcher_1_name', ''))))
        
        # نوع التأشيرة
        self.setItem(row, 6, QTableWidgetItem(str(transaction.get('visa_type', ''))))
        
        # الأولوية مع اللون
        priority = transaction.get('priority', 'medium')
        priority_text = self.get_priority_text(priority)
        priority_item = QTableWidgetItem(priority_text)
        priority_item.setBackground(QColor(self.get_priority_color(priority) + "30"))
        priority_item.setForeground(QColor(self.get_priority_color(priority)))
        priority_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
        self.setItem(row, 7, priority_item)
        
        # تاريخ الاستحقاق
        due_date = transaction.get('due_date', '')
        due_date_item = QTableWidgetItem(str(due_date) if due_date else 'غير محدد')
        
        # تلوين المعاملات المتأخرة
        if due_date and not transaction.get('completion_date'):
            try:
                from datetime import datetime, date
                due_date_obj = datetime.strptime(due_date, '%Y-%m-%d').date()
                if due_date_obj < date.today():
                    due_date_item.setBackground(QColor("#dc3545"))
                    due_date_item.setForeground(QColor("white"))
                    due_date_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            except:
                pass
        
        self.setItem(row, 8, due_date_item)
        
        # الإجراءات (أزرار)
        actions_widget = self.create_actions_widget(transaction)
        self.setCellWidget(row, 9, actions_widget)
    
    def create_actions_widget(self, transaction: dict) -> QWidget:
        """إنشاء ويدجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(3)
        
        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل المعاملة")
        edit_btn.setMaximumSize(25, 25)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        edit_btn.clicked.connect(lambda: self.transaction_double_clicked.emit(transaction))
        layout.addWidget(edit_btn)
        
        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف المعاملة")
        delete_btn.setMaximumSize(25, 25)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        # يمكن إضافة وظيفة الحذف هنا
        layout.addWidget(delete_btn)
        
        return widget
    
    def get_priority_text(self, priority: str) -> str:
        """الحصول على نص الأولوية"""
        priority_map = {
            'low': 'منخفض',
            'medium': 'متوسط',
            'high': 'عالي',
            'urgent': 'عاجل'
        }
        return priority_map.get(priority, priority)
    
    def get_priority_color(self, priority: str) -> str:
        """الحصول على لون الأولوية"""
        color_map = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }
        return color_map.get(priority, '#6c757d')
    
    def on_selection_changed(self):
        """معالجة تغيير التحديد"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.transactions_data):
            self.transaction_selected.emit(self.transactions_data[current_row])
    
    def on_double_click(self, item):
        """معالجة النقر المزدوج"""
        row = item.row()
        if 0 <= row < len(self.transactions_data):
            self.transaction_double_clicked.emit(self.transactions_data[row])
    
    def get_selected_transaction(self) -> dict:
        """الحصول على المعاملة المحددة"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.transactions_data):
            return self.transactions_data[current_row]
        return {}

class TransactionsListWidget(BaseWidget):
    """ويدجت قائمة المعاملات"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        # تهيئة الاعتمادات أولاً لأن BaseWidget يستدعي setup_content مبكراً
        self.connection_manager = connection_manager
        self.auth_service = auth_service

        # المستودعات والخدمات
        self.transaction_repository = TransactionRepository(connection_manager)
        self.lookup_service = LookupService(connection_manager)
        self.user_repository = UserRepository(connection_manager)

        # البيانات
        self.current_transactions = []
        self.lookup_data = {}

        # الآن نسمح لـ BaseWidget ببناء الواجهة واستدعاء setup_content
        super().__init__("إدارة المعاملات", parent)
        
        # مؤقت البحث
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
    
    def setup_content(self):
        """إعداد المحتوى"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        self.setup_toolbar(main_layout)
        
        # منطقة البحث والتصفية
        self.setup_search_filter_area(main_layout)
        
        # الجدول
        self.setup_table(main_layout)
        
        # شريط المعلومات
        self.setup_info_bar(main_layout)
        
        self.content_layout.addLayout(main_layout)
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def setup_toolbar(self, layout):
        """إعداد شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.Shape.Box)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # زر إضافة معاملة جديدة
        self.add_button = QPushButton("➕ إضافة معاملة جديدة")
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)
        toolbar_layout.addWidget(self.add_button)
        
        toolbar_layout.addStretch()
        
        # زر تحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        toolbar_layout.addWidget(self.refresh_button)
        
        # زر تصدير
        self.export_button = QPushButton("📤 تصدير")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #117a8b;
            }
        """)
        toolbar_layout.addWidget(self.export_button)
        
        layout.addWidget(toolbar_frame)
    
    def setup_search_filter_area(self, layout):
        """إعداد منطقة البحث والتصفية"""
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.Shape.Box)
        search_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        
        search_layout = QVBoxLayout(search_frame)
        
        # صف البحث
        search_row = QHBoxLayout()
        
        # حقل البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في رقم الوارد، الموضوع، أو الملاحظات...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        
        search_row.addWidget(search_label)
        search_row.addWidget(self.search_edit, 1)
        
        # زر مسح البحث
        self.clear_search_button = QPushButton("مسح")
        self.clear_search_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        search_row.addWidget(self.clear_search_button)
        
        search_layout.addLayout(search_row)
        
        # صف التصفية
        filter_row = QHBoxLayout()
        
        # تصفية حسب الحالة
        filter_row.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.setStyleSheet("""
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                min-width: 120px;
            }
        """)
        filter_row.addWidget(self.status_filter)
        
        # تصفية حسب الباحث
        filter_row.addWidget(QLabel("الباحث:"))
        self.researcher_filter = QComboBox()
        self.researcher_filter.setStyleSheet("""
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                min-width: 150px;
            }
        """)
        filter_row.addWidget(self.researcher_filter)
        
        # تصفية حسب الأولوية
        filter_row.addWidget(QLabel("الأولوية:"))
        self.priority_filter = QComboBox()
        self.priority_filter.setStyleSheet("""
            QComboBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                min-width: 100px;
            }
        """)
        filter_row.addWidget(self.priority_filter)
        
        filter_row.addStretch()
        
        # خانة اختيار المعاملات المتأخرة فقط
        self.overdue_only_checkbox = QCheckBox("المتأخرة فقط")
        self.overdue_only_checkbox.setStyleSheet("font-weight: bold; color: #dc3545;")
        filter_row.addWidget(self.overdue_only_checkbox)
        
        search_layout.addLayout(filter_row)
        
        layout.addWidget(search_frame)
    
    def setup_table(self, layout):
        """إعداد الجدول"""
        # إنشاء الجدول
        self.table = TransactionTableWidget()
        layout.addWidget(self.table)
        
        # ربط الأحداث
        self.table.transaction_selected.connect(self.on_transaction_selected)
        self.table.transaction_double_clicked.connect(self.edit_transaction)
    
    def setup_info_bar(self, layout):
        """إعداد شريط المعلومات"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.Shape.Box)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #e9ecef;
                border: 1px solid #ced4da;
                border-radius: 3px;
                padding: 8px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        
        # عدد المعاملات
        self.count_label = QLabel("عدد المعاملات: 0")
        self.count_label.setStyleSheet("font-weight: bold;")
        info_layout.addWidget(self.count_label)
        
        info_layout.addStretch()
        
        # معلومات إضافية
        self.info_label = QLabel("")
        info_layout.addWidget(self.info_label)
        
        layout.addWidget(info_frame)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل بيانات الجداول المساعدة
            self.lookup_data = self.lookup_service.get_all_lookup_data()
            
            # تحميل قوائم التصفية
            self.load_filter_options()
            
            # تحميل المعاملات
            self.load_transactions()
            
            # ربط أحداث البحث والتصفية
            self.setup_search_connections()
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {str(e)}")
    
    def load_filter_options(self):
        """تحميل خيارات التصفية"""
        try:
            # تحميل حالات الطلبات
            self.status_filter.addItem("جميع الحالات", None)
            for status in self.lookup_data.get('request_statuses', []):
                self.status_filter.addItem(status['request_status'], status['id'])
            
            # تحميل الباحثين
            self.researcher_filter.addItem("جميع الباحثين", None)
            researchers = self.user_repository.get_researchers()
            for researcher in researchers:
                self.researcher_filter.addItem(researcher['full_name'], researcher['user_id'])
            
            # تحميل الأولويات
            self.priority_filter.addItem("جميع الأولويات", None)
            priorities = [
                ("منخفض", "low"),
                ("متوسط", "medium"),
                ("عالي", "high"),
                ("عاجل", "urgent")
            ]
            for text, value in priorities:
                self.priority_filter.addItem(text, value)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل خيارات التصفية: {str(e)}")
    
    def setup_search_connections(self):
        """إعداد اتصالات البحث والتصفية"""
        # البحث النصي
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        self.clear_search_button.clicked.connect(self.clear_search)
        
        # التصفية
        self.status_filter.currentTextChanged.connect(self.apply_filters)
        self.researcher_filter.currentTextChanged.connect(self.apply_filters)
        self.priority_filter.currentTextChanged.connect(self.apply_filters)
        self.overdue_only_checkbox.toggled.connect(self.apply_filters)
        
        # أزرار الأدوات
        self.add_button.clicked.connect(self.add_transaction)
        self.refresh_button.clicked.connect(self.refresh)
        self.export_button.clicked.connect(self.export_transactions)
    
    def load_transactions(self):
        """تحميل المعاملات"""
        try:
            self.show_loading("جاري تحميل المعاملات...")
            
            # الحصول على المعاملات مع التفاصيل
            transactions = self.transaction_repository.get_transactions_with_details(limit=1000)
            
            self.current_transactions = transactions
            self.table.load_transactions(transactions)
            
            # تحديث شريط المعلومات
            self.update_info_bar()
            
            self.hide_loading()
            
        except Exception as e:
            self.hide_loading()
            self.logger.error(f"خطأ في تحميل المعاملات: {str(e)}")
            self.show_error(f"فشل في تحميل المعاملات: {str(e)}")
    
    def on_search_text_changed(self):
        """معالجة تغيير نص البحث"""
        # تأخير البحث لتجنب البحث المستمر أثناء الكتابة
        self.search_timer.stop()
        self.search_timer.start(500)  # 500 مللي ثانية
    
    def perform_search(self):
        """تنفيذ البحث"""
        search_term = self.search_edit.text().strip()
        
        if search_term:
            try:
                self.show_loading("جاري البحث...")
                
                # البحث في المعاملات
                results = self.transaction_repository.search_transactions(search_term)
                
                self.current_transactions = results
                self.table.load_transactions(results)
                self.update_info_bar()
                
                self.hide_loading()
                
            except Exception as e:
                self.hide_loading()
                self.logger.error(f"خطأ في البحث: {str(e)}")
                self.show_error(f"فشل في البحث: {str(e)}")
        else:
            self.apply_filters()
    
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.apply_filters()
    
    def apply_filters(self):
        """تطبيق التصفية"""
        try:
            self.show_loading("جاري تطبيق التصفية...")
            
            # جمع معايير التصفية
            filters = {}
            
            if self.status_filter.currentData():
                filters['status_id'] = self.status_filter.currentData()
            
            if self.researcher_filter.currentData():
                filters['researcher_id'] = self.researcher_filter.currentData()
            
            if self.priority_filter.currentData():
                filters['priority'] = self.priority_filter.currentData()
            
            if self.overdue_only_checkbox.isChecked():
                filters['overdue_only'] = True
            
            # تطبيق التصفية
            if filters:
                results = self.transaction_repository.filter_transactions(filters)
            else:
                results = self.transaction_repository.get_transactions_with_details(limit=1000)
            
            self.current_transactions = results
            self.table.load_transactions(results)
            self.update_info_bar()
            
            self.hide_loading()
            
        except Exception as e:
            self.hide_loading()
            self.logger.error(f"خطأ في تطبيق التصفية: {str(e)}")
            self.show_error(f"فشل في تطبيق التصفية: {str(e)}")
    
    def update_info_bar(self):
        """تحديث شريط المعلومات"""
        count = len(self.current_transactions)
        self.count_label.setText(f"عدد المعاملات: {count}")
        
        # إحصائيات سريعة
        if count > 0:
            overdue_count = sum(1 for t in self.current_transactions 
                              if t.get('due_date') and not t.get('completion_date') 
                              and t.get('due_date') < str(datetime.now().date()))
            
            if overdue_count > 0:
                self.info_label.setText(f"متأخرة: {overdue_count}")
                self.info_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            else:
                self.info_label.setText("لا توجد معاملات متأخرة")
                self.info_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.info_label.setText("")
    
    def add_transaction(self):
        """إضافة معاملة جديدة"""
        try:
            dialog = TransactionFormDialog(
                self.connection_manager, 
                self.auth_service, 
                parent=self
            )
            
            dialog.transaction_saved.connect(self.on_transaction_saved)
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة إضافة المعاملة: {str(e)}")
            self.show_error(f"فشل في فتح نافذة الإضافة: {str(e)}")
    
    def edit_transaction(self, transaction: dict):
        """تعديل معاملة"""
        try:
            dialog = TransactionFormDialog(
                self.connection_manager, 
                self.auth_service, 
                transaction=transaction,
                parent=self
            )
            
            dialog.transaction_saved.connect(self.on_transaction_saved)
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"خطأ في فتح نافذة تعديل المعاملة: {str(e)}")
            self.show_error(f"فشل في فتح نافذة التعديل: {str(e)}")
    
    def on_transaction_saved(self, transaction_data: dict):
        """معالجة حفظ المعاملة"""
        try:
            if transaction_data.get('id'):
                # تحديث معاملة موجودة
                success = self.transaction_repository.update(
                    transaction_data['id'], 
                    transaction_data
                )
                if success:
                    self.show_success("تم تحديث المعاملة بنجاح")
                else:
                    self.show_error("فشل في تحديث المعاملة")
            else:
                # إضافة معاملة جديدة
                transaction_id = self.transaction_repository.create_transaction(transaction_data)
                if transaction_id:
                    self.show_success("تم إضافة المعاملة بنجاح")
                else:
                    self.show_error("فشل في إضافة المعاملة")
            
            # تحديث القائمة
            self.refresh()
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ المعاملة: {str(e)}")
            self.show_error(f"فشل في حفظ المعاملة: {str(e)}")
    
    def on_transaction_selected(self, transaction: dict):
        """معالجة تحديد معاملة"""
        # يمكن إضافة منطق إضافي هنا
        pass
    
    def export_transactions(self):
        """تصدير المعاملات"""
        # سيتم تطوير هذه الوظيفة لاحقاً
        self.show_info("ميزة التصدير قيد التطوير")
    
    def refresh(self):
        """تحديث القائمة"""
        self.load_transactions()
    
    def show_success(self, message: str):
        """عرض رسالة نجاح"""
        QMessageBox.information(self, "نجح", message)
    
    def show_info(self, message: str):
        """عرض رسالة معلومات"""
        QMessageBox.information(self, "معلومات", message)
