#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات
"""

import sys
import os
import sqlite3

# إضافة مسار src
sys.path.insert(0, 'src')

def test_direct_connection():
    """اختبار الاتصال المباشر بقاعدة البيانات"""
    
    print("اختبار الاتصال المباشر بقاعدة البيانات...")
    
    try:
        # اختبار قاعدة البيانات الموجودة
        db_path = "data/iots_local.db"
        
        if not os.path.exists(db_path):
            print(f"   ❌ قاعدة البيانات غير موجودة: {db_path}")
            return False
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # اختبار استعلام بسيط
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"   ✅ تم الاتصال بقاعدة البيانات")
        print(f"   📋 عدد الجداول: {len(tables)}")
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"      - {table[0]}: {count} سجل")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager():
    """اختبار مدير التكوين"""
    
    print("\nاختبار مدير التكوين...")
    
    try:
        from utils.config_manager import ConfigManager
        
        config = ConfigManager()
        
        print(f"   ✅ تم تحميل التكوين")
        print(f"   📝 mysql_host: {config.mysql_host}")
        print(f"   📝 sqlite_database: {config.sqlite_database}")
        
        return config
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير التكوين: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_logger():
    """اختبار نظام السجلات"""
    
    print("\nاختبار نظام السجلات...")
    
    try:
        from utils.logger import Logger
        
        logger = Logger("test")
        logger.info("اختبار السجلات")
        
        print("   ✅ نظام السجلات يعمل")
        
        return logger
        
    except Exception as e:
        print(f"   ❌ خطأ في نظام السجلات: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_simple_connection_manager():
    """اختبار مدير الاتصال المبسط"""
    
    print("\nاختبار مدير الاتصال المبسط...")
    
    try:
        config = test_config_manager()
        if not config:
            return False
        
        logger = test_logger()
        if not logger:
            return False
        
        # اختبار إنشاء مدير الاتصال خطوة بخطوة
        print("   📝 إنشاء مدير الاتصال...")
        
        # استيراد الفئة
        from database.connection_manager import ConnectionManager
        print("   ✅ تم استيراد ConnectionManager")
        
        # إنشاء الكائن
        conn_manager = ConnectionManager(config)
        print("   ✅ تم إنشاء ConnectionManager")
        
        # اختبار الاتصال
        if conn_manager.test_connection():
            print("   ✅ اختبار الاتصال نجح")
        else:
            print("   ⚠️ اختبار الاتصال فشل")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في مدير الاتصال: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("اختبار الاتصال بقاعدة البيانات")
    print("=" * 50)
    
    tests = [
        ("الاتصال المباشر", test_direct_connection),
        ("مدير الاتصال المبسط", test_simple_connection_manager),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"اختبار: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 50}")
    print(f"نتائج الاختبار: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
