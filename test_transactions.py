#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المعاملات
Transaction Management System Test

يختبر جميع وظائف إدارة المعاملات
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة مسار src
sys.path.insert(0, 'src')

def test_transaction_repository():
    """اختبار مستودع المعاملات"""
    
    print("اختبار مستودع المعاملات...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from database.transaction_repository import TransactionRepository
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        transaction_repo = TransactionRepository(conn_manager)
        
        # اختبار الحصول على المعاملات مع التفاصيل
        transactions = transaction_repo.get_transactions_with_details(limit=5)
        print(f"✅ تم الحصول على {len(transactions)} معاملة مع التفاصيل")
        
        if transactions:
            # عرض أول معاملة كمثال
            first_transaction = transactions[0]
            print(f"   مثال: {first_transaction.get('head_incoming_no')} - {first_transaction.get('subject', '')[:30]}...")
        
        # اختبار البحث
        search_results = transaction_repo.search_transactions("تأشيرة")
        print(f"✅ البحث عن 'تأشيرة': {len(search_results)} نتيجة")
        
        # اختبار التصفية
        filters = {'priority': 'medium'}
        filtered_results = transaction_repo.filter_transactions(filters)
        print(f"✅ التصفية حسب الأولوية المتوسطة: {len(filtered_results)} نتيجة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مستودع المعاملات: {str(e)}")
        return False

def test_lookup_service():
    """اختبار خدمة الجداول المساعدة"""
    
    print("\nاختبار خدمة الجداول المساعدة...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from database.lookup_repositories import LookupService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        lookup_service = LookupService(conn_manager)
        
        # اختبار الحصول على جميع البيانات
        all_data = lookup_service.get_all_lookup_data()
        
        for table_name, data in all_data.items():
            print(f"✅ {table_name}: {len(data)} عنصر")
        
        # اختبار بيانات القوائم المنسدلة
        visa_types = lookup_service.get_combo_data('visa_types')
        print(f"✅ أنواع التأشيرات للقوائم: {len(visa_types)} نوع")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة الجداول المساعدة: {str(e)}")
        return False

def test_transaction_model():
    """اختبار نموذج المعاملة"""
    
    print("\nاختبار نموذج المعاملة...")
    
    try:
        from models.transaction_model import Transaction
        
        # إنشاء معاملة تجريبية
        transaction_data = {
            'head_incoming_no': 'TEST/001',
            'head_incoming_date': '2024-01-15',
            'subject': 'معاملة اختبار',
            'user_id': 1,
            'priority': 'medium'
        }
        
        transaction = Transaction.from_dict(transaction_data)
        print("✅ تم إنشاء نموذج المعاملة")
        
        # اختبار التحقق من صحة البيانات
        validation_errors = transaction.validate()
        if not validation_errors:
            print("✅ البيانات صحيحة")
        else:
            print(f"⚠️ أخطاء التحقق: {validation_errors}")
        
        # اختبار التحويل إلى قاموس
        transaction_dict = transaction.to_dict()
        print("✅ تم تحويل النموذج إلى قاموس")
        
        # اختبار الطرق المساعدة
        days_since = transaction.days_since_received()
        print(f"✅ الأيام منذ الاستلام: {days_since}")
        
        priority_display = transaction.get_priority_display()
        print(f"✅ عرض الأولوية: {priority_display}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج المعاملة: {str(e)}")
        return False

def test_statistics_service():
    """اختبار خدمة الإحصائيات"""
    
    print("\nاختبار خدمة الإحصائيات...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.statistics_service import StatisticsService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        stats_service = StatisticsService(conn_manager)
        
        # اختبار الإحصائيات العامة
        overview_stats = stats_service.get_overview_statistics()
        print(f"✅ الإحصائيات العامة: {len(overview_stats)} مؤشر")
        
        if overview_stats:
            print(f"   إجمالي المعاملات: {overview_stats.get('total_transactions', 0)}")
            print(f"   معدل الإنجاز: {overview_stats.get('completion_rate', 0)}%")
        
        # اختبار الإحصائيات الشهرية
        monthly_stats = stats_service.get_monthly_statistics(2024)
        print(f"✅ الإحصائيات الشهرية: {len(monthly_stats)} شهر")
        
        # اختبار إحصائيات الباحثين
        researcher_stats = stats_service.get_researcher_statistics()
        print(f"✅ إحصائيات الباحثين: {len(researcher_stats)} باحث")
        
        # اختبار إحصائيات أنواع التأشيرات
        visa_stats = stats_service.get_visa_type_statistics()
        print(f"✅ إحصائيات أنواع التأشيرات: {len(visa_stats)} نوع")
        
        # اختبار النشاطات الحديثة
        recent_activities = stats_service.get_recent_activity(5)
        print(f"✅ النشاطات الحديثة: {len(recent_activities)} نشاط")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة الإحصائيات: {str(e)}")
        return False

def test_crud_operations():
    """اختبار عمليات CRUD"""
    
    print("\nاختبار عمليات CRUD...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from database.transaction_repository import TransactionRepository
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        transaction_repo = TransactionRepository(conn_manager)
        
        # اختبار إنشاء معاملة جديدة
        test_transaction = {
            'head_incoming_no': f'TEST/{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'head_incoming_date': date.today().strftime('%Y-%m-%d'),
            'subject': 'معاملة اختبار CRUD',
            'researcher_notes': 'ملاحظات اختبار',
            'user_id': 1,
            'visa_type_id': 1,
            'received_from_id': 1,
            'request_status_id': 1,
            'priority': 'medium',
            'due_date': (date.today() + timedelta(days=7)).strftime('%Y-%m-%d'),
            'created_by': 1
        }
        
        # إنشاء المعاملة
        transaction_id = transaction_repo.create_transaction(test_transaction)
        if transaction_id:
            print(f"✅ تم إنشاء معاملة جديدة بالمعرف: {transaction_id}")
            
            # اختبار القراءة
            created_transaction = transaction_repo.find_by_id(transaction_id)
            if created_transaction:
                print("✅ تم قراءة المعاملة المُنشأة")
                
                # اختبار التحديث
                update_data = {
                    'researcher_notes': 'ملاحظات محدثة',
                    'priority': 'high',
                    'updated_by': 1
                }
                
                update_success = transaction_repo.update(transaction_id, update_data)
                if update_success:
                    print("✅ تم تحديث المعاملة")
                    
                    # اختبار تحديث الحالة
                    status_update = transaction_repo.update_transaction_status(
                        transaction_id, 2, action_id=1, 
                        notes="تم تحديث الحالة", updated_by=1
                    )
                    if status_update:
                        print("✅ تم تحديث حالة المعاملة")
                    
                    # اختبار إسناد باحث
                    assign_success = transaction_repo.assign_researcher(
                        transaction_id, 1, position=1, assigned_by=1
                    )
                    if assign_success:
                        print("✅ تم إسناد المعاملة للباحث")
                    
                    # اختبار الحذف
                    delete_success = transaction_repo.delete(transaction_id)
                    if delete_success:
                        print("✅ تم حذف المعاملة الاختبارية")
                    else:
                        print("⚠️ فشل في حذف المعاملة الاختبارية")
                else:
                    print("❌ فشل في تحديث المعاملة")
            else:
                print("❌ فشل في قراءة المعاملة المُنشأة")
        else:
            print("❌ فشل في إنشاء المعاملة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات CRUD: {str(e)}")
        return False

def run_transaction_tests():
    """تشغيل جميع اختبارات المعاملات"""
    
    print("=" * 60)
    print("اختبار نظام إدارة المعاملات")
    print("=" * 60)
    
    tests = [
        ("مستودع المعاملات", test_transaction_repository),
        ("خدمة الجداول المساعدة", test_lookup_service),
        ("نموذج المعاملة", test_transaction_model),
        ("خدمة الإحصائيات", test_statistics_service),
        ("عمليات CRUD", test_crud_operations),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج اختبار إدارة المعاملات: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات إدارة المعاملات نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ إنشاء معاملات جديدة")
        print("- ✅ عرض وتصفح المعاملات")
        print("- ✅ تعديل المعاملات الموجودة")
        print("- ✅ البحث في المعاملات")
        print("- ✅ تصفية المعاملات")
        print("- ✅ إحصائيات متقدمة")
        print("- ✅ رسوم بيانية")
        return True
    else:
        print("⚠️ بعض اختبارات إدارة المعاملات فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_transaction_tests()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 نظام إدارة المعاملات جاهز للاستخدام!")
            print("\nلتجربة النظام:")
            print("1. شغل التطبيق: python main.py")
            print("2. سجل الدخول: admin / admin")
            print("3. انتقل إلى 'المعاملات' > 'عرض المعاملات'")
            print("4. جرب إضافة معاملة جديدة")
            print("5. استكشف الرسوم البيانية والإحصائيات")
            print(f"{'=' * 60}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
