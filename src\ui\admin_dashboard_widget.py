# -*- coding: utf-8 -*-
"""
لوحة تحكم المدير
Admin Dashboard Widget

لوحة التحكم الرئيسية للمدير مع جميع الأدوات الإدارية
"""

from typing import Dict, List, Optional
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QLabel, QFrame, QPushButton, QGroupBox, QGridLayout,
                            QProgressBar, QTextEdit, QSplitter, QScrollArea)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPixmap

from ui.base_widget import BaseWidget
from ui.user_management_widget import UserManagementWidget
from ui.lookup_management_widget import LookupManagementWidget
from services.admin_service import AdminService
from utils.logger import Logger

class SystemStatsWidget(QWidget):
    """ويدجت إحصائيات النظام"""
    
    def __init__(self, admin_service: AdminService, parent=None):
        super().__init__(parent)
        
        self.admin_service = admin_service
        self.setup_ui()
        self.load_stats()
        
        # مؤقت التحديث التلقائي
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_stats)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("📊 إحصائيات النظام")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # بطاقات الإحصائيات
        self.setup_stats_cards(scroll_layout)
        
        # الرسوم البيانية المصغرة
        self.setup_mini_charts(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث الإحصائيات")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.load_stats)
        layout.addWidget(refresh_btn)
    
    def setup_stats_cards(self, layout):
        """إعداد بطاقات الإحصائيات"""
        cards_frame = QFrame()
        cards_layout = QGridLayout(cards_frame)
        cards_layout.setSpacing(15)
        
        # بطاقات الإحصائيات
        self.stats_cards = {}
        
        cards_config = [
            ('users', 'المستخدمين', '👥', '#3498db'),
            ('transactions', 'المعاملات', '📄', '#27ae60'),
            ('completed', 'المكتملة', '✅', '#f39c12'),
            ('overdue', 'المتأخرة', '⏰', '#e74c3c')
        ]
        
        for i, (key, title, icon, color) in enumerate(cards_config):
            card = self.create_stats_card(title, icon, color, "0")
            self.stats_cards[key] = card
            
            row = i // 2
            col = i % 2
            cards_layout.addWidget(card, row, col)
        
        layout.addWidget(cards_frame)
    
    def create_stats_card(self, title: str, icon: str, color: str, value: str) -> QFrame:
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid {color};
            }}
            QFrame:hover {{
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(10)
        
        # الصف العلوي - الأيقونة والعنوان
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 24))
        icon_label.setStyleSheet(f"color: {color};")
        top_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #6c757d;")
        top_layout.addWidget(title_label)
        
        top_layout.addStretch()
        layout.addLayout(top_layout)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        # حفظ مرجع لتسمية القيمة
        card.value_label = value_label
        
        return card
    
    def setup_mini_charts(self, layout):
        """إعداد الرسوم البيانية المصغرة"""
        charts_group = QGroupBox("معلومات إضافية")
        charts_layout = QVBoxLayout(charts_group)
        
        # معلومات قاعدة البيانات
        self.db_info_text = QTextEdit()
        self.db_info_text.setReadOnly(True)
        self.db_info_text.setMaximumHeight(100)
        self.db_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        charts_layout.addWidget(self.db_info_text)
        
        # معلومات الجداول المساعدة
        self.lookup_info_text = QTextEdit()
        self.lookup_info_text.setReadOnly(True)
        self.lookup_info_text.setMaximumHeight(80)
        self.lookup_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            }
        """)
        charts_layout.addWidget(self.lookup_info_text)
        
        layout.addWidget(charts_group)
    
    def load_stats(self):
        """تحميل الإحصائيات"""
        try:
            stats = self.admin_service.get_system_statistics()
            
            # تحديث البطاقات
            self.stats_cards['users'].value_label.setText(str(stats.get('total_users', 0)))
            self.stats_cards['transactions'].value_label.setText(str(stats.get('total_transactions', 0)))
            self.stats_cards['completed'].value_label.setText(str(stats.get('completed_transactions', 0)))
            self.stats_cards['overdue'].value_label.setText(str(stats.get('overdue_transactions', 0)))
            
            # معلومات قاعدة البيانات
            db_info = f"""
معلومات قاعدة البيانات:
نوع قاعدة البيانات: {stats.get('database_type', 'غير محدد')}
المستخدمين النشطين: {stats.get('active_users', 0)}
المستخدمين غير النشطين: {stats.get('inactive_users', 0)}
معدل الإنجاز: {stats.get('completion_rate', 0):.1f}%
            """
            self.db_info_text.setText(db_info.strip())
            
            # معلومات الجداول المساعدة
            lookup_info = f"""
الجداول المساعدة:
أنواع التأشيرات: {stats.get('visa_types_count', 0)}
مصادر الورود: {stats.get('received_from_count', 0)}
الإجراءات المتخذة: {stats.get('actions_count', 0)}
حالات الطلبات: {stats.get('statuses_count', 0)}
            """
            self.lookup_info_text.setText(lookup_info.strip())
            
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")

class QuickActionsWidget(QWidget):
    """ويدجت الإجراءات السريعة"""
    
    # إشارات مخصصة
    action_requested = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # العنوان
        title_label = QLabel("⚡ الإجراءات السريعة")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # الأزرار
        buttons_config = [
            ('add_user', '👤 إضافة مستخدم جديد', '#27ae60'),
            ('backup_db', '💾 نسخ احتياطي لقاعدة البيانات', '#3498db'),
            ('system_logs', '📋 عرض سجلات النظام', '#f39c12'),
            ('export_data', '📤 تصدير البيانات', '#9b59b6'),
            ('system_settings', '⚙️ إعدادات النظام', '#34495e'),
            ('user_activity', '📊 نشاط المستخدمين', '#1abc9c')
        ]
        
        for action_id, text, color in buttons_config:
            button = QPushButton(text)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: left;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            button.clicked.connect(lambda checked, aid=action_id: self.action_requested.emit(aid))
            layout.addWidget(button)
        
        layout.addStretch()
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50",
            "#1abc9c": "#16a085"
        }
        return color_map.get(color, color)

class AdminDashboardWidget(BaseWidget):
    """لوحة تحكم المدير الرئيسية"""
    
    def __init__(self, connection_manager, auth_service, parent=None):
        # عيّن الاعتمادات أولاً
        self.connection_manager = connection_manager
        self.auth_service = auth_service
        self.admin_service = AdminService(connection_manager)

        # ثم بنِ الواجهة عبر BaseWidget (سيستدعي setup_content)
        super().__init__("لوحة تحكم المدير", parent)
    
    def setup_content(self):
        """إعداد المحتوى"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # التبويبات الرئيسية
        self.setup_main_tabs(layout)
        
        self.content_layout.addLayout(layout)
    
    def setup_title_bar(self):
        """إعداد شريط العنوان"""
        # لا نحتاج شريط عنوان منفصل لأن BaseWidget يوفر العنوان
        pass
    
    def setup_main_tabs(self, layout):
        """إعداد التبويبات الرئيسية"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: #f8f9fa;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                font-size: 13px;
            }
            QTabBar::tab:selected {
                background-color: #f8f9fa;
                border-bottom-color: #f8f9fa;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # تبويب النظرة العامة
        self.setup_overview_tab()
        
        # تبويب إدارة المستخدمين
        self.setup_users_tab()
        
        # تبويب إدارة الجداول المساعدة
        self.setup_lookup_tab()
        
        # تبويب الأدوات الإدارية
        self.setup_tools_tab()
        
        layout.addWidget(self.tabs)
    
    def setup_overview_tab(self):
        """إعداد تبويب النظرة العامة"""
        overview_widget = QWidget()
        overview_layout = QHBoxLayout(overview_widget)
        overview_layout.setContentsMargins(10, 10, 10, 10)
        overview_layout.setSpacing(15)
        
        # الجانب الأيسر - الإحصائيات
        stats_widget = SystemStatsWidget(self.admin_service)
        stats_widget.setMaximumWidth(400)
        overview_layout.addWidget(stats_widget)
        
        # الجانب الأيمن - الإجراءات السريعة
        actions_widget = QuickActionsWidget()
        actions_widget.action_requested.connect(self.handle_quick_action)
        overview_layout.addWidget(actions_widget)
        
        self.tabs.addTab(overview_widget, "📊 النظرة العامة")
    
    def setup_users_tab(self):
        """إعداد تبويب إدارة المستخدمين"""
        self.users_widget = UserManagementWidget(
            self.connection_manager, self.auth_service
        )
        self.tabs.addTab(self.users_widget, "👥 إدارة المستخدمين")
    
    def setup_lookup_tab(self):
        """إعداد تبويب إدارة الجداول المساعدة"""
        self.lookup_widget = LookupManagementWidget(
            self.connection_manager, self.auth_service
        )
        self.tabs.addTab(self.lookup_widget, "🗂️ الجداول المساعدة")
    
    def setup_tools_tab(self):
        """إعداد تبويب الأدوات الإدارية"""
        tools_widget = QWidget()
        tools_layout = QVBoxLayout(tools_widget)
        tools_layout.setContentsMargins(20, 20, 20, 20)
        tools_layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("🔧 الأدوات الإدارية")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        tools_layout.addWidget(title_label)
        
        # مجموعة أدوات قاعدة البيانات
        db_group = QGroupBox("أدوات قاعدة البيانات")
        db_layout = QVBoxLayout(db_group)
        
        db_buttons = [
            ("💾 إنشاء نسخة احتياطية", "#3498db", "backup_db"),
            ("📥 استعادة نسخة احتياطية", "#e67e22", "restore_db"),
            ("🧹 تنظيف قاعدة البيانات", "#f39c12", "cleanup_db"),
            ("📊 تحليل الأداء", "#9b59b6", "analyze_performance")
        ]
        
        for text, color, action in db_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px;
                    font-weight: bold;
                    text-align: left;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(lambda checked, a=action: self.handle_tool_action(a))
            db_layout.addWidget(btn)
        
        tools_layout.addWidget(db_group)
        
        # مجموعة أدوات النظام
        system_group = QGroupBox("أدوات النظام")
        system_layout = QVBoxLayout(system_group)
        
        system_buttons = [
            ("📋 عرض سجلات النظام", "#1abc9c", "view_logs"),
            ("📈 تقرير الاستخدام", "#27ae60", "usage_report"),
            ("⚙️ إعدادات النظام", "#34495e", "system_settings"),
            ("🔄 إعادة تشغيل الخدمات", "#e74c3c", "restart_services")
        ]
        
        for text, color, action in system_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 12px;
                    font-weight: bold;
                    text-align: left;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(lambda checked, a=action: self.handle_tool_action(a))
            system_layout.addWidget(btn)
        
        tools_layout.addWidget(system_group)
        
        tools_layout.addStretch()
        
        self.tabs.addTab(tools_widget, "🔧 الأدوات الإدارية")
    
    def handle_quick_action(self, action_id: str):
        """معالجة الإجراءات السريعة"""
        try:
            if action_id == 'add_user':
                # التبديل إلى تبويب المستخدمين وفتح نافذة الإضافة
                self.tabs.setCurrentIndex(1)  # تبويب المستخدمين
                if hasattr(self.users_widget, 'add_user'):
                    self.users_widget.add_user()
            
            elif action_id == 'backup_db':
                self.handle_tool_action('backup_db')
            
            elif action_id == 'system_logs':
                self.handle_tool_action('view_logs')
            
            elif action_id == 'export_data':
                self.handle_tool_action('export_data')
            
            elif action_id == 'system_settings':
                self.handle_tool_action('system_settings')
            
            elif action_id == 'user_activity':
                self.handle_tool_action('user_activity')
            
            else:
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.information(self, "معلومات", f"الإجراء '{action_id}' قيد التطوير")
                
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الإجراء السريع {action_id}: {str(e)}")
    
    def handle_tool_action(self, action: str):
        """معالجة إجراءات الأدوات"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            
            # معظم الأدوات قيد التطوير
            tools_messages = {
                'backup_db': 'ميزة النسخ الاحتياطي قيد التطوير',
                'restore_db': 'ميزة استعادة النسخة الاحتياطية قيد التطوير',
                'cleanup_db': 'ميزة تنظيف قاعدة البيانات قيد التطوير',
                'analyze_performance': 'ميزة تحليل الأداء قيد التطوير',
                'view_logs': 'ميزة عرض السجلات قيد التطوير',
                'usage_report': 'ميزة تقرير الاستخدام قيد التطوير',
                'system_settings': 'ميزة إعدادات النظام قيد التطوير',
                'restart_services': 'ميزة إعادة تشغيل الخدمات قيد التطوير',
                'export_data': 'ميزة تصدير البيانات قيد التطوير',
                'user_activity': 'ميزة نشاط المستخدمين قيد التطوير'
            }
            
            message = tools_messages.get(action, f"الأداة '{action}' قيد التطوير")
            QMessageBox.information(self, "معلومات", message)
            
        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ أداة {action}: {str(e)}")
    
    def darken_color(self, color: str) -> str:
        """تغميق اللون"""
        color_map = {
            "#3498db": "#2980b9",
            "#e67e22": "#d35400",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085",
            "#27ae60": "#229954",
            "#34495e": "#2c3e50",
            "#e74c3c": "#c0392b"
        }
        return color_map.get(color, color)
    
    def refresh(self):
        """تحديث لوحة التحكم"""
        try:
            # تحديث التبويب النشط
            current_widget = self.tabs.currentWidget()
            if hasattr(current_widget, 'refresh'):
                current_widget.refresh()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث لوحة التحكم: {str(e)}")
