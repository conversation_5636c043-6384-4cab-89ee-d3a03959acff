# نظام متابعة المراسلات والمعاملات (IOTS)

نظام متكامل لمتابعة المراسلات والمعاملات (الوارد والصادر) مبني باستخدام Python و PyQt6.

## المميزات

- ✅ **نمط التشغيل المزدوج**: يعمل مع MySQL (متصل) أو SQLite (محلي)
- ✅ **واجهة عربية**: دعم كامل للغة العربية واتجاه RTL
- ✅ **نظام مصادقة آمن**: تشفير كلمات المرور ونظام صلاحيات
- ✅ **لوحة تحكم تفاعلية**: إحصائيات ومعلومات سريعة
- ✅ **إدارة المعاملات**: إضافة وتعديل ومتابعة المعاملات
- ✅ **نظام تقارير**: تقارير مفصلة وتصدير للبيانات
- ✅ **مزامنة ذكية**: مزامنة تلقائية بين النمط المحلي والخادم

## متطلبات النظام

- Python 3.8 أو أحدث
- Windows 10/11 (مُحسن للنظام)
- ذاكرة: 4 جيجابايت RAM على الأقل
- مساحة تخزين: 500 ميجابايت

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
# إذا كان لديك git
git clone <repository-url>
cd InOut

# أو قم بتحميل الملفات يدوياً
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. اختبار النظام
```bash
python test_system.py
```

### 4. إعداد قاعدة البيانات
```bash
python setup_database.py
```

### 5. تشغيل التطبيق
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin`

## هيكل المشروع

```
InOut/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المتطلبات
├── setup_database.py      # إعداد قاعدة البيانات
├── test_system.py         # اختبار النظام
├── config/
│   └── configuration.ini  # ملف التكوين
├── src/
│   ├── database/          # طبقة قاعدة البيانات
│   ├── ui/               # واجهة المستخدم
│   ├── services/         # الخدمات
│   ├── utils/            # الأدوات المساعدة
│   └── models/           # النماذج
├── assets/               # الموارد (CSS, JS, صور)
├── data/                 # قاعدة البيانات المحلية
├── logs/                 # ملفات السجلات
├── reports/              # التقارير المُصدرة
└── temp/                 # الملفات المؤقتة
```

## الاستخدام

### تسجيل الدخول
1. شغل التطبيق باستخدام `python main.py`
2. أدخل بيانات تسجيل الدخول
3. ستظهر لك النافذة الرئيسية

### لوحة التحكم
- عرض الإحصائيات السريعة
- المعاملات الحديثة
- الإجراءات السريعة

### إدارة المعاملات
- إضافة معاملة جديدة
- تعديل المعاملات الموجودة
- البحث والتصفية
- تتبع حالة المعاملات

### التقارير
- تقارير يومية/شهرية/سنوية
- تصدير إلى Excel/PDF
- إحصائيات مفصلة

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في تثبيت PyQt6
```bash
pip install --upgrade pip
pip install PyQt6
```

#### 2. خطأ في قاعدة البيانات
```bash
# احذف قاعدة البيانات وأعد إنشاءها
rm data/iots_local.db
python setup_database.py
```

#### 3. مشاكل الترميز العربي
- تأكد من أن النظام يدعم UTF-8
- تأكد من تثبيت خطوط عربية

#### 4. مشاكل الاتصال بـ MySQL
- تحقق من إعدادات الاتصال في `config/configuration.ini`
- تأكد من تشغيل خادم MySQL
- النظام سيعمل تلقائياً في النمط المحلي إذا فشل الاتصال

### ملفات السجلات
- السجلات محفوظة في: `logs/application.log`
- يمكن تغيير مستوى السجلات من ملف التكوين

## التطوير

### إضافة ميزات جديدة
1. أنشئ فرع جديد للتطوير
2. اتبع هيكل المشروع الموجود
3. أضف الاختبارات المناسبة
4. حدث التوثيق

### هيكل قاعدة البيانات
- راجع `src/database/mysql_schema.sql` للهيكل الكامل
- راجع `src/database/sqlite_schema.sql` للنسخة المحلية

## الدعم

### الحصول على المساعدة
- راجع ملفات السجلات في `logs/`
- شغل `python test_system.py` للتشخيص
- تحقق من ملف التكوين `config/configuration.ini`

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نسخة Python المستخدمة
- نظام التشغيل
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## الترخيص

هذا المشروع مطور بواسطة Augment Agent لأغراض تعليمية وتجارية.

## التحديثات

### الإصدار 1.0.0 (الحالي)
- النظام الأساسي مع جميع الميزات الأساسية
- دعم MySQL و SQLite
- واجهة عربية كاملة
- نظام مصادقة وصلاحيات

### الإصدار 1.1.0 (الجديد)
- ✅ **لوحة تحكم متقدمة**: تبويبات متعددة مع إحصائيات شاملة
- ✅ **رسوم بيانية تفاعلية**: رسوم دائرية وعمودية وخطية
- ✅ **إحصائيات تفصيلية**: إحصائيات الباحثين وأنواع التأشيرات
- ✅ **مؤشرات أداء**: مقاييس الأداء والكفاءة
- ✅ **رسوم متحركة**: تأثيرات بصرية وانتقالات سلسة

### التحديثات المخططة
- نظام الإشعارات المتقدم
- تقارير أكثر تفصيلاً
- واجهة ويب اختيارية
- تطبيق موبايل

---

**ملاحظة**: هذا النظام في مرحلة التطوير النشط. بعض الميزات قد تكون قيد التطوير.
