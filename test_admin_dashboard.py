#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار لوحة تحكم المدير
Admin Dashboard Test

يختبر جميع وظائف لوحة تحكم المدير
"""

import sys
import os
from datetime import datetime, date, timedelta

# إضافة مسار src
sys.path.insert(0, 'src')

def test_admin_service():
    """اختبار خدمة الإدارة"""
    
    print("اختبار خدمة الإدارة...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.admin_service import AdminService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        admin_service = AdminService(conn_manager)
        
        # اختبار الحصول على المستخدمين مع الإحصائيات
        users = admin_service.get_all_users_with_stats()
        print(f"✅ المستخدمون مع الإحصائيات: {len(users)} مستخدم")
        
        # اختبار إحصائيات النظام
        system_stats = admin_service.get_system_statistics()
        print(f"✅ إحصائيات النظام: {len(system_stats)} إحصائية")
        
        # اختبار الصلاحيات المتاحة
        permissions = admin_service.get_available_permissions()
        print(f"✅ الصلاحيات المتاحة: {len(permissions)} صلاحية")
        
        # اختبار بيانات الجداول المساعدة
        lookup_tables = ['visa_types', 'received_from_sources', 'actions_taken', 'request_statuses']
        for table in lookup_tables:
            data = admin_service.get_lookup_table_data(table)
            print(f"✅ جدول {table}: {len(data)} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خدمة الإدارة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_user_management():
    """اختبار إدارة المستخدمين"""
    
    print("\nاختبار إدارة المستخدمين...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.admin_service import AdminService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        admin_service = AdminService(conn_manager)
        
        # اختبار إنشاء مستخدم تجريبي
        test_user_data = {
            'user_name': f'test_user_{int(datetime.now().timestamp())}',
            'user_pass': 'test123456',
            'full_name': 'مستخدم تجريبي',
            'email': f'test_{int(datetime.now().timestamp())}@example.com',
            'phone': '1234567890',
            'permission': 'user'
        }
        
        success, message = admin_service.create_user(test_user_data)
        if success:
            print(f"✅ إنشاء مستخدم تجريبي: {message}")
            
            # البحث عن المستخدم المنشأ
            from database.user_repository import UserRepository
            user_repo = UserRepository(conn_manager)
            created_user = user_repo.find_by_username(test_user_data['user_name'])
            
            if created_user:
                user_id = created_user['user_id']
                
                # اختبار تحديث المستخدم
                update_data = {
                    'full_name': 'مستخدم تجريبي محدث',
                    'phone': '0987654321'
                }
                
                success, message = admin_service.update_user(user_id, update_data)
                if success:
                    print(f"✅ تحديث المستخدم: {message}")
                else:
                    print(f"❌ فشل تحديث المستخدم: {message}")
                
                # اختبار تبديل حالة المستخدم
                success, message = admin_service.toggle_user_status(user_id)
                if success:
                    print(f"✅ تبديل حالة المستخدم: {message}")
                else:
                    print(f"❌ فشل تبديل حالة المستخدم: {message}")
                
                # اختبار إعادة تعيين كلمة المرور
                success, message = admin_service.reset_user_password(user_id, 'newpassword123')
                if success:
                    print(f"✅ إعادة تعيين كلمة المرور: {message}")
                else:
                    print(f"❌ فشل إعادة تعيين كلمة المرور: {message}")
                
                # اختبار إحصائيات المستخدم
                user_stats = admin_service.get_user_statistics(user_id)
                print(f"✅ إحصائيات المستخدم: {len(user_stats)} إحصائية")
                
                # حذف المستخدم التجريبي
                try:
                    user_repo.delete(user_id)
                    print("✅ تم حذف المستخدم التجريبي")
                except:
                    print("⚠️ لم يتم حذف المستخدم التجريبي")
            
        else:
            print(f"❌ فشل إنشاء مستخدم تجريبي: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المستخدمين: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_lookup_management():
    """اختبار إدارة الجداول المساعدة"""
    
    print("\nاختبار إدارة الجداول المساعدة...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.admin_service import AdminService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        admin_service = AdminService(conn_manager)
        
        # اختبار إضافة عنصر تجريبي لجدول أنواع التأشيرات
        test_visa_type = {
            'visa_type': f'تأشيرة تجريبية {int(datetime.now().timestamp())}',
            'description': 'وصف تجريبي',
            'is_active': 1
        }
        
        success, message = admin_service.add_lookup_item('visa_types', test_visa_type)
        if success:
            print(f"✅ إضافة نوع تأشيرة تجريبي: {message}")
            
            # البحث عن العنصر المضاف
            visa_types = admin_service.get_lookup_table_data('visa_types')
            added_item = None
            for item in visa_types:
                if item.get('visa_type') == test_visa_type['visa_type']:
                    added_item = item
                    break
            
            if added_item:
                item_id = added_item['id']
                
                # اختبار تحديث العنصر
                update_data = {
                    'visa_type': f'تأشيرة تجريبية محدثة {int(datetime.now().timestamp())}',
                    'description': 'وصف محدث'
                }
                
                success, message = admin_service.update_lookup_item('visa_types', item_id, update_data)
                if success:
                    print(f"✅ تحديث نوع التأشيرة: {message}")
                else:
                    print(f"❌ فشل تحديث نوع التأشيرة: {message}")
                
                # اختبار حذف العنصر
                success, message = admin_service.delete_lookup_item('visa_types', item_id)
                if success:
                    print(f"✅ حذف نوع التأشيرة: {message}")
                else:
                    print(f"❌ فشل حذف نوع التأشيرة: {message}")
        else:
            print(f"❌ فشل إضافة نوع تأشيرة تجريبي: {message}")
        
        # اختبار جداول أخرى
        test_cases = [
            ('received_from_sources', {
                'received_from': f'مصدر تجريبي {int(datetime.now().timestamp())}',
                'contact_info': 'معلومات اتصال تجريبية',
                'is_active': 1
            }),
            ('actions_taken', {
                'action_taken': f'إجراء تجريبي {int(datetime.now().timestamp())}',
                'description': 'وصف إجراء تجريبي',
                'is_active': 1
            }),
            ('request_statuses', {
                'request_status': f'حالة تجريبية {int(datetime.now().timestamp())}',
                'status_color': '#ff5733',
                'is_active': 1
            })
        ]
        
        for table_name, test_data in test_cases:
            success, message = admin_service.add_lookup_item(table_name, test_data)
            if success:
                print(f"✅ إضافة عنصر لجدول {table_name}: نجح")
                
                # حذف العنصر التجريبي
                table_data = admin_service.get_lookup_table_data(table_name)
                main_field = list(test_data.keys())[0]
                for item in table_data:
                    if item.get(main_field) == test_data[main_field]:
                        admin_service.delete_lookup_item(table_name, item['id'])
                        break
            else:
                print(f"❌ فشل إضافة عنصر لجدول {table_name}: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الجداول المساعدة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_system_statistics():
    """اختبار إحصائيات النظام"""
    
    print("\nاختبار إحصائيات النظام...")
    
    try:
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.admin_service import AdminService
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        admin_service = AdminService(conn_manager)
        
        # الحصول على إحصائيات النظام
        stats = admin_service.get_system_statistics()
        
        # التحقق من وجود الإحصائيات المطلوبة
        required_stats = [
            'total_users', 'active_users', 'inactive_users',
            'total_transactions', 'completed_transactions', 'overdue_transactions',
            'visa_types_count', 'received_from_count', 'actions_count', 'statuses_count',
            'database_type'
        ]
        
        missing_stats = []
        for stat in required_stats:
            if stat not in stats:
                missing_stats.append(stat)
        
        if missing_stats:
            print(f"⚠️ إحصائيات مفقودة: {', '.join(missing_stats)}")
        else:
            print("✅ جميع الإحصائيات المطلوبة متوفرة")
        
        # عرض الإحصائيات
        print("\nالإحصائيات الحالية:")
        print(f"- إجمالي المستخدمين: {stats.get('total_users', 0)}")
        print(f"- المستخدمون النشطون: {stats.get('active_users', 0)}")
        print(f"- إجمالي المعاملات: {stats.get('total_transactions', 0)}")
        print(f"- المعاملات المكتملة: {stats.get('completed_transactions', 0)}")
        print(f"- المعاملات المتأخرة: {stats.get('overdue_transactions', 0)}")
        print(f"- نوع قاعدة البيانات: {stats.get('database_type', 'غير محدد')}")
        
        return len(missing_stats) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إحصائيات النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_admin_widgets():
    """اختبار واجهات المدير"""
    
    print("\nاختبار واجهات المدير...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from utils.config_manager import ConfigManager
        from database.connection_manager import ConnectionManager
        from services.auth_service import AuthService
        from ui.admin_dashboard_widget import AdminDashboardWidget
        from ui.user_management_widget import UserManagementWidget
        from ui.lookup_management_widget import LookupManagementWidget
        
        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # إعداد الاتصالات
        config = ConfigManager()
        conn_manager = ConnectionManager(config)
        auth_service = AuthService(conn_manager, config)
        
        # اختبار لوحة تحكم المدير
        admin_dashboard = AdminDashboardWidget(conn_manager, auth_service)
        print("✅ إنشاء لوحة تحكم المدير: نجح")
        
        # اختبار ويدجت إدارة المستخدمين
        user_management = UserManagementWidget(conn_manager, auth_service)
        print("✅ إنشاء ويدجت إدارة المستخدمين: نجح")
        
        # اختبار ويدجت إدارة الجداول المساعدة
        lookup_management = LookupManagementWidget(conn_manager, auth_service)
        print("✅ إنشاء ويدجت إدارة الجداول المساعدة: نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهات المدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_admin_dashboard_tests():
    """تشغيل جميع اختبارات لوحة تحكم المدير"""
    
    print("=" * 60)
    print("اختبار لوحة تحكم المدير")
    print("=" * 60)
    
    tests = [
        ("خدمة الإدارة", test_admin_service),
        ("إدارة المستخدمين", test_user_management),
        ("إدارة الجداول المساعدة", test_lookup_management),
        ("إحصائيات النظام", test_system_statistics),
        ("واجهات المدير", test_admin_widgets),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"تشغيل: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'=' * 60}")
    print(f"نتائج اختبار لوحة تحكم المدير: {passed_tests}/{total_tests} نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات لوحة تحكم المدير نجحت!")
        print("\nالميزات المتاحة:")
        print("- ✅ إدارة المستخدمين الكاملة")
        print("- ✅ إدارة الجداول المساعدة")
        print("- ✅ إحصائيات النظام المتقدمة")
        print("- ✅ الإجراءات السريعة")
        print("- ✅ الأدوات الإدارية")
        print("- ✅ واجهات تفاعلية متطورة")
        return True
    else:
        print("⚠️ بعض اختبارات لوحة تحكم المدير فشلت")
        return False

def main():
    """الدالة الرئيسية"""
    
    try:
        success = run_admin_dashboard_tests()
        
        if success:
            print(f"\n{'=' * 60}")
            print("🚀 لوحة تحكم المدير جاهزة للاستخدام!")
            print("\nلتجربة النظام:")
            print("1. شغل التطبيق: python main.py")
            print("2. سجل الدخول كمدير: admin / admin")
            print("3. انتقل إلى 'لوحة تحكم المدير'")
            print("4. استكشف التبويبات المختلفة:")
            print("   - النظرة العامة والإحصائيات")
            print("   - إدارة المستخدمين")
            print("   - إدارة الجداول المساعدة")
            print("   - الأدوات الإدارية")
            print(f"{'=' * 60}")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\nتم إيقاف الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\nخطأ غير متوقع: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
